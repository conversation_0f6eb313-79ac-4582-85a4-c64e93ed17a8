<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateOrdersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('orders', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('ID');
            $table->integer('user_id')->comment('用户id');
            $table->integer('shop_id')->comment('店铺id');
            $table->string('tid', 50)->comment('主订单');
            $table->tinyInteger('type')->default(0)->comment('订单类型');
            $table->string('express_no', 255)->nullable()->comment('快递单号');
            $table->integer('merge_pid')->default(0)->comment('地址合并父id');
            $table->integer('template_id')->default(0)->comment('打印模板id');
            $table->tinyInteger('is_merge')->default(0)->comment('是否合并订单');
            $table->integer('is_divide')->default(0)->comment('是否拆单');
            $table->integer('divide_pid')->default(0)->comment('拆弹之前的订单ID，取消合单用');
            $table->string('buyer_nick', 255)->nullable()->comment('买家昵称');
            $table->string('seller_nick', 50)->nullable()->comment('卖家昵称');
            $table->tinyInteger('order_status')->default(0)->comment('订单状态');
            $table->tinyInteger('refund_status')->default(0)->comment('退款状态');
            $table->tinyInteger('print_status')->default(0)->comment('打印状态');
            $table->string('shop_title', 50)->nullable()->comment('店铺名');
            $table->string('receiver_state', 50)->nullable()->comment('收货人省份');
            $table->string('receiver_city', 50)->nullable()->comment('收货人城市');
            $table->string('receiver_district', 50)->nullable()->comment('收货人地区');
            $table->string('receiver_town', 50)->nullable()->comment('收货人街道');
            $table->string('receiver_name', 255)->nullable()->comment('收货人名字');
            $table->string('receiver_phone', 255)->nullable()->comment('收货人手机');
            $table->integer('receiver_zip')->nullable()->comment('收件人邮编');
            $table->string('receiver_address', 255)->nullable()->comment('收货地址');
            $table->char('address_md5', 64)->nullable()->comment('收件人邮政编码');
            $table->decimal('payment', 10, 2)->default(0.00)->comment('实付金额');
            $table->decimal('total_fee', 10, 2)->default(0.00)->comment('总金额');
            $table->decimal('discount_fee', 10, 2)->default(0.00)->comment('优惠金额');
            $table->decimal('post_fee', 10, 2)->default(0.00)->comment('运费');
            $table->tinyInteger('seller_flag')->default(0)->comment('卖家旗帜');
            $table->text('seller_memo')->nullable()->comment('卖家备注');
            $table->string('buyer_message', 255)->nullable()->comment('买家留言');
            $table->tinyInteger('has_buyer_message')->default(0)->comment('是否有买家留言');
            $table->string('express_code', 255)->nullable()->comment('快递公司代码');
            $table->integer('num')->default(0)->comment('商品数量');
            $table->integer('sku_num')->default(0)->comment('SKU数量');
            $table->tinyInteger('is_pre_sale')->default(0)->comment('是否预售1是0否');
            $table->timestamp('promise_ship_at')->nullable()->comment('承诺发货时间');
            $table->timestamp('order_created_at')->nullable()->comment('订单创建时间');
            $table->timestamp('order_updated_at')->nullable()->comment('订单修改时间');
            $table->timestamp('send_at')->nullable()->comment('发货时间');
            $table->timestamp('finished_at')->nullable()->comment('订单完成时间');
            $table->timestamp('groupon_at')->nullable()->comment('成团时间');
            $table->timestamp('locked_at')->nullable()->comment('锁定时间');
            $table->timestamp('recycled_at')->nullable()->comment('回收时间');
            $table->timestamp('pay_at')->nullable()->comment('支付时间');
            $table->timestamps();
            $table->softDeletes();

            $table->unique('tid');
            $table->index('address_md5');
            $table->index(['user_id', 'shop_id', 'order_created_at']);
            $table->index(['user_id', 'shop_id', 'created_at']);
            $table->comment = '订单表';
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('Orders');
    }
}
