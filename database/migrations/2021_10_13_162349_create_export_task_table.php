<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateExportTaskTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('export_task', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('user_id')->comment('user_id');
            $table->integer('shop_id')->comment('shop_id');
            $table->tinyInteger('type')->comment('导出类型');
            $table->string('condition', 255)->nullable()->comment('查询条件(json数组)');
            $table->tinyInteger('status')->default(0)->comment('任务状态：0待执行 1执行中 2已完成');
            $table->string('file_path', 255)->nullable()->comment('文件路径');
            $table->string('url', 100)->nullable()->comment('前端请求url');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('export_task');
    }
}
