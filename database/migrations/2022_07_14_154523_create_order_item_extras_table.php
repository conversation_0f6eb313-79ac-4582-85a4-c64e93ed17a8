<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateOrderItemExtrasTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('order_item_extras', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('order_item_id');
//            $table->string('quality_goods_sn')->default('')->comment('质检商品单编号');
            $table->string('quality_order_code')->default('')->comment('质检订单码');
            // 0 不需要质检，10 未检测，15 检测部分通过，20 检测通过，25 检测部分失败，30 检测失败
            $table->tinyInteger('quality_status')->default(0)->comment('质检状态：0 不需要质检');

            $table->timestamps();
            $table->softDeletes();

            $table->unique(['order_item_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('order_item_extras');
    }
}
