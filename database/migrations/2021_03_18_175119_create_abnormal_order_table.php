<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateAbnormalOrderTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('abnormal_order', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('ID');
            $table->integer('shop_id')->comment('店铺id');
            $table->tinyInteger('type')->default(0)->comment('1地址变更 2退款 3备注 4地址异常');
            $table->tinyInteger('status')->default(0)->comment('0未读 1已读');
            $table->bigInteger('order_id')->default(0)->comment('主订单主键');
            $table->string('desc', 100)->comment('异常原因');
            $table->text('extra')->nullable()->comment('扩展字段');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('abnormal_order');
    }
}
