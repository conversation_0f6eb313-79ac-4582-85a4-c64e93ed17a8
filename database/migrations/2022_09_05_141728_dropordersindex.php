<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class Dropordersindex extends Migration
{

    private $idxShopIdSendAt = 'idx_shopid_sendat';
    private $idxPayat = 'idx_payat';

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropIndex("orders_shop_id_send_at_pay_at_index");
//            $table->index(['shop_id', 'send_at'], $this->idxShopIdSendAt);
//            $table->index(['pay_at'], $this->idxPayat);

        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('orders', function (Blueprint $table) {
//            $table->dropIndex($this->idxShopIdSendAt);
//            $table->dropIndex($this->idxPayat);
        });
    }
}
