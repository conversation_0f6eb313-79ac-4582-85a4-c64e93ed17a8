<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateTemplatesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('templates', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('授权记录 ID');
            $table->integer('user_id')->comment('用户id');
            $table->integer('shop_id')->comment('shop_id');
            $table->integer('company_id')->comment('快递公司id');
            $table->tinyInteger('auth_source')->default(1)->comment('授权来源');
            $table->string('owner_id', 255)->nullable()->comment('owner_id');
            $table->string('owner_name', 255)->nullable()->comment('owner_name');
            $table->tinyInteger('type')->default(1)->comment('面单类型 1=拼多多电子面单');
            $table->tinyInteger('style')->default(1)->comment('模板样式 1=拼多多标准模板，3=拼多多一联单模板');
            $table->string('template_url', 255)->comment('模板样式对应的官方模板url');
            $table->string('name', 128)->comment('模板名称');
            $table->string('description', 128)->nullable()->comment('描述');
            $table->string('default_print', 255)->nullable()->comment('默认打印机');
            $table->decimal('width', 5,1)->comment('宽');
            $table->decimal('height', 5,1)->comment('高');
            $table->decimal('picture_height', 5,1)->comment('图/高');
            $table->decimal('picture_width', 5,1)->comment('图/宽');
            $table->string('picture', 255)->nullable()->comment('面单封面图链接');
            $table->string('waybill_type', 100)->nullable()->comment('面单类型');
            $table->string('merge_template_url', 255)->nullable()->comment('模板链接');
            $table->integer('parent_template_id')->default(0)->comment('延伸父级ID');
            $table->integer('time_delivery')->nullable()->comment('时效服务');
            $table->integer('insure')->nullable()->comment('保价服务');
            $table->string('wp_code', 64)->nullable()->comment('快递公司ID');
            $table->string('wp_name', 64)->nullable()->comment('快递公司名称');
            $table->text('custom_config')->nullable()->comment('商家自定义模板信息');
            $table->string('url', 255)->nullable()->comment('商家自定义模板url');
            $table->string('sender_name', 64)->nullable()->comment('寄件人');
            $table->string('sender_mobile', 64)->nullable()->comment('寄件人手机号');
            $table->string('sender_province', 32)->nullable()->comment('省');
            $table->string('sender_city', 32)->nullable()->comment('市');
            $table->string('sender_district', 32)->nullable()->comment('区');
            $table->string('sender_address', 32)->nullable()->comment('详细地址');
            $table->integer('shipping_address_id')->default(0)->comment('发货地址id');
            $table->tinyInteger('default')->default(1)->comment('是否默认 1=不，2=是');
            $table->timestamps();
            $table->softDeletes();
            $table->index(['user_id', 'shop_id', 'auth_source']);
            $table->comment = '电子面单模板表';
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('templates');
    }
}
