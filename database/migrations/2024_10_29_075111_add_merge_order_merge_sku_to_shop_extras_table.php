<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddMergeOrderMergeSkuToShopExtrasTable extends Migration
{
    public function up(): void
    {
        Schema::table('shop_extras', function (Blueprint $table) {
            $table->tinyInteger('merge_order_merge_sku')->default(1)->comment('合并订单SKU')->after('merge_order_num');
        });
    }

    public function down(): void
    {
        Schema::table('shop_extras', function (Blueprint $table) {
            //
        });
    }
}
