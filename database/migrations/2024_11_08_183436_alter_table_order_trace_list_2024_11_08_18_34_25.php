<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AlterTableOrderTraceList20241108183425 extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('order_trace_list', function (Blueprint $table) {
            $table->index(['status','latest_updated_at','updated_at'],'idx_status_lastestupdatedat_updatedat');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('order_trace_list', function (Blueprint $table) {
            //
        });
    }
}
