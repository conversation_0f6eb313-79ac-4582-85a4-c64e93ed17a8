<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddRoleTypeToShopTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('shops', function (Blueprint $table) {
            $table->tinyInteger('role_type')->default(1)->comment('店铺角色类型 0未知 1商家 2厂家')->after('type');
            $table->timestamp('last_factory_sync_at')->nullable()->comment('最后厂商同步时间')->after('last_operated_at');
            $table->timestamp('last_factory_operated_at')->nullable()->comment('最后厂商手动同步时间')->after('last_factory_sync_at');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('shops', function (Blueprint $table) {
            $table->dropColumn('role_type');
            $table->dropColumn('last_factory_sync_at');
            $table->dropColumn('last_factory_operated_at');
        });
    }
}
