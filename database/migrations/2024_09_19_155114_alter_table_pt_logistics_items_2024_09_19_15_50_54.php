<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AlterTablePtLogisticsItems20240919155054 extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('pt_logistics_items', function (Blueprint $table) {
            $table->string('sku_uuid')->nullable()->after('sku_id')->comment("商品SKU UUID");
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('pt_logistics_items', function (Blueprint $table) {
            //
        });
    }
}
