<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddMultiPackageMainToPackagesTable extends Migration
{
    public function up(): void
    {
        Schema::table('packages', function (Blueprint $table) {
            $table->string('multi_package_main_waybill_code',32)->default('')->comment('一单多包裹主单号');

            $table->index('multi_package_main_waybill_code');
        });
    }

    public function down(): void
    {
        Schema::table('packages', function (Blueprint $table) {
            //
        });
    }
}
