<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class Romoveuslessorderidx extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        try {
            Schema::table('orders', function (Blueprint $table) {
                $table->dropIndex('idx_payat');

            });
        }catch (Exception $ex){

        }
        try {
            Schema::table('orders', function (Blueprint $table) {
                $table->dropIndex('idx_shopid_sendat');

            });
        }catch (Exception $ex){

        }


    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('orders', function (Blueprint $table) {
            //
        });
    }
}
