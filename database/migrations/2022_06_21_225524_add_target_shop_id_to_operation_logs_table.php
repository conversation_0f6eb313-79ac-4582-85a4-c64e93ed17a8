<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddTargetShopIdToOperationLogsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('operation_logs', function (Blueprint $table) {
            $table->integer('target_shop_id')->default(0)->comment('目标店铺id')->after('append_info');

            $table->index(['target_shop_id','time']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('operation_logs', function (Blueprint $table) {
            $table->dropColumn('target_shop_id');
            $table->dropIndex(['target_shop_id','time']);
        });
    }
}
