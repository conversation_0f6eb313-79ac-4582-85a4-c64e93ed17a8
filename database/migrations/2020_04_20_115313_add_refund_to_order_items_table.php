<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddRefundToOrderItemsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('order_items', function (Blueprint $table) {
            $table->tinyInteger('refund_status')->default(0)->comment('退款状态')->after('refund_id');
            $table->timestamp('refund_created_at')->nullable()->comment('退款创建时间')->after('refund_status');
            $table->timestamp('refund_updated_at')->nullable()->comment('退款修改时间')->after('refund_created_at');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('order_items', function (Blueprint $table) {
            $table->dropColumn('refund_status');
            $table->dropColumn('refund_created_at');
            $table->dropColumn('refund_updated_at');
        });
    }
}
