<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AlterTableWaybillHistories20240305153135 extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('waybill_histories', function (Blueprint $table) {
            $table->string('created_by', 64)->nullable()->comment('创建人');
            $table->string('updated_by', 64)->nullable()->comment('更新人');
        });
        Schema::table('print_records', function (Blueprint $table) {
            $table->string('created_by', 64)->nullable()->comment('创建人');
            $table->string('updated_by', 64)->nullable()->comment('更新人');
        });
    }


    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('waybill_histories', function (Blueprint $table) {
            //
        });
    }
}
