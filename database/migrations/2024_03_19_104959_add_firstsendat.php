<?php

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddFirstsendat extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('order_items', function (Blueprint $table) {
            $table->dateTime('first_send_at')->comment('首次发货时间')->after('send_at')->nullable();
            $table->dateTime('first_send_waybill_code')->comment('首次发货运单号')->after('first_send_at')->nullable();
            $table->index( ['shop_id', 'first_send_at'], 'idx_shopid_firstsendat');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('order_items', function (Blueprint $table) {
            //
        });
    }
}
