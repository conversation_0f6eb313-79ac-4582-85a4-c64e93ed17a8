<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddIsCrossShopMergeOrderToShopExtrasTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('shop_extras', function (Blueprint $table) {
            $table->tinyInteger('is_cross_shop_merge_order')->default(0)->comment('是否跨店铺合并订单')->after('merge_order_num');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('shop_extras', function (Blueprint $table) {
            $table->dropColumn('is_cross_shop_merge_order');
        });
    }
}
