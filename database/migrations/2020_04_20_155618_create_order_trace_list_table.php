<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateOrderTraceListTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('order_trace_list', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->integer('user_id')->comment('用户id');
            $table->integer('shop_id')->comment('店铺id');
            $table->tinyInteger('type')->comment('平台类型');
            $table->string('tid', 50)->nullable()->comment('订单号');
            $table->string('express_code', 50)->nullable()->comment('快递公司code');
            $table->string('express_no', 50)->nullable()->comment('快递号');
            $table->tinyInteger('status')->default(0)->comment('物流状态(action对应)');
            $table->string('action')->nullable()->comment('节点说明 ，指明当前节点揽收、派送，签收等');
            $table->string('receiver_province', 50)->nullable()->comment('收货人省份');
            $table->string('receiver_name', 50)->nullable()->comment('收货人名字');
            $table->timestamp('send_at')->nullable()->comment('发货时间');
            $table->timestamp('latest_updated_at')->nullable()->comment('最新轨迹更新时间');
            $table->string('latest_trace')->nullable()->comment('最新轨迹');
            $table->text('trace_list')->nullable()->comment('快件所有轨迹');
            $table->timestamps();
            $table->index(['user_id', 'shop_id', 'status']);
            $table->index('express_no');
            $table->index('tid');
            $table->index('type');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('order_trace_list');
    }
}
