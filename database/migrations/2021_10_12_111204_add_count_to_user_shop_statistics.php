<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddCountToUserShopStatistics extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('user_shop_statistics', function (Blueprint $table) {
            $table->integer('real_print_count')->comment('打印数量统计')->after('print_count');
            $table->integer('real_print_shop_count')->comment('打印店铺数量统计')->after('print_shop_count');
            $table->integer('delivery_shop_count')->comment('发货店铺统计')->after('print_shop_count');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('user_shop_statistics', function (Blueprint $table) {
            $table->dropColumn('real_print_count');
            $table->dropColumn('real_print_shop_count');
            $table->dropColumn('delivery_shop_count');
        });
    }
}
