<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class WaybillhistoryTshopid extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('waybill_histories', function (Blueprint $table) {
            $table->bigInteger('to_shop_id')->nullable()->comment('给哪个店铺取号');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('waybill_histories', function (Blueprint $table) {
            $table->dropColumn('to_shop_id');
        });
    }
}
