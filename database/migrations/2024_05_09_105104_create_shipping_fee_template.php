<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateShippingFeeTemplate extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('shipping_fee_templates', function (Blueprint $table) {
            $table->bigincrements('id');
            $table->bigInteger('shop_id');
            $table->string('wp_code',50)->comment('快递公司编码，多个快递公司之间用,分隔');
            $table->decimal('start_standard')->nullable()->comment('首重');
            $table->decimal('start_fee')->nullable()->commemnt('首重运费');
            $table->decimal('add_standard')->nullable()->commemnt('每增加重量');
            $table->decimal('add_fee')->nullable()->commemnt('每增加运费');
            $table->text('extra_setting')->nullable()->comment('加价区域');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('shipping_fee_template');
    }
}
