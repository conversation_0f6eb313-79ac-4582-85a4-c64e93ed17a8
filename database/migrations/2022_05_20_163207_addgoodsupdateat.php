<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class Addgoodsupdateat extends Migration
{
    private  $idxUpdatedAt = 'idx_shopid_updatedat';
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('goods', function (Blueprint $table) {
            $table->index(['shop_id','updated_at'], $this->idxUpdatedAt);
        });
        Schema::table('goods_skus', function (Blueprint $table) {
            $table->index(['shop_id','updated_at'], $this->idxUpdatedAt);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('goods', function (Blueprint $table) {
            $table->dropIndex($this->idxUpdatedAt);
        });
        Schema::table('goods_skus', function (Blueprint $table) {
            $table->dropIndex($this->idxUpdatedAt);
        });
    }
}
