<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddIncludeToQueryArea extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('query_areas', function (Blueprint $table) {
            $table->tinyInteger('include')->default(1)->comment('是否包含 1包含;2排除');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('query_areas', function (Blueprint $table) {
            $table->dropColumn('include');
        });
    }
}
