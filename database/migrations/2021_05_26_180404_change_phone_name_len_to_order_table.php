<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class ChangePhoneNameLenToOrderTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->string('receiver_name',30)->nullable()->comment('收货人名字')->change();
            $table->string('receiver_phone',100)->nullable()->comment('收货人手机')->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->string('receiver_name', 50)->nullable()->comment('收货人名字')->change();
            $table->string('receiver_phone', 11)->nullable()->comment('收货人手机')->change();
        });
    }
}
