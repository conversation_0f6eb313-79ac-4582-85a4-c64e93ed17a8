<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddAuthappid extends Migration
{
    private  $idxAuthToAppId = 'idx_authtoappid';
    private  $idxAppId = 'idx_appid';
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('api_auth', function (Blueprint $table) {
            $table->string('auth_to_app_id',50)->nullable()->default(null)->comment('授权appid');

            $table->index(['auth_to_app_id'], $this->idxAuthToAppId);

            $table->index(['app_id'], $this->idxAppId);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('api_auth', function (Blueprint $table) {
            $table->dropColumn('auth_to_app_id');
            $table->dropIndex($this->idxAuthToAppId);
            $table->dropIndex($this->idxAppId);
        });
    }
}
