<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class Dropwaybillhistoryindex extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('waybill_histories', function (Blueprint $table) {
            $table->dropIndex("idx_shopid_waybillcode_createdat");
            $idxShopIdCreatedatWithData = 'idx_shopid_createdat_waybill_code';
            $table->index(['shop_id','created_at','waybill_code'], $idxShopIdCreatedatWithData);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('waybill_histories', function (Blueprint $table) {
            //
        });
    }
}
