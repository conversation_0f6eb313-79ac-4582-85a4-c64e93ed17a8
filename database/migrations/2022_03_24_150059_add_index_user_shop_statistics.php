<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddIndexUserShopStatistics extends Migration
{
    private $idxPrintCount = 'idx_printcount';
    private $idxOrderCount = 'idx_ordercount';
    private $idxDeliveryCount = 'idx_deliverycount';

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('user_shop_statistics', function (Blueprint $table) {

            $table->index(['print_count'], $this->idxPrintCount);
            $table->index(['order_count'], $this->idxOrderCount);
            $table->index(['delivery_count'], $this->idxDeliveryCount);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('user_shop_statistics', function (Blueprint $table) {
            $table->dropIndex($this->idxOrderCount);
            $table->dropIndex($this->idxDeliveryCount);
            $table->dropIndex($this->idxPrintCount);
        });
    }
}
