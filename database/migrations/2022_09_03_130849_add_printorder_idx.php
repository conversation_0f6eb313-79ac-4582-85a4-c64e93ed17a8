<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddPrintorderIdx extends Migration
{
    private $idxShopIdWaybillCodeCreatedat = 'idx_shopid_waybillcode_createdat';
    private $idxShopIdBatchNoWaybillcodeCreatedat = 'idx_shopid_batchno_waybillcode_created';

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('waybill_histories', function (Blueprint $table) {
            $table->index(['shop_id', 'waybill_code', 'created_at'], $this->idxShopIdWaybillCodeCreatedat);
        });
        Schema::table('print_records', function (Blueprint $table) {
            $table->index(['shop_id', 'batch_no', 'waybill_code', 'created_at'], $this->idxShopIdBatchNoWaybillcodeCreatedat);
        });
        //
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('waybill_histories', function (Blueprint $table) {
            $table->dropIndex($this->idxShopIdWaybillCodeCreatedat);
        });
        Schema::table('print_records', function (Blueprint $table) {
            $table->dropIndex($this->idxShopIdBatchNoWaybillcodeCreatedat);
        });
        //
    }
}
