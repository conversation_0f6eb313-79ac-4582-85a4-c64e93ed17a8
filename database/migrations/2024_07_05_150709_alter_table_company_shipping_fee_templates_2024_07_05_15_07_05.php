<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AlterTableCompanyShippingFeeTemplates20240705150705 extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('company_shipping_fee_templates', function (Blueprint $table) {
            $table->tinyInteger('auth_source')->comment('授权来源');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('company_shipping_fee_templates', function (Blueprint $table) {
            //
        });
    }
}
