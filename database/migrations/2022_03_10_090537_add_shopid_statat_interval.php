<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddShopidStatatInterval extends Migration
{
    private $idxShopIdStatatInterval = 'idx_shopid_statat_interval';

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('user_shop_statistics', function (Blueprint $table) {

            $table->index(['shop_id', 'stat_at','interval'], $this->idxShopIdStatatInterval);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('user_shop_statistics', function (Blueprint $table) {
            $table->dropIndex($this->idxShopIdStatatInterval);
        });
    }
}
