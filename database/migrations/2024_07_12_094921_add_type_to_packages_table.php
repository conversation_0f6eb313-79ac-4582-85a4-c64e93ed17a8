<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddTypeToPackagesTable extends Migration
{
    public function up(): void
    {
        Schema::table('packages', function (Blueprint $table) {
            $table->tinyInteger('source_type')->default(0)->comment('来源类型 0:取号,1:内部发货');
            $table->tinyInteger('delivery_type')->default(0)->comment('发货类型 0:无,1:首次发货,2:变更单号,3:补发,4:换货,99:其他');
            $table->bigInteger('old_pt_logistics_id')->default(0)->comment('旧平台包裹id');
        });
    }

    public function down(): void
    {
        Schema::table('packages', function (Blueprint $table) {
            //
        });
    }
}
