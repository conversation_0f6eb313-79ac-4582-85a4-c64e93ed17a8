<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateOperationLogsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {

        Schema::create('operation_logs', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('user_id');
            $table->integer('shop_id');
            $table->integer('order_id')->default(0)->comment('订单ID');
            $table->string('tid',50)->default('')->comment('订单号');
            $table->integer('package_id')->default(0)->comment('包裹ID');
            $table->string('waybill_code',50)->default('')->comment('电子面单号');
            $table->string('wp_code',50)->default('')->comment('物流公司编码');
            $table->string('content')->default('')->comment('操作内容');
            $table->integer('type')->comment('操作类型');
            $table->string('remark',1024)->default('')->comment('备注');
            $table->string('operator_ip')->default('')->comment('操作人IP');
            $table->string('operator_name')->default('')->comment('操作人名');
            $table->string('append_info')->default('')->comment('附加信息');
            $table->timestamp('time')->nullable()->comment('操作时间');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('operation_logs');
    }
}
