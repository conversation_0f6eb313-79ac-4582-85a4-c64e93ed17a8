<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateOrderItemsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('order_items', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('ID');
            $table->integer('user_id')->comment('用户id');
            $table->integer('shop_id')->comment('店铺id');
            $table->bigInteger('order_id')->default(0)->comment('主订单主键');
            $table->string('tid', 50)->comment('主订单');
            $table->string('oid', 50)->comment('子订单号');
            $table->tinyInteger('type')->default(0)->comment('订单类型');
            $table->decimal('payment', 10, 2)->default(0.00)->comment('实付金额');
            $table->decimal('total_fee', 10, 2)->default(0.00)->comment('总金额');
            $table->decimal('discount_fee', 10, 2)->default(0.00)->comment('优惠金额');
            $table->decimal('goods_price', 10, 2)->default(0.00)->comment('商品单价');
            $table->string('goods_pic', 255)->nullable()->comment('商品图片');
            $table->string('goods_title', 255)->nullable()->comment('商品标题');
            $table->integer('goods_num')->default(0)->comment('商品数量');
            $table->string('num_iid', 20)->nullable()->comment('商品id');
            $table->string('sku_id', 255)->nullable()->comment('SKU id');
            $table->string('sku_value', 255)->default(0)->comment('SKU的值。如：手机套餐:官方标配');
            $table->integer('refund_id')->default(0)->comment('退款id');
            $table->timestamp('order_created_at')->nullable()->comment('订单创建时间');
            $table->timestamp('order_updated_at')->nullable()->comment('订单修改时间');
            $table->timestamps();
            $table->softDeletes();
            $table->unique('oid');
            $table->index(['user_id', 'shop_id', 'created_at']);
            $table->index('tid');
            $table->comment = '订单子项表';
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('order_items');
    }
}
