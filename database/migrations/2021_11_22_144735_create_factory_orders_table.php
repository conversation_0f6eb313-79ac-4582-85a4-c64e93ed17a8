<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateFactoryOrdersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('factory_orders', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->integer('shop_id')->comment('厂家店铺id');
            $table->string('distr_tid', 60)->comment('代打订单号');
            $table->string('distr_oid', 60)->comment('子订单号');
            $table->integer('distr_shop_id')->length(60)->comment('代打店铺id');
            $table->integer('distr_platform_shop_id')->length(60)->comment('代打店铺平台id');
            $table->string('distr_shop_name', 60)->comment('代打店铺名');
            $table->string('waybill_code', 20)->default('')->comment('快递单号');
            $table->string('wp_code', 20)->default('')->comment('快递公司代码');
            $table->tinyInteger('distr_status')->default(0)->comment('分配状态');
            $table->tinyInteger('print_status')->default(0)->comment('打印状态');
            $table->tinyInteger('aftersale_status')->default(1)->comment('售后状态 1无售后 2有售后');
            $table->integer('print_num')->default(0)->comment('打印次数');
            $table->string('seller_memo', 1024)->default('')->comment('卖家备注');
            $table->string('buyer_message', 1024)->default('')->comment('买家留言');
            $table->string('distr_cancel_reason')->default('')->comment('代发取消原因');
            $table->string('receiver_state', 50)->default('')->comment('收货人省份');
            $table->string('receiver_city', 50)->default('')->comment('收货人城市');
            $table->string('receiver_district', 50)->default('')->comment('收货人地区');
            $table->string('receiver_town', 50)->default('')->comment('收货人街道');
            $table->string('receiver_id', 50)->default('')->comment('收件人 id');
            $table->string('goods_title')->comment('商品标题');
            $table->integer('goods_num')->comment('商品数量');
            $table->decimal('goods_price')->comment('商品单价');
            $table->decimal('goods_total_price')->comment('商品总价');
            $table->string('goods_id', 20)->default('')->comment('商品 id');
            $table->string('sku_id', 20)->default('')->comment('SKU id');
            $table->string('sku_value')->default('')->comment('SKU的值');
            $table->string('outer_goods_id')->default('')->comment('商家外部商品编码');
            $table->string('outer_sku_id')->default('')->comment('商家外部sku编码');
            $table->timestamp('print_shipping_at')->nullable()->comment('打印发货单');
            $table->timestamp('promise_ship_at')->nullable()->comment('承诺发货时间');
            $table->timestamp('print_at')->nullable()->comment('打印时间');
            $table->timestamp('distr_at')->nullable()->comment('分配时间');
            $table->timestamp('locked_at')->nullable()->comment('锁定时间');
            $table->timestamp('return_at')->nullable()->comment('回传时间');
            $table->timestamps();
            $table->softDeletes();

            $table->unique('distr_tid');
            $table->index(['shop_id', 'distr_status', 'distr_at']);
        });

//        Schema::create('factory_order_items', function (Blueprint $table) {
//            $table->increments('id');
//            $table->integer('order_id')->comment('订单id');
//            $table->string('dist_oid')->comment('子订单号');
//            $table->string('goods_title')->comment('商品标题');
//            $table->integer('goods_num')->comment('商品数量');
//            $table->decimal('goods_price')->comment('商品单价');
//            $table->string('goods_iid', 20)->default('')->comment('商品 id');
//            $table->string('sku_id', 20)->default('')->comment('SKU id');
//            $table->string('sku_value')->default('')->comment('SKU的值');
//            $table->string('outer_iid')->default('')->comment('商家外部商品编码');
//            $table->string('outer_sku_iid')->default('')->comment('商家外部sku编码');
//            $table->timestamps();
//            $table->softDeletes();
//
//            $table->index('order_id');
//            $table->unique('dist_oid');
//        });

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('factory_orders');
//        Schema::dropIfExists('factory_order_items');
    }
}
