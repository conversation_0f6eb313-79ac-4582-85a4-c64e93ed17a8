<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePtLogisticsTable extends Migration
{
    public function up(): void
    {
        Schema::create('pt_logistics', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->bigInteger('shop_id')->comment('店铺id');
            $table->bigInteger('order_id')->comment('订单id');
            $table->bigInteger('package_id')->default(0)->comment('包裹id');
            $table->tinyInteger('source_type')->default(0)->comment('来源类型 1:内部发货,2:外部发货');
            $table->string('waybill_code',50)->comment('运单号');
            $table->string('wp_code',50)->comment('快递公司编码');
            $table->string('delivery_at')->nullable()->comment('发货时间');
            $table->string('delivery_id')->nullable()->comment('平台包裹id');
            $table->timestamps();
            $table->softDeletes();

            $table->index('shop_id');
//            $table->index('order_id');
            $table->unique(['shop_id','waybill_code','wp_code']);
        });

    }

    public function down(): void
    {
        Schema::dropIfExists('pt_logistics');
    }
}
