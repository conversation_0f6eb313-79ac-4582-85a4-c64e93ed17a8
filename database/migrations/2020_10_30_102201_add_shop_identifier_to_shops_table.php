<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddShopIdentifierToShopsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
	    Schema::table('shops', function (Blueprint $table) {
		    $table->string('shop_identifier', 100)->nullable()->comment('多店铺唯一身份')->after('shop_name');
	    });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
	    Schema::table('shops', function (Blueprint $table) {
		    $table->dropColumn('shop_identifier');
	    });
    }
}
