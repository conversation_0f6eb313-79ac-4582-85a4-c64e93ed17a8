<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddSkuValue1SkuValue2ToOrderItemsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('order_items', function (Blueprint $table) {
            $table->string('sku_value1', 100)->default('')->comment('SKU值1')->after('sku_value');
            $table->string('sku_value2', 100)->default('')->comment('SKU值2')->after('sku_value1');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('order_items', function (Blueprint $table) {
            $table->dropColumn(['sku_value1', 'sku_value2']);
        });
    }
}
