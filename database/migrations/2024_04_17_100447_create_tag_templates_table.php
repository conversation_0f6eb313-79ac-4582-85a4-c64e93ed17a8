<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateTagTemplatesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('tag_templates', function (Blueprint $table) {
            $table->increments('id');
            $table->bigInteger('shop_id')->comment('店铺ID');
            $table->string('name')->comment('模板名称');
            $table->integer('horizontal')->default(0)->comment('水平');
            $table->integer('vertical')->default(0)->comment('垂直');
            $table->integer('width')->default(0)->comment('宽度');
            $table->integer('height')->default(0)->comment('高度');
            $table->tinyInteger('is_default')->default(0)->comment('是否默认');
            $table->string('default_printer')->comment('默认打印机');
            $table->text('custom_config')->comment('自定义配置');
            $table->timestamps();
            $table->softDeletes();

            $table->index('shop_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('tag_templates');
    }
}
