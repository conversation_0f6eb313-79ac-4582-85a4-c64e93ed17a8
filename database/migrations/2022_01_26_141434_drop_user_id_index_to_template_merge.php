<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class DropUserIdIndexToTemplateMerge extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('template_merges', function (Blueprint $table) {
            $table->dropIndex(['user_id','shop_id']);
            $table->index(['shop_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('template_merges', function (Blueprint $table) {
            $table->index(['user_id','shop_id']);
            $table->dropIndex(['shop_id']);
        });
    }
}
