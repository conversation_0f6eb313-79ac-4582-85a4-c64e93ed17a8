<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateGoodsSkusTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('goods_skus', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('goods_id')->comment('商品id');
            $table->integer('user_id')->comment('用户id');
            $table->integer('shop_id')->comment('店铺id');
            $table->tinyInteger('type')->default(0)->comment('订单类型');
            $table->string('sku_id', 64)->nullable()->comment('sku id');
            $table->string('sku_value', 255)->nullable()->comment('规格名称');
            $table->string('outer_id', 64)->nullable()->comment('商家外部编码（sku）');
            $table->string('outer_goods_id', 64)->nullable()->comment('商家外部编码（商品）');
            $table->string('custom_sku_value', 255)->nullable()->comment('自定义商品名称');
            $table->string('sku_pic', 255)->nullable()->comment('商品主图链接');
            $table->tinyInteger('is_onsale')->default(0)->comment('上下架状态');
            $table->timestamps();
            $table->softDeletes();
            $table->comment = '商品sku';
            $table->index(['user_id', 'shop_id', 'type']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('goods_skus');
    }
}
