<?php
/**
 * 微信视频号的增值服务
 */

use App\Constants\WaybillConst;

return [
    //SF把共有的跟视频号特有的产品类型合并到一起
    'SF' => array_merge(WaybillConst::SF_SHARE_SERVICE_LIST, [
        [
            'required' => false,
            'service_desc' => '产品类型',
            'service_name' => '产品类型',
            'service_code' => 'product_type',
            'service_attributes' => [
                [
                    'attribute_name' => '产品类型',
                    'attribute_type' => 'enum',
                    'attribute_code' => 'value',
                    'type_desc' => WaybillConst::SF_PRODUCT_TYPE

                ]
            ]
        ]]),

    //CNSD
    'CNSD' => [

        [
            'required' => false,
            'service_desc' => '标准保价',
            'service_name' => '标准保价',
            'service_code' => 'VA002',
            'service_attributes' => [
                [
                    'attribute_name' => '保价金额',
                    'attribute_type' => 'number',
                    'attribute_code' => 'value',
                    'type_desc' => '{"min":1,"max":1000000}'
                ]
            ]
        ],

    ],

    'DBKD' => [

        [
            'required' => false,
            'service_desc' => '保价',
            'service_name' => '保价',
            'service_code' => 'insuranceValue',
            'service_attributes' => [
                [
                    'attribute_name' => '保价金额',
                    'attribute_type' => 'number',
                    'attribute_code' => 'value',
                    'type_desc' => '{"min":1,"max":1000000}'
                ]
            ]
        ],
        [
            'required' => false,
            'service_desc' => '产品类型',
            'service_name' => '产品类型',
            'service_code' => 'product_type',
            'service_attributes' => [
                [
                    'attribute_name' => '产品类型',
                    'attribute_type' => 'enum',
                    'attribute_code' => 'value',
                    'type_desc' => '{"1":"大件快递3.60","2":"标准快递","3":"特快专递","4":"航空大件隔日达","5":"航空大件次日达","6":"重包入户","7":"精准卡航(新)","8":"精准重货(新)","9":"精准汽运(新)"}'
                ]
            ]
        ]

    ],

    'STO' => [

        [
            'required' => false,
            'service_desc' => '保价业务',
            'service_name' => '保价业务',
            'service_code' => 'INSURE_SERVICE',
            'service_attributes' => [
                [
                    'attribute_name' => '保价金额',
                    'attribute_type' => 'number',
                    'attribute_code' => 'value',
                    'type_desc' => '{"min":1,"max":1000000}'
                ]
            ]
        ],

    ],
    'JD' => [
        [
            'required' => false,
            'service_desc' => '普通保价-快递',
            'service_name' => '普通保价-快递',
            'service_code' => 'ed-a-0002',
            'service_attributes' => [
                [
                    'attribute_name' => '保价金额',
                    'attribute_type' => 'number',
                    'attribute_code' => 'value',
                    'type_desc' => '{"min":1,"max":1000000}'
                ]
            ]
        ],
        [
            'required' => false,
            'service_desc' => '温层信息',
            'service_name' => '温层信息',
            'service_code' => 'temperature_range',
            'service_attributes' => [
                [
                    'attribute_name' => '温层',
                    'attribute_type' => 'enum',
                    'attribute_code' => 'value',
                    'type_desc' => '{"1":"普通/常温","5":"鲜活","6":"控温","7":"冷藏","8":"冷冻","9":"深冷"}'
                ]
            ]
        ],
        [
            'required' => false,
            'service_desc' => '产品类型',
            'service_name' => '产品类型',
            'service_code' => 'product_type',
            'service_attributes' => [
                [
                    'attribute_name' => '产品类型',
                    'attribute_type' => 'enum',
                    'attribute_code' => 'value',
                    'type_desc' => '{"1":"京东标快","2":"京东快送","3":"生鲜标快","4":"生鲜特快","5":"电商特惠","6":"特惠包裹","7":"特惠小件","8":"函速达","11":"特快零担","12":"特快重货"}'
                ]
            ]
        ],

    ],
    "EMS" => [
        [
            'required' => false,
            'service_desc' => '产品类型',
            'service_name' => '产品类型',
            'service_code' => 'product_type',
            'service_attributes' => [
                [
                    'attribute_name' => '产品类型',
                    'attribute_type' => 'enum',
                    'attribute_code' => 'value',
                    'type_desc' => '{"1":"特快专递（EMS）","2":"快递包裹（youzhengguonei）","3":"邮政电商标快（yzdsbk）"}'
                ]
            ]
        ]
    ],
    //顺丰速运
    "shunfeng" => [

    ]


];
