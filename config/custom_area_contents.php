<?php

/**
 * 商家自定义区域 （暂用）
 */
return [
    [
        "id"        => 1,
        "aliasName" => "店铺名",
        "showName"  => "店铺名称",
        "value"     => "<%=data.mallName%>",
        "width"     => 100,
        "height"    => 20,
        "top"       => 0,
        "left"      => 0,
        "fontSize"  => 12,
        "fontFamily"=> "simsun",
        "fontWeight"=> "bold",
        "position"  => "absolute",
        "custom"    => false,
        "isMerge"   => false,
    ],
    [
        "id"        => 5,
        "aliasName" => "商家ID",
        "showName"  => "商家ID",
        "value"     => "<%=data.ownerName%>",
        "width"     => 80,
        "height"    => 20,
        "top"       => 0,
        "left"      => 0,
        "fontSize"  => 12,
        "fontFamily"=> "simsun",
        "fontWeight"=> "bold",
        "position"  => "absolute",
        "custom"    => false,
        "isMerge"   => false,
    ],
//    [
//        "id"        => 3,
//        "aliasName" => "订单号",
//        "showName"  => "193170000164XXXX",
//        "value"     => "<%=data.orderNum%>",
//        "width"     => 216,
//        "height"    => 20,
//        "top"       => 0,
//        "left"      => 0,
//        "fontSize"  => 12,
//        "fontFamily"=> "simsun",
//        "fontWeight"=> "bold",
//        "position"  => "absolute",
//        "custom"    => false,
//        "isMerge"   => false,
//    ],
//    [
//        "id"        => 2,
//        "aliasName" => "下单时间",
//        "showName"  => "2019-06-16 12:12:12",
//        "value"     => "<%=data.confirmTime%>",
//        "width"     => 156,
//        "height"    => 20,
//        "top"       => 0,
//        "left"      => 0,
//        "fontSize"  => 12,
//        "fontFamily"=> "simsun",
//        "fontWeight"=> "bold",
//        "position"  => "absolute",
//        "custom"    => false,
//        "isMerge"   => false,
//    ],
//    [
//        "id"        => 12,
//        "aliasName" => "买家留言",
//        "showName"  => "买家留言",
//        "value"     => "<%=data.buyerMemo%>",
//        "width"     => 60,
//        "height"    => 20,
//        "top"       => 0,
//        "left"      => 0,
//        "fontSize"  => 12,
//        "fontFamily"=> "simsun",
//        "fontWeight"=> "bold",
//        "position"  => "absolute",
//        "custom"    => false,
//        "isMerge"   => false,
//    ],
//	[
//        "id"        => 11,
//        "aliasName" => "卖家备注",
//        "showName"  => "[卖家备注：内部商品请小心轻放]",
//        "value"     => "<%=data.remark%>",
//        "width"     => 82,
//        "height"    => 178,
//        "top"       => 0,
//        "left"      => 205,
//        "fontSize"  => 12,
//        "fontFamily"=> "simsun",
//        "fontWeight"=> "bold",
//        "position"  => "absolute",
//        "custom"    => false,
//        "isMerge"   => false,
//    ],
    [
        "id"        => 9,
        "aliasName" => "总价",
        "showName"  => "总价: 99.99元",
        "value"     => "<%=data.totalPrice%>",
        "width"     => 95,
        "height"    => 20,
        "top"       => 60,
        "left"      => 220,
        "fontSize"  => 12,
        "fontFamily"=> "simsun",
        "fontWeight"=> "bold",
        "position"  => "absolute",
        "custom"    => false,
        "isMerge"   => false,
    ],
    [
        "id"        => 7,
        "aliasName" => "总数",
        "showName"  => "共计：2件",
        "value"     => "<%=data.count%>",
        "width"     => 74,
        "height"    => 20,
        "top"       => 60,
        "left"      => 220,
        "fontSize"  => 12,
        "fontFamily"=> "simsun",
        "fontWeight"=> "bold",
        "position"  => "absolute",
        "custom"    => false,
        "isMerge"   => false,
    ],
    [
        "id"        => 10,
        "aliasName" => "总支付金额",
        "showName"  => "总支付金额：89.99元",
        "value"     => "<%=data.payAmount%>",
        "width"     => 125,
        "height"    => 20,
        "top"       => 60,
        "left"      => 220,
        "fontSize"  => 12,
        "fontFamily"=> "simsun",
        "fontWeight"=> "bold",
        "position"  => "absolute",
        "custom"    => false,
        "isMerge"   => false,
    ],
//    [
//        "id"        => 4,
//        "aliasName" => "商品名称",
//        "showName"  => "商品名称",
//        "value"     => "<%=data.productName%>",
//        "width"     => 150,
//        "height"    => 20,
//        "top"       => 0,
//        "left"      => 0,
//        "fontSize"  => 12,
//        "fontFamily"=> "simsun",
//        "fontWeight"=> "bold",
//        "position"  => "absolute",
//        "custom"    => false,
//        "isMerge"   => true,
//    ],
//	[
//        "id"        => 14,
//        "aliasName" => "商品ID",
//        "showName"  => "商品ID",
//        "value"     => "<%=data.goodsId%>",
//        "width"     => 80,
//        "height"    => 20,
//        "top"       => 0,
//        "left"      => 0,
//        "fontSize"  => 12,
//        "fontFamily"=> "simsun",
//        "fontWeight"=> "bold",
//        "position"  => "absolute",
//        "custom"    => false,
//        "isMerge"   => true,
//    ],
//    [
//        "id"        => 19,
//        "aliasName" => "商品编码",
//        "showName"  => "商品编码",
//        "value"     => "<%=data.outerId%>",
//        "width"     => 80,
//        "height"    => 20,
//        "top"       => 0,
//        "left"      => 0,
//        "fontSize"  => 12,
//        "fontFamily"=> "simsun",
//        "fontWeight"=> "bold",
//        "position"  => "absolute",
//        "custom"    => false,
//        "isMerge"   => true,
//    ],
//    [
//        "id"        => 8,
//        "aliasName" => "商品单价",
//        "showName"  => "9.99元",
//        "value"     => "<%=data.money%>",
//        "width"     => 47,
//        "height"    => 20,
//        "top"       => 0,
//        "left"      => 0,
//        "fontSize"  => 12,
//        "fontFamily"=> "simsun",
//        "fontWeight"=> "bold",
//        "position"  => "absolute",
//        "custom"    => false,
//        "isMerge"   => true,
//    ],
//    [
//        "id"        => 6,
//        "aliasName" => "规格",
//        "showName"  => "规格",
//        "value"     => "<%=data.productStandard%>",
//        "width"     => 100,
//        "height"    => 20,
//        "top"       => 0,
//        "left"      => 0,
//        "fontSize"  => 12,
//        "fontFamily"=> "simsun",
//        "fontWeight"=> "bold",
//        "position"  => "absolute",
//        "custom"    => false,
//        "isMerge"   => true,
//    ],
//	[
//        "id"        => 15,
//        "aliasName" => "规格ID",
//        "showName"  => "55316XXXXXX",
//        "value"     => "<%=data.skuId%>",
//        "width"     => 100,
//        "height"    => 20,
//        "top"       => 0,
//        "left"      => 0,
//        "fontSize"  => 12,
//        "fontFamily"=> "simsun",
//        "fontWeight"=> "bold",
//        "position"  => "absolute",
//        "custom"    => false,
//        "isMerge"   => true,
//    ],
//	[
//        "id"        => 21,
//        "aliasName" => "规格编码",
//        "showName"  => "规格编码",
//        "value"     => "<%=data.outerSkuId%>",
//        "width"     => 80,
//        "height"    => 20,
//        "top"       => 0,
//        "left"      => 0,
//        "fontSize"  => 12,
//        "fontFamily"=> "simsun",
//        "fontWeight"=> "bold",
//        "position"  => "absolute",
//        "custom"    => false,
//        "isMerge"   => true,
//    ],
//    [
//        "id"        => 13,
//        "aliasName" => "规格+数量",
//        "showName"  => "天蓝色 X2件",
//        "value"     => "<%=data.standardAndSkuNum%>",
//        "width"     => 100,
//        "height"    => 20,
//        "top"       => 0,
//        "left"      => 0,
//        "fontSize"  => 12,
//        "fontFamily"=> "simsun",
//        "fontWeight"=> "bold",
//        "position"  => "absolute",
//        "custom"    => false,
//        "isMerge"   => true,
//    ],
//    [
//        "id"        => 17,
//        "aliasName" => "商品名称+规格+数量",
//        "showName"  => "商品名称,10*120mm*10,天蓝色 X2件",
//        "value"     => "<%=data.allProductDetail%>",
//        "width"     => 240,
//        "height"    => 20,
//        "top"       => 0,
//        "left"      => 0,
//        "fontSize"  => 12,
//        "fontFamily"=> "simsun",
//        "fontWeight"=> "bold",
//        "position"  => "absolute",
//        "custom"    => false,
//        "isMerge"   => true,
//    ],
    [
        "id"        => 20,
        "aliasName" => "商品数量水印",
        "showName"  => "2件",
        "value"     => "<%=data.watermark%>",
        "width"     => 80,
        "height"    => 50,
        "top"       => 20,
        "left"      => 140,
        "fontSize"  => 40,
        "fontFamily"=> "simsun",
        "fontWeight"=> "bold",
        "position"  => "absolute",
        "color"     => "rgba(0, 0, 0, 0.4)",
        "custom"    => false,
        "isMerge"   => false,
    ],
    [
        "id"        => 23,
        "aliasName" => "n/m",
        "showName"  => "n/m包裹",
        "value"     => "<%=data.numOfPackage%>",
        "width"     => 100,
        "height"    => 20,
        "top"       => 0,
        "left"      => 0,
        "fontSize"  => 14,
        "fontFamily"=> "simsun",
        "fontWeight"=> "bold",
        "position"  => "absolute",
        "custom"    => false,
        "isMerge"   => false,
    ],
    [
        "id"        => 25,
        "aliasName" => "软件备注",
        "showName"  => "软件备注",
        "value"     => "<%=data.softRemark%>",
        "width"     => 204,
        "height"    => 178,
        "top"       => 0,
        "left"      => 0,
        "fontSize"  => 12,
        "fontFamily"=> "simsun",
        "fontWeight"=> "bold",
        "position"  => "absolute",
        "custom"    => false,
        "isMerge"   => false,
    ],
    [
        "id"        => 24,
        "aliasName" => "发货内容",
        "showName"  => "发货内容",
        "value"     => "<%=data.contents%>",
        "width"     => 204,
        "height"    => 178,
        "top"       => 0,
        "left"      => 0,
        "fontSize"  => 12,
        "fontFamily"=> "simsun",
        "fontWeight"=> "bold",
        "position"  => "absolute",
        "custom"    => false,
        "isMerge"   => false,
    ],
    [
        "id"        => 18,
        "aliasName" => "自定义文字",
        "showName"  => null,
        "value"     => null,
        "width"     => 1,
        "height"    => 1,
        "top"       => 40,
        "left"      => 0,
        "fontSize"  => 12,
        "fontFamily"=> "simsun",
        "fontWeight"=> "bold",
        "position"  => "absolute",
        "custom"    => true,
        "isMerge"   => false,
    ],
//    [
//        "id"        => 22,
//        "aliasName" => "规格编码+数量",
//        "showName"  => "规格编码+数量",
//        "value"     => "<%=data.outerIdAndSkuNum%>",
//        "width"     => 100,
//        "height"    => 20,
//        "top"       => 0,
//        "left"      => 0,
//        "fontSize"  => 12,
//        "fontFamily"=> "simsun",
//        "fontWeight"=> "bold",
//        "position"  => "absolute",
//        "custom"    => false,
//        "isMerge"   => true,
//    ],
//    [
//        "id"        => 16,
//        "aliasName" => "商品数量",
//        "showName"  => "✖1",
//        "value"     => "<%=data.skuNum%>",
//        "width"     => 40,
//        "height"    => 20,
//        "top"       => 0,
//        "left"      => 0,
//        "fontSize"  => 12,
//        "fontFamily"=> "simsun",
//        "fontWeight"=> "bold",
//        "position"  => "absolute",
//        "custom"    => false,
//        "isMerge"   => true,
//    ],
];
