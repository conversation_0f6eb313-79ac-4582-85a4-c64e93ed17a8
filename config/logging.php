<?php

use Monolog\Handler\StreamHandler;

return [

    /*
    |--------------------------------------------------------------------------
    | Default Log Channel
    |--------------------------------------------------------------------------
    |
    | This option defines the default log channel that gets used when writing
    | messages to the logs. The name specified in this option should match
    | one of the channels defined in the "channels" configuration array.
    |
    */

    'default' => env('LOG_CHANNEL', 'daily'),

    /*
    |--------------------------------------------------------------------------
    | Log Channels
    |--------------------------------------------------------------------------
    |
    | Here you may configure the log channels for your application. Out of
    | the box, Laravel uses the Monolog PHP logging library. This gives
    | you a variety of powerful log handlers / formatters to utilize.
    |
    | Available Drivers: "single", "daily", "slack", "syslog",
    |                    "errorlog", "monolog",
    |                    "custom", "stack"
    |
    */

    'channels' => [
        'stack' => [
            'driver' => 'stack',
            'channels' => ['single'],
        ],

        'single' => [
            'driver' => 'single',
            'path' => storage_path('logs/lumen.log'),
            'level' => 'debug',
        ],

        'daily' => [
            'driver' => 'daily',
            'path' => storage_path('logs/lumen.log'),
            'tap' => [App\Logging\CustomizeFormatter::class],
            'level' => 'info',
            'days' => 3,
        ],

        'slack' => [
            'driver' => 'slack',
            'url' => env('LOG_SLACK_WEBHOOK_URL'),
            'username' => 'lumen Log',
            'emoji' => ':boom:',
            'level' => 'critical',
        ],

        'stderr' => [
            'driver' => 'monolog',
            'handler' => StreamHandler::class,
            'with' => [
                'stream' => 'php://stderr',
            ],
        ],

        'syslog' => [
            'driver' => 'syslog',
            'level' => 'debug',
        ],

        'errorlog' => [
            'driver' => 'errorlog',
            'level' => 'debug',
        ],
        'request' => [
            'driver' => 'daily',
            'path' => storage_path('logs/request.log'),
            'tap' => [App\Logging\CustomizeFormatter::class],
//            'formatter' => App\logging\CustomizeFormatter::class,
            'level' => 'debug',
            'days' => 3,
            'value_max_length' => 2048,
        ],
        'openapi' => [
            'driver' => 'daily',
            'path' => storage_path('logs/open_api.log'),
            'tap' => [App\Logging\CustomizeFormatter::class],
//            'formatter' => App\logging\CustomizeFormatter::class,
            'level' => 'debug',
            'days' => 3,
            'value_max_length' => 2048,
        ],
        'jdJcq' => [
            'driver' => 'daily',
            'path' => storage_path('logs/jd_jcq.log'),
            'tap' => [App\Logging\CustomizeFormatter::class],
//            'formatter' => App\logging\CustomizeFormatter::class,
            'level' => 'debug',
            'days' => 3,
            'value_max_length' => 2048,
         ],
        'jdDbSync' => [
            'driver' => 'daily',
            'path' => storage_path('logs/jd_db_sync.log'),
            'tap' => [App\Logging\CustomizeFormatter::class],
//            'formatter' => App\logging\CustomizeFormatter::class,
            'level' => 'debug',
            'days' => 3,
            'value_max_length' => 2048,
        ],
    ],

];
