<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2025/4/27
 * Time: 15:48
 */

// 来自接口：https://open.taobao.com/api.htm?spm=a219a.7386797.0.0.17bf669avaeWbK&source=search&docId=233&docType=2
// 更新日期：2025-04-27
return [
    [
        "code" => "POST",
        "id" => 1,
        "name" => "中国邮政"
    ],
    [
        "code" => "OTHER",
        "id" => -1,
        "name" => "其他"
    ],
    [
        "code" => "CYEXP",
        "id" => 511,
        "name" => "长宇"
    ],
    [
        "code" => "DTW",
        "id" => 512,
        "name" => "大田"
    ],
    [
        "code" => "YUD",
        "id" => 513,
        "name" => "长发"
    ],
    [
        "code" => "DISTRIBUTOR_13211725",
        "id" => 1216000000124268,
        "name" => "跨越速运"
    ],
    [
        "code" => "DISTRIBUTOR_30493846",
        "id" => 1216000000361492,
        "name" => "平安达腾飞快递"
    ],
    [
        "code" => "DISTRIBUTOR_13222803",
        "id" => 1216000000125358,
        "name" => "中通快运"
    ],
    [
        "code" => "PKGJWL",
        "id" => 21000038002,
        "name" => "派易国际物流77"
    ],
    [
        "code" => "DISTRIBUTOR_13148625",
        "id" => 6000100034229,
        "name" => "菜鸟大件-中铁配"
    ],
    [
        "code" => "DISTRIBUTOR_13159132",
        "id" => 6000100034186,
        "name" => "菜鸟大件-日日顺配"
    ],
    [
        "code" => "DISTRIBUTOR_30464910",
        "id" => 1216000000349602,
        "name" => "丰网速运"
    ],
    [
        "code" => "DISTRIBUTOR_30292473",
        "id" => 1216000000280959,
        "name" => "大食品商家自配"
    ],
    [
        "code" => "WND",
        "id" => 21000127009,
        "name" => "WnDirect"
    ],
    [
        "code" => "GZLT",
        "id" => 200427,
        "name" => "飞远配送 "
    ],
    [
        "code" => "DISTRIBUTOR_1710055",
        "id" => 5000000178661,
        "name" => "邮政标准快递"
    ],
    [
        "code" => "DISTRIBUTOR_13484485",
        "id" => 1216000000158681,
        "name" => "顺心捷达"
    ],
    [
        "code" => "YC",
        "id" => 1139,
        "name" => "远长"
    ],
    [
        "code" => "DFH",
        "id" => 1137,
        "name" => "东方汇"
    ],
    [
        "code" => "UNIPS",
        "id" => 1237,
        "name" => "发网"
    ],
    [
        "code" => "MGSD",
        "id" => 21000007003,
        "name" => "美国速递"
    ],
    [
        "code" => "BHWL",
        "id" => 21000053037,
        "name" => "保宏物流"
    ],
    [
        "code" => "ZJS",
        "id" => 103,
        "name" => "宅急送"
    ],
    [
        "code" => "SF",
        "id" => 505,
        "name" => "顺丰速运"
    ],
    [
        "code" => "STO",
        "id" => 100,
        "name" => "申通快递"
    ],
    [
        "code" => "EMS",
        "id" => 2,
        "name" => "EMS"
    ],
    [
        "code" => "YUNDA",
        "id" => 102,
        "name" => "韵达快递"
    ],
    [
        "code" => "HTKY",
        "id" => 502,
        "name" => "极兔速递"
    ],
    [
        "code" => "YTO",
        "id" => 101,
        "name" => "圆通速递"
    ],
    [
        "code" => "TTKDEX",
        "id" => 504,
        "name" => "天天快递"
    ],
    [
        "code" => "QFKD",
        "id" => 1216,
        "name" => "全峰快递"
    ],
    [
        "code" => "UC",
        "id" => 1207,
        "name" => "优速快递"
    ],
    [
        "code" => "DBKD",
        "id" => 5000000110730,
        "name" => "德邦快递"
    ],
    [
        "code" => "ZTO",
        "id" => 500,
        "name" => "中通快递"
    ],
    [
        "code" => "GTO",
        "id" => 200143,
        "name" => "国通快递"
    ],
    [
        "code" => "FAST",
        "id" => 1204,
        "name" => "快捷快递"
    ],
    [
        "code" => "SURE",
        "id" => 201174,
        "name" => "速尔快运"
    ],
    [
        "code" => "FEDEX",
        "id" => 106,
        "name" => "联邦快递"
    ],
    [
        "code" => "SHQ",
        "id" => 108,
        "name" => "华强物流"
    ],
    [
        "code" => "UAPEX",
        "id" => 1259,
        "name" => "全一快递"
    ],
    [
        "code" => "HOAU",
        "id" => 1191,
        "name" => "天地华宇"
    ],
    [
        "code" => "BEST",
        "id" => 105,
        "name" => "百世物流"
    ],
    [
        "code" => "LB",
        "id" => 1195,
        "name" => "龙邦速递"
    ],
    [
        "code" => "XB",
        "id" => 1186,
        "name" => "新邦物流"
    ],
    [
        "code" => "EYB",
        "id" => 3,
        "name" => "邮政电商标快EYB"
    ],
    [
        "code" => "XFWL",
        "id" => 202855,
        "name" => "信丰物流"
    ],
    [
        "code" => "POSTB",
        "id" => 200734,
        "name" => "邮政快递包裹"
    ],
    [
        "code" => "DBL",
        "id" => 107,
        "name" => "德邦物流"
    ],
    [
        "code" => "LTS",
        "id" => 1214,
        "name" => "联昊通"
    ],
    [
        "code" => "ESB",
        "id" => 200740,
        "name" => "E速宝"
    ],
    [
        "code" => "BESTQJT",
        "id" => 105031,
        "name" => "百世快运"
    ]
];

