<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2021/6/21
 * Time: 11:44
 */

namespace App\Logging;


use Monolog\Formatter\LineFormatter;

class CustomizeFormatter extends LineFormatter
{

    /**
     * 自定义给定的日志实例。
     *
     * @param  \Illuminate\Log\Logger  $logger
     * @return void
     */
    public function __invoke($logger)
    {
        foreach ($logger->getHandlers() as $handler) {
            $handler->setFormatter(new CustomizeLineFormatter());
        }
    }


}
