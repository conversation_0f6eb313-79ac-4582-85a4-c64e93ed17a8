<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ExportTask extends Model
{
    use SoftDeletes;

    protected $table = "export_task";
    //任务状态
    const STATUS_WAITING = 0; //待执行
    const STATUS_SUCCESS = 1;//完成
    const STATUS_DOING = 2;//执行中
    const STATUS_ERROR = 3; //执行失败

    //任务类型
    const WAYBILL_HISTORY = 1; //取号记录
    const WAYBILL_DELIVERY = 2; //发货记录
    const TYPE_PRINT_LOG = 3; //打印日志
    const SHARE_WAYBILL_LIST = 4; //分享单号明细
    const EXCEPTION_LOGISTIC = 5; //异常物流
    const DELIVER_RECORD = 6; //发货记录新
    const WAYBILL_HISTORY_PACKAGE = 7; //取号记录包裹

    const WAREHOUSE_DETAIL_REPORT = 8; //云仓明细报表

    const PLATFORM_ORDER = 9; //平台订单
    /**
     * The attributes that are mass assignable.
     * @var array
     */
    protected $fillable = [
        'user_id',
        'shop_id',
        'type',
        'condition',
        'status',
        'file_path',
        'created_at',
        'updated_at',
        'url',
        'memo',
        'name'
    ];


    /**
     * @param $shopIdList
     * @param string $orderShopName
     * @return string
     */
    public static  function buildExportTaskName($shopIdList,string $orderShopName=""):string
    {
        $name="";
        if(!empty($orderShopName)){
            $name=$orderShopName. '-' . date('m-d-H-i');;
        }else {
            //把店铺名拼接成任务名
            foreach ($shopIdList as $shopId) {
                $shop = Shop::find($shopId);
                $name = $name . $shop->shop_name . "-";
            }
            $name = $name . date('m-d-H-i');
//            if (!empty($shopIdList)) {
//                $firstShop = Shop::find($shopIdList[0]);
//                $name = $firstShop->shop_name;
//                $size = sizeof($shopIdList);
//                if ($size > 1) {
//                    $name = $name . "-" . strval($size);
//                }
//                $name = $name . '-' . date('m-d-H-i');
//            }

        }
        return $name;
    }
}
