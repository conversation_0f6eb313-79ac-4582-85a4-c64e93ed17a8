<?php

namespace App\Models;

interface WaybillAuth
{

    /**
     * 返回serviceId，这个京东用到
     * @return string
     */
    public  function getServiceId(): string;

    /**
     * 返回平台的店铺ID
     * @return string
     */
    public function getIdentifier():string;

    /**
     * 返回平台的店铺名称
     * @return string
     */
    public function getShopName():string;

    /** 这个是微信小商店使用的
     * @return string
     */

    public function getSpecificationId():string;

    /**
     * 返回平台的店铺accessToken
     * @return string
     */
    public function getAccessToken():string;
}
