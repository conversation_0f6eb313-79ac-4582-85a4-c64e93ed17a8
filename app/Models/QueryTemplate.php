<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2020/5/20
 * Time: 15:04
 */

namespace App\Models;


use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class QueryTemplate extends Model
{
	use SoftDeletes;

    /**
     * The attributes that are mass assignable.
     * @var array
     */
    protected $fillable = [
        'user_id',
        'shop_id',
        'name',
        'data',
    ];
}
