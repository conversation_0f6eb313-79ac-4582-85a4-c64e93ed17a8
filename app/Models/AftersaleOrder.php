<?php
/**
 * Created by PhpStorm.
 * User: xuji<PERSON><PERSON>
 * Date: 2022/9/20
 * Time: 14:05
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;

class AftersaleOrder extends Model
{
    use SoftDeletes;

    protected $table = "aftersale_order";

    protected $fillable = [
        'tid',
        'shop_id',
        'refund_id',
        'refund_status',
        'refund_reason',
        'refund_memo',
        'refund_price',
        'refund_created_at',
        'refund_updated_at'
    ];

    /**
     * 退款状态
     */

    const REFUND_STATUS_INIT = 1; //待退款
    const REFUND_STATUS_WAIT = 2; //退款中
    const REFUND_STATUS_SUCCESS = 3; //退款成功
    const REFUND_STATUS_FAIL = 4; //退款失败
    const REFUND_STATUS_OTHER = 5; //未知

    /**
     * 关联子订单
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function orderItem()
    {
        return $this->hasMany('App\Models\OrderItem', 'refund_id', 'refund_id');
    }
}