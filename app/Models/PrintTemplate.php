<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class PrintTemplate extends Model
{
    use SoftDeletes;

    /**
     * 与模型关联的表名
     *
     * @var string
     */
    protected $table = 'print_templates';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'user_id',
        'style',
        'name',
        'width',
        'height',
        'waybill_type',
        'show_logo',
        'horizontal',
        'horizontal_type',
        'vertical',
        'vertical_type',
        'merge_template_url',
        'wp_code',
        'wp_name',
        'custom_config',
        'print_contents',
    ];

    /**
     * 模板样式常量
     */
    const STYLE_PDD_STANDARD = 1; // 拼多多标准模板
    const STYLE_PDD_ONE_SHEET = 3; // 拼多多一联单模板

    /**
     * 获取关联的用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }
} 