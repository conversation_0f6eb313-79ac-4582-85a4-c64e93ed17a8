<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class AbnormalOrder extends Model
{
    use SoftDeletes;

    protected $connection = 'mysql';

    protected $table = 'abnormal_order';

    const TYPE_OF_NONE   = 0; //无
    const TYPE_OF_ADDRESS_UPDATE   = 1; //打印后修改收货地址
    const TYPE_OF_ORDER_REMARK = 2; //打印后修改备注
    const TYPE_OF_ORDER_UNSENT_REFUND     = 3; //打印后订单未发货，用户退款
    const TYPE_OF_ADDRESS_ABNORMAL     = 4; //收货地址异常
    const TYPE_OF_DELIVERY_TIMEOUT     = 5; //已锁单待发货超时
    const TYPE_OF_ORDER_SEND_REFUND     = 6; //打印后订单已发货，用户退款
    const TYPE_OF_ORDER_MERGE_CHANGE     = 7; //打印后合单有变化
    const TYPE_PRINT_FAIL    = 8; // 打印失败

    const STATUS_OF_UNREAD   = 0; //未读
    const STATUS_OF_READ     = 1; //已读

    // 有异常的 type 数组
    const ABNORMAL_TYPE_ARRAY = [
        self::TYPE_OF_ADDRESS_UPDATE,
        self::TYPE_OF_ORDER_UNSENT_REFUND,
        self::TYPE_OF_ORDER_REMARK,
        self::TYPE_OF_ADDRESS_ABNORMAL,
        self::TYPE_OF_DELIVERY_TIMEOUT,
        self::TYPE_OF_ORDER_SEND_REFUND,
        self::TYPE_OF_ORDER_MERGE_CHANGE,
        self::TYPE_PRINT_FAIL,
    ];
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'type',
        'status',
        'shop_id',
        'order_id',
        'extra',
        'desc',
        'updated_at',
        'deleted_at'
    ];


    public function order()
    {
        return $this->belongsTo('App\Models\Fix\Order', 'order_id', 'id');
    }
}
