<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ShopExtra extends Model
{
    /**
     * 打印以后是否重置模板
     */
    const AUTO_RESET_TEMPLATE_NO = 0;

    /**
     * 文件下载
     */
    const  BROWSER_DOWNLOAD_NO = 0;
    const PRINT_CONTENTS_DEFAULT = '{"goodsTitle":"1","outerIid":"0","numIid":"0","skuDesc":"1","skuId":"0","outerSkuIid":"0","payment":"0","goodsNum":"1","goodsLineFeed":"1","goodsPaging":"1","goodsNumStyle":"[%s]","goodsNumCompany":"件","pageCountNum":"5","goodsMerge":"1"}';
    const MERGE_ORDER_MERGE_SKU_YES = 1;

    protected $fillable = [
        'user_id',
        'shop_id',
        'index_combine_show',
        'warning_address_str',
        'needle_items',
        'merge_order_open',
        'merge_order_num',
        'merge_order_merge_sku',
        'is_cross_shop_merge_order',
        'is_auto_refresh',
        'is_automatic_delivery',
        'print_contents',
        'preset_logistics_union_wp_code',
        'preset_logistics_district_switch',
        'preset_logistics_district_data',
        'auto_reset_template',
        'browser_download',
        'is_unlimited_version',
        'warehouse_name',
        'waybill_extra_province',
        'open_live_mode',
        'live_mode_expire_at',
        'print_config',
    ];

	const SHOW_SINGLE = 1; //单个显示
	const SHOW_COMBILE = 2; //合并显示

	const WARNING_ADDRESS_DEFAULT = '村,庄,旗,屯,乡,镇';  //设置乡镇默认值

	const NEEDLE_ITEMS = 'refund,warningAddress';  //默认项

	const MERGE_ORDER_OPEN_YES = 1;  //默认开启合单
	const MERGE_ORDER_OPEN_NO = 0;  //关闭合单

	const MERGE_ORDER_NUM_DEFAULT = 100;  //合单数量默认值
	const IS_CROSS_SHOP_MERGE_ORDER_DEFAULT = 0; // 是否跨店铺合单

	const IS_AUTH_REFRESH_NO = 0;  //订单数据不自动刷新
	const IS_AUTH_REFRESH_YES = 1;  //订单数据自动刷新

	const IS_AUTOMATIC_DELIVERY_NO = 0;  //不自动发货
	const IS_AUTOMATIC_DELIVERY_YES = 1;  //自动发货


    const DEFAULT_PRINT_CONFIG = '{"orderNo":"0","goodsTitle":"1","outerIid":"0","numIid":"0","skuDesc":"1","skuId":"0","outerSkuIid":"0","payment":"0","goodsNum":"1","goodsLineFeed":"1","goodsPaging":"0","goodsNumStyle":"[%s]","goodsNumCompany":"件","pageCountNum":5,"maxPageCountNum":5,"goodsMerge":"1","buyerMemo":"0","remark":"0"}';
    /**
     * 这个属性应该被转换为原生类型.
     *
     * @var array
     */
    protected $casts = [
        'preset_logistics_district_data' => 'array',
    ];

    public static function firstByShopId($shopId)
    {
        return self::query()->where('shop_id', $shopId)->first();
    }

    public static function getValByShopId($shopId, $key, $default = null)
    {
        $shopExtra = self::firstByShopId($shopId);
        if (empty($shopExtra)) {
            return $default;
        }
        return $shopExtra->$key;
    }

    public static function getPrintContentConfig($shopId)
    {
        $shopExtra = ShopExtra::query()->where('shop_id', $shopId)->first();
        if (empty($shopExtra['print_contents'])){
            $str = ShopExtra::PRINT_CONTENTS_DEFAULT;
        }else{
            $str = $shopExtra['print_contents'];
        }
        // 打印内容配置
        return json_decode($str, true);
    }


    /**
     * 新疆和西藏省级名称
     * @var string
     */
    public $xinjiang_xizang = '新疆维吾尔自治区,西藏自治区';
}
