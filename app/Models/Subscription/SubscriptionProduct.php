<?php

namespace App\Models\Subscription;

use App\Constants\PlatformConst;
use App\Models\UserExtra;

class SubscriptionProduct
{

    public const PRODUCT_TYPE_MAIN ='main';
    /**
     * 产品名称
     * @var string $name
     */
    public $name;
    /**
     * @var string $code 产品编码，唯一标识一个产品
     */
    public $code;
    /**
     * @var string $platform 产品所属平台，如：小红书、淘工厂等
     */
    public $platform;
    /**
     * @var string $type 产品类型
     * @var
     */
    public $type;

    public $versions;


    /**
     * @param $name
     * @param $code
     * @param $type
     * @param $platform
     * @param $versions
     */
    public function __construct($name, $code, $type, $platform,$versions)
    {
        $this->name = $name;
        $this->code = $code;
        $this->platform = $platform;
        $this->type = $type;
        $this->versions = $versions;
    }

    /**
     * 获取平台的订购产品
     * @param string $platform
     * @return SubscriptionProduct|null
     */
    public static function platformSubscriptionMainProduct(string $platform): ?SubscriptionProduct
    {
        $all = self::allProducts();
        return array_first($all, function ($item) use ($platform) {
            return $item->platform == $platform && $item->type == SubscriptionProduct::PRODUCT_TYPE_MAIN;
        }
        );

    }

    public static function allProducts(): array
    {
        $xhsVersions=[
            new SubscriptionVersion(UserExtra::VERSION_STANDARD_NAME, UserExtra::VERSION_STANDARD),
            new SubscriptionVersion(UserExtra::VERSION_SENIOR_NAME, UserExtra::VERSION_SENIOR),
        ];
        $alc2mVersions=[
            new SubscriptionVersion(UserExtra::VERSION_STANDARD_NAME, UserExtra::VERSION_STANDARD),
            new SubscriptionVersion(UserExtra::VERSION_SENIOR_NAME, UserExtra::VERSION_SENIOR),
        ];
        return [
            new SubscriptionProduct('小红书', PlatFormConst::XHS, self::PRODUCT_TYPE_MAIN,PlatFormConst::XHS,$xhsVersions),
            new SubscriptionProduct('淘工厂', PlatFormConst::ALC2M, self::PRODUCT_TYPE_MAIN,PlatFormConst::ALC2M,$alc2mVersions),

        ];
    }

    public function versions():array{
        return $this->versions;
    }

}
