<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class QueryArea extends Model
{
	use SoftDeletes;

	protected $fillable = [
		'user_id',
		'shop_id',
		'name',
		'province_str',
		'city_str',
		'district_str',
		'data',
		'template_id',
		'include',
		'union_wp_code',
		'custom_original_data',
        'custom_district_str',
        'version'
	];

	/**
	 * 数据处理
	 * @param array $data
	 * @return array
	 */
	public static function handleStr(array $data, $version = 1)
	{
		$provinceStr = '';
		$cityStr = '';
		$districtStr = '';

		if (empty($data) && !is_array($data)) {
			return [
				'province_str' => $provinceStr,
				'city_str'     => $cityStr,
				'district_str' => $districtStr,
			];
		}
        if ($version == 2) {
            $provinceArr = [];
            // 特殊的省别名,省级市,名字长的
            $specialProvince = [
                '北京市' => ['北京市', '北京'],
                '天津市' => ['天津市', '天津'],
                '上海市' => ['上海市', '上海'],
                '重庆市' => ['重庆市', '重庆'],
                '内蒙古自治区' => ['内蒙古自治区', '内蒙古'],
                '广西壮族自治区' => ['广西壮族自治区', '广西'],
                '西藏自治区' => ['西藏自治区', '西藏'],
                '宁夏回族自治区' => ['宁夏回族自治区', '宁夏'],
                '新疆维吾尔自治区' => ['新疆维吾尔自治区', '新疆'],
            ];
            foreach ($data as $p) {
                if (!empty($specialProvince[$p['name']])){
                    $provinceArr = array_merge($provinceArr,$specialProvince[$p['name']]);
                }else{
                    // 替换掉后面的省，比如：甘肃省,甘肃
                    $provinceArr[] = $p['name'];
                    if (strpos($p['name'], '省') !== false) {
                        $provinceArr[] = str_replace('省', '', $p['name']);
                    }
                }
            }
            $provinceStr = implode(',', $provinceArr);
        }else{
            foreach ($data as $p) {
                $provinceStr .= $p['code'] . ',';
                foreach ($p['children'] as $c) {
                    $cityStr .= $c['code'] . ',';
                    foreach ($c['children'] as $d) {
                        $districtStr .= $d['code'] . ',';
                    }
                }
            }
            $provinceStr = substr($provinceStr, 0, -1);
            $cityStr = substr($cityStr, 0, -1);
            $districtStr = substr($districtStr, 0, -1);
        }


        return [
			'data'         => json_encode($data, JSON_UNESCAPED_UNICODE),
			'province_str' => $provinceStr,
			'city_str'     => $cityStr,
			'district_str' => $districtStr
		];
	}

}
