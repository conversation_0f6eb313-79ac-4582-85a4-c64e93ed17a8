<?php

namespace App\Models;

use App\Services\Goods\AbstractGoodsService;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class Goods extends Model
{
    use SoftDeletes;

    const IS_ONSALE_NO = 1; //下架
    const IS_ONSALE_YES = 2; //上架
    /**
     * The attributes that are mass assignable.
     * @var array
     */
    protected $fillable = [
        'user_id',
        'shop_id',
        'type',
        'num_iid',
        'outer_goods_id',
        'goods_title',
        'custom_title',
        'goods_pic',
        'is_onsale',
        'goods_created_at',
        'goods_updated_at',
    ];

    public function skus()
    {
        return $this->hasMany(GoodsSku::class, 'goods_id', 'id')->from(DB::raw('goods_skus' . ' force index(idx_goodsid_withdata_ext)'));
    }



    /**
     * 商品批量入库
     * @param array $goods
     * @param int $userId
     * @param int $shopId
     */
    public
    static function batchSave(array $goods, int $userId, int $shopId)
    {
        foreach ($goods as $index => $good) {
            $numIid = (string)$good['num_iid'];
            $good['num_iid'] = $numIid;
            $cacheKey = "batchSaveGoods{$good['type']}:{$good['num_iid']}:";
            DB::transaction(function () use ($good, $userId, $shopId, $cacheKey, $numIid) {
                $good['user_id'] = $userId;
                $good['shop_id'] = $shopId;
                if (empty($good['skus'])) {
                    Log::error('empty skus', $good);
                }
                $skus = $good['skus'];
                unset($good['skus']);
                $remark = AbstractGoodsService::readCacheRemark($numIid);
                if (!empty($remark)) {
                    Log::info('从缓存中获取商品备注 num_iid' . $numIid);
                    $good['remark'] = $remark;
                }
                $nowTime = Carbon::now();
                $good['updated_at'] = $nowTime;
                $goodModel = self::query()->updateOrCreate([
                    'num_iid' => $good['num_iid'],
                    'type' => $good['type'],
                    //'user_id' => $good['user_id'],
                    'shop_id' => $good['shop_id'],
                ], $good);

                Log::info('更新商品成功！', ['good' => $good]);
                foreach ($skus as $datum) {
                    $datum['updated_at'] = $nowTime;
                    $bool = GoodsSku::query()->updateOrInsert([
                        'goods_id' => $goodModel['id'],
                        //'user_id'  => $userId,
                        'shop_id' => $shopId,
                        'type' => $datum['type'],
                        'sku_id' => $datum['sku_id'],
                    ], $datum);
                    if (!$bool) {
                        Log::error('插入商品sku失败！', ['skus' => $datum]);
                    }
                }
            });
        }
    }

    public static function batchUpdateData($updateColumn, $updateData, $table, $whereColumn = "id", $otherWhere = '1=1')
    {
        $primaryKey = array_keys($updateData);
        $Ids = implode("','", $primaryKey);
        $Ids = "'$Ids'"; // 转成 '1','2','3'
        $executeSql = "";
        $executeSql .= "UPDATE {$table} SET ";
        foreach ($updateColumn as $sk => $value) {
            $executeSql .= " $value=CASE $whereColumn ";
            foreach ($primaryKey as &$vs) {
                $executeSql .= " WHEN '{$vs}' THEN '{$updateData[$vs][$value]}' ";
            }
            $executeSql .= " END,";
        }
        $executeSql = rtrim($executeSql, ',');
        $executeSql .= " WHERE ".$whereColumn." IN($Ids) and ".$otherWhere."; ";
        DB::update($executeSql);
    }

    public static function batchUpdateData2($table, $updateColumn, $updateData, $whereColumn = "id")
    {
        $primaryKey = array_keys($updateData);
        $Ids = implode(',', $primaryKey);
        $executeSql = "";
        $executeSql .= "UPDATE {$table} SET ";
        foreach ($updateColumn as $sk => $value) {
            $executeSql .= " $value=CASE $whereColumn ";
            foreach ($primaryKey as $vs) {
                $executeSql .= " WHEN {$vs} THEN '{$updateData[$vs][$value]}' ";
            }
            $executeSql .= " END,";
        }
        $executeSql = rtrim($executeSql, ',');
        $executeSql .= " WHERE " . $whereColumn . " IN($Ids);";
        return $executeSql;
        DB::update($executeSql);
    }
}
