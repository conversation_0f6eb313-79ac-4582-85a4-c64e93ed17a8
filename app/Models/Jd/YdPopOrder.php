<?php

namespace App\Models\Jd;

use Illuminate\Database\Eloquent\Model;

/**
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\Jd\YdPopOrder query()
 */
class YdPopOrder  extends Model
{

    /**
     * The table associated with the model.
     */
    protected $table = 'yd_pop_order';

    /**
     * The connection name for the model.
     */
    protected $connection = 'jd_data_push';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = ['id', 'order_id', 'order_status', 'order_type', 'shop_id', 'doudian_open_id', 'create_time', 'update_time', 'ddp_created', 'ddp_modified', 'ddp_response', 'version', 'digest', 'version_update_time'];
}
