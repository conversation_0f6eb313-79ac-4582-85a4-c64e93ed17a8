<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * 货品SKU
 */
class CommoditySku extends Model
{
    protected $table='commodity_sku';

    protected $fillable = [
        'shop_id',
        'type',
        'name',
        'weight',
        'net_weight',
        'settlement_price',
        'commodity_id',
        'platform_sku_out_id',
        'platform_commodity_out_id'
    ];

    /**
     * 关联到平台商品
     * @return BelongsTo
     */
    public function goods(): BelongsTo
    {
        return $this->belongsTo(Goods::class, 'num_iid', 'platform_commodity_out_id');
    }

    public function commodity(): BelongsTo
    {
        return $this->belongsTo(Commodity::class, 'commodity_id', 'id');
    }

}
