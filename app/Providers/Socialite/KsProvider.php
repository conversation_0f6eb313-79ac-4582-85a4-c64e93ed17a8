<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2020/3/9
 * Time: 18:43
 */

namespace App\Providers\Socialite;


use App\Services\Client\KsClient;
use Overtrue\Socialite\AccessTokenInterface;
use Overtrue\Socialite\ProviderInterface;
use Overtrue\Socialite\Providers\AbstractProvider;
use Overtrue\Socialite\User;

/**
 * Class KsProvider
 * @package App\Providers\Socialite
 * @see https://docs.qq.com/doc/DQ3hyU3F4b0p6TENX
 * @see https://docs.qq.com/doc/DUklHZ05lUG12aURq
 */
class KsProvider extends AbstractProvider implements ProviderInterface
{
    protected $stateless = true;
    /**
     *
     * @var string
     */
    protected $baseUrl = 'https://open.kuaishou.com';
    protected $gatewayUrl = 'https://s.kwaixiaodian.com';
    protected $scopes = [
        'user_base',
        'user_info',
        'merchant_item',
        'merchant_order',
        'merchant_servicemarket',
        'merchant_user',
        'merchant_comment',
        'merchant_logistics',
        'merchant_refund',
    ];


    /**
     * @inheritDoc
     */
    protected function getAuthUrl($state)
    {
        return $this->buildAuthUrlFromBase($this->gatewayUrl . '/oauth/authorize', $state);
    }

    /**
     * @return KsClient
     * <AUTHOR>
     */
    protected function getClient()
    {
        return new KsClient($this->clientId, $this->clientSecret);
    }

    /**
     * @inheritDoc
     */
    protected function getCodeFields($state = null)
    {
        $fields = array_merge([
            'app_id' => $this->clientId,
            'redirect_uri' => $this->redirectUrl,
            'scope' => env('KS_SCOPE', $this->formatScopes($this->scopes, $this->scopeSeparator)),  //快递侠
            'response_type' => 'code',
        ], $this->parameters);

        if ($this->usesState()) {
            $fields['state'] = $state;
        }

        return $fields;
    }

    /**
     * @inheritDoc
     */
    protected function getTokenUrl()
    {
        return $this->gatewayUrl . '/oauth2/access_token';
    }

    /**
     * @inheritDoc
     */
    protected function getUserByToken(AccessTokenInterface $token)
    {
//        $response = $this->getHttpClient()->get($this->baseUrl . '/openapi/user_info', [
//            'query' => [
//                'app_id' => $this->clientId,
//                'access_token' => $token->getToken(),
//            ],
//            'headers' => [
//                'Content-type' => 'application/json',
//                "Accept" => "application/json"
//            ],
//        ]);
        $ksClient = $this->getClient();
        $ksClient->setAccessToken($token->getToken());
        $data = $ksClient->execute('get', '/open/user/seller/get', []);
	    \Log::info(var_export($data, true));

        $user = $data['data'];
        $user['refresh_token'] = $token->getAttribute('refresh_token');
        $user['expires_in'] = $token->getAttribute('expires_in');
        $user['open_id'] = $token->getAttribute('open_id');
        return $user;
    }

    /**
     * @inheritDoc
     */
    protected function mapUserToObject(array $user)
    {
//        $shopId = $this->request->get('shopId');
//        if (empty($shopId)){
//            throw new \InvalidArgumentException('缺少Shop id');
//        }
        return new User([
            'id' => $user['sellerId'],
            'nickname' => $user['name'],
            'name' => $user['name'],
            'username' => $user['name'],// 'KS'.$shopId,
            'avatar' => $user['head'],
        ]);
    }

    /**
     * Get the access token for the given code.
     *
     * @param string $code
     *
     * @return AccessTokenInterface
     */
    public function getAccessToken($code)
    {
        if ($this->accessToken) {
            return $this->accessToken;
        }

        $postKey = 'query';

        $response = $this->getHttpClient()->get($this->getTokenUrl(), [
            'headers' => [
                'Content-type' => 'application/json',
                "Accept" => "application/json"
            ],
            'verify' => false,
            $postKey => $this->getTokenFields($code),
        ]);

        return $this->parseAccessToken($response->getBody());
    }

    /**
     * Get the POST fields for the token request.
     *
     * @param string $code
     *
     * @return array
     */
    protected function getTokenFields($code)
    {
        return [
            'app_id' => $this->clientId,
            'app_secret' => $this->clientSecret,
            'code' => $code,
            'grant_type' => 'code',
        ];
    }
}
