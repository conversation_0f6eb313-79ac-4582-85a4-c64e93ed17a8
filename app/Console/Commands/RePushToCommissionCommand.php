<?php
/**
 * Created by PhpStorm.
 * User: x<PERSON><PERSON><PERSON><PERSON>
 * Date: 2022/6/20
 * Time: 14:38
 */

namespace App\Console\Commands;

use App\Constants\PlatformConst;
use App\Models\ApiShopBind;
use App\Models\PlatformOrder;
use App\Models\Shop;
use App\Services\Order\OrderServiceManager;
use Illuminate\Console\Command;
use GuzzleHttp\Client;

class RePushToCommissionCommand extends Command
{
    protected $name = 'command:repush-to-commission';
    protected $description = '失败重新推送到结算系统';

    public function handle()
    {
        ApiShopBind::query()->chunk(50, function ($bindShopArr) {
            $pushData = [];
            foreach ($bindShopArr as $bindShop) {
                $shop = Shop::query()->where('id', $bindShop->shop_id)->first();
                if (empty($shop)) {
                    continue;
                }
                $platformOrder = PlatformOrder::query()->where(['auth_user_id' => $shop->name, 'is_push' => 0])->whereNotNull('order_id')->get();
                foreach ($platformOrder as $item) {
                    $pushData[] = [
                        'mallName' => $shop->name,
                        'amount' => $item->pay_fee / 100,
                        'orderTime' => $item->order_created_at,
                        'orderNo' => $item->order_id,
                        'days' => $item->duration,
                        'orderStatus' => 1,
                        'appkey' => $bindShop->app_id,
                        'shopId' => $shop->id,
                        'skuId' => $item->sku_id,
                        'skuName' => $item['sku_spec'],
                        'serviceName' => $item['service_name'],
                        'agentCode' => $bindShop->agent_code??'',
                        'shopName' => $shop->shop_name ?? '',
                    ];

                    if (!$item->shop_id) {
                        $item->shop_id = $shop->id;
                        $item->user_id = $shop->user_id;
                        $item->identifier = $shop->identifier;
                        $item->save();
                    }
                }
            }

            if (!empty($pushData)) {
                $formParams = [
                    'platformCode' => PlatformConst::TAOBAO,
                    'traceId' => session_create_id(),
                    'timestamp' => time(),
                ];
                $data['list'] = $pushData;
                $formParams['sign'] = GenerateSignForCommission($formParams, $data);
                //push
                $client = new Client([
                    'timeout' => 3,
                    'connect_timeout' => 3,
                ]);
                $response = $client->request('POST', 'http://180.184.68.244:8082/api/fuwu/order/sync?' . http_build_query($formParams), [
                    'json' => $data,
                ]);

                $body = json_decode($response->getBody(), true);
                \Log::Info('重新推送到结算系统 params:' . json_encode(array_merge($formParams, $data)) . ' res:' . json_encode($body));
                if (isset($body['data']) && $body['data'] == 'SUCCESS') {
                    foreach ($pushData as $item) {
                        PlatformOrder::query()->where('order_id', $item['orderNo'])->update(['is_push' => 1]);
                    }
                }
            }
        });
    }
}
