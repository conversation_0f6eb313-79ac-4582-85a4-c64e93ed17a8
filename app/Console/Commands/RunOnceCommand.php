<?php
/**
 * Created by PhpStorm.
 * User: xuji<PERSON><PERSON>
 * Date: 2022/2/16
 * Time: 9:15
 */

namespace App\Console\Commands;

use App\Constants\PlatformConst;
use App\Models\Address;
use App\Models\ApiAuthShop;
use App\Models\Company;
use App\Models\DeliveryRecord;
use App\Models\Order;
use App\Models\OrderCipherInfo;
use App\Models\QueryArea;
use App\Models\Shop;
use App\Models\ShopBind;
use App\Models\Template;
use App\Models\User;
use App\Models\WaybillHistory;
use App\Services\Auth\AuthServiceManager;
use App\Services\Order\OrderServiceManager;
use Illuminate\Console\Command;
use SebastianBergmann\CodeCoverage\Report\PHP;

class RunOnceCommand extends Command
{
    protected $signature   = 'command:run-once {--action=} {--shop_id=}';
    protected $description = '一次性脚本';

    /**
     * @var \Illuminate\Support\Collection|null
     */
    private $addressList;

    public function handle()
    {
        $action = $this->option('action');
        if (!$action) {
            $this->info('action invalid!');
            return;
        }

        //历史数据更新shopCode
        if ($action == 'createShopCode') {
            $this->createShopCode();
        }

        //历史数据绑定关系处理
        if ($action == 'changeBind') {
            $this->changeBind();
        }

        //更新历史数据添加source_shopid
        if ($action == 'addSourceShopIdToCompany') {
            $this->addSourceShopIdToCompany();
        }

        if ($action == 'addSourceShopIdToHistory') {
            $this->addSourceShopIdToHistory();
        }
        if ($action == 'modifyQueryAreasData') {
            $this->modifyQueryAreasData();
        }
        if ($action == 'fixDelivery') {
            $this->fixDelivery();
        }
        if ($action == 'closeShopSyncSwitch') {
            $this->closeShopSyncSwitch();
        }
        if ($action == 'encryptOrder') {
            $this->encryptOrder();
        }
        if ($action == 'updateVersion') {
            $this->updateVersion();
        }
        if ($action == 'fixOpenSubscribeMsg') {
            $this->fixOpenSubscribeMsg();
        }
    }

    protected function createShopCode()
    {
        $query = Shop::query();
        $query->each(function ($shop) {
            if (!$shop->shop_code) {
                //判断是否是礼品网的店铺 礼品网店铺使用原来的邀请码 防止礼品网调用出错
                $apiAuth = ApiAuthShop::query()->where('shop_identifier', $shop->identifier)->first();
                if (!empty($apiAuth)) {
                    $user = User::query()->where('id', $shop->user_id)->first();
                    Shop::query()->where('id', $shop->id)->update([
                        'shop_code' => $user->invite_code
                    ]);
                } else {
                    Shop::query()->where('id', $shop->id)->update([
                        'shop_code' => createShopCode()
                    ]);
                }
            }
        });
    }

    protected function changeBind()
    {
        //查询有绑定关系的user
        $hasBindShops = Shop::query()->where('inviter', '<>', 0)->get();
        foreach ($hasBindShops as $value) {
            $shop = Shop::query()->where('user_id', $value['inviter'])->first();
            //建立绑定关系（兄弟） 被绑定的作为主店铺
            ShopBind::bindShop($shop->id, $value['id'], ShopBind::BIND_TYPE_BROTHER);
        }
    }

    protected function addSourceShopIdToCompany()
    {
        Company::query()
            ->whereNotNull('source_userid')
            ->where('created_at', '<', date('Y-m-d H:i:s', time()))
            ->each(function ($company) {
                $shop = Shop::query()->where('user_id', $company->source_userid)->first();
                if (!empty($shop)) {
                    $company->source_shopid = $shop->id;
                    $company->save();
                }
        });
    }

    protected function addSourceShopIdToHistory()
    {
        WaybillHistory::query()
            ->where('source_userid', '<>', 0)
            ->where('created_at', '<', date('Y-m-d H:i:s', time()))
            ->each(function ($waybill) {
                $shop = Shop::query()->where('user_id', $waybill->source_userid)->first();
                if (!empty($shop)) {
                    $waybill->source_shopid = $shop->id;
                    $waybill->save();
                }
        });
    }

    private function modifyQueryAreasData()
    {
        $query = QueryArea::query();
        $this->addressList = Address::getAddressByCache();
        $query->each(function ($area) {
            echo $area->id . PHP_EOL;
            $dataArr = json_decode($area->data, true);
            $first = array_first($dataArr);
            if (!empty($first['code'])) {
                return;
            }
            if ($area->template_id > 0) {
                $template = Template::firstByIdAndShopId($area->template_id, $area->shop_id);
                if (!empty($template)) {
                    $express_company_list = config('express_company');
                    $express_company_list = array_pluck($express_company_list, null, 'wpCode');
                    $area->union_wp_code = $express_company_list[$template->wp_code]['unionWpCode'] ?? '';
                }
            }
            $list = $this->formatAddress($dataArr);
            $handleData = QueryArea::handleStr($list);
            $area->data = json_encode($list,JSON_UNESCAPED_UNICODE);
            $area->province_str = $handleData['province_str'];
            $area->city_str = $handleData['city_str'];
            $area->district_str = $handleData['district_str'];
            $area->save();
        });
    }

    protected function formatAddress(array $list, $parentCode = 1, $level = 1)
    {
        $resArr = [];
        foreach ($list as $item) {
            if ($level == 3 && is_string($item)) {
                $name = $item;
            }else{
                $name = $item['label'] ?? $item['name'];
            }
            $address = $this->addressList->where('name', $name)->where('parent_code', $parentCode)->first();
            if (empty($address) && $level == 2) {
                $address = $this->addressList->whereIn('name', [
                    '市辖区',
                    '省直辖县级行政区划',
                    '省直辖县级行政区划',
                    '自治区直辖县级行政区划',
                    '县',
                ])->where('parent_code', $parentCode)->first();
            }

            // 兼容数据
            $mapping = ['那曲市' => '那曲地区'];
            if (empty($address) && !empty($mapping[$name])) {
                $name = $mapping[$name];
                $address = $this->addressList->where('name', $name)->where('parent_code', $parentCode)->first();
            }
            if (empty($address)) {
                echo json_encode(compact('name','parentCode','level'),JSON_UNESCAPED_UNICODE) .'，找不到地址'. PHP_EOL;
                continue;
            }
//            echo json_encode($address,JSON_UNESCAPED_UNICODE).PHP_EOL;

            $arr = [
                'name' => $address['name'],
                'code' => $address['code'],
                'selectedAll' => $item['selectedAll'] ?? false,
                'children' => [],
            ];

            if (!empty($item['children']) && is_array($item['children'])) {
                $arr['children'] = $this->formatAddress($item['children'], $address['code'], $level + 1);
            }
            $resArr[] = $arr;
        }
        return $resArr;
    }

    protected function fixDelivery()
    {
        $shop = Shop::query()->where('id', 4310)->first();
        $orderService = OrderServiceManager::create(PlatformConst::PLATFORM_TYPE_MAP_REVERT[$shop->type]);
        $orderService->setShop($shop);
        $deliveryRecord = DeliveryRecord::query()->where(['shop_id'=>4310, 'wp_code'=>'JT'])->get();
        foreach ($deliveryRecord as $item) {
            try {
                $ret = $orderService->deliveryOrdersAgain($item['order_no'], $item['wp_code'], $item['waybill_code']);
                \Log::info('fix delivery order_no:' . $item['order_no'] . ' ret:' . json_encode($ret));
            } catch (\Exception $e) {
                \Log::error($e->getMessage(), ['order_no' => $item['order_no']]);
            }
        }
    }

    /**
     * 关闭没有快递模板的同步开关
     * <AUTHOR>
     */
    private function closeShopSyncSwitch()
    {
        Shop::query()->where('sync_switch', 1)->each(function ($shop){
            $exists = Template::query()->where('shop_id', $shop->id)->exists();
            if (!$exists) {
                $shop->sync_switch = 0;
                $shop->save();
                \Log::info('closeShopSyncSwitch', ['shop_id' => $shop->id]);
            }
        });
    }

    private function encryptOrder()
    {
        $shop_id = $this->option('shop_id');
        \App\Models\Fix\Order::query()->with('orderCipherInfo')
            ->where('shop_id', $shop_id)
            ->each(function ($order) {
                if (!is_numeric($order->receiver_phone)) {
                    echo 'not is_numeric:'.$order->receiver_phone . PHP_EOL;
                    return;
                }
                \DB::transaction(function () use ($order) {
                    $cipherInfo = [
                        'receiver_phone_ciphertext' => appEncrypt($order->receiver_phone),
                        'receiver_name_ciphertext' => appEncrypt($order->receiver_name),
                        'receiver_address_ciphertext' => appEncrypt($order->receiver_address),
                        'receiver_phone_mask' => dataDesensitizationForOpenApi($order->receiver_phone, 3, 4),
                        'receiver_name_mask' => dataDesensitizationForOpenApi($order->receiver_name, 1),
                        'receiver_address_mask' => addressDesensitization($order->receiver_address),
                    ];
                    OrderCipherInfo::query()->updateOrCreate([
                        'order_id' => $order->id,
                    ], $cipherInfo);

                    $order->receiver_phone = md5($order->receiver_phone);
                    $order->receiver_name = md5($order->receiver_name);
                    $order->receiver_address = md5($order->receiver_address);
                    $order->save();
                });

//                echo $order->tid . PHP_EOL;
//                return false;
            });
    }

    private function updateVersion()
    {
        $authService  = AuthServiceManager::create(config('app.platform'));
        $shopId = null;
        if ($this->option('shop_id')) {
            $shopId = $this->option('shop_id');
        }

        $query =$authService->selectAuthAvailableShops($shopId);
        $orderService  = OrderServiceManager::create();

        $query->orderByDesc('id');
        $query->each(function ($shop) use ($orderService) {
            echo $shop->id . PHP_EOL;
            // 设置授权信息
            $orderService->setShop($shop);
            // 保存订购关系表
            $orderService->saveUserEdition();
        });

    }
    private function fixOpenSubscribeMsg()
    {
        Shop::query()->where('sync_switch', 1)->where('expire_at','>',date('Y-m-d H:i:s'))
            ->each(function ($shop){
            $abstractOrderService = OrderServiceManager::create();
            $abstractOrderService->setShop($shop);
            $abstractOrderService->openSubscribeMsg();
        });

    }
}
