<?php

namespace App\Console\Commands;

use App\Jobs\Goods\SyncGoodsJob;
use App\Models\Shop;
use App\Services\Auth\AuthServiceManager;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class CronSyncGoodsCommand extends Command
{
    protected $signature   = 'command:cron-sync-goods  {--shop_id=}';
    protected $description = '定时同步店铺商品';
    const CHUNK_SIZE = 500;//每页500条查询

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $authService  = AuthServiceManager::create(config('app.platform'));
        $shopId = null;
        if ($this->option('shop_id')) {
            $shopId = $this->option('shop_id');
        }

        $query=$authService->selectAuthAvailableShops($shopId);

        $query->chunk(self::CHUNK_SIZE, function ($shops)  {
            $shops->each(function ($shop) {
                $redis    = redis('cache');
                $redisKey = 'sync_goods_run:' . $shop->id;
                $bool     = $redis->exists($redisKey);
                if ($bool) {
                    return false;
                }
                Log::info('shop', [$shop]);
                $beginLastGoodsSync=date('Y-m-d H:i:s');
                \Log::info("开始同步商品数据&清理历史商品",['shopId'=>$shop->id,"beginLastGoodsSync"=>$beginLastGoodsSync]);
                dispatch((new SyncGoodsJob($shop,1,$beginLastGoodsSync))->setAction('syncGoodsCommand'));
            });
        });
    }
}
