<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2021/3/29
 * Time: 17:55
 */

namespace App\Console\Commands;


use App\Jobs\Orders\DeleteOrderJob;
use App\Models\AbnormalOrder;
use App\Models\OperationLog;
use App\Models\Order;
use App\Models\OrderCipherInfo;
use App\Models\OrderTraceList;
use App\Models\Shop;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

/**
 * 清理过期的物流轨迹
 * Class ClearExpiredOrderCmd
 * @package App\Console\Commands
 */
class ClearExpiredOrderTraceCmd extends Command
{
    protected $signature = 'clear:expired-order-trace {--shop_id=}';

    protected $description = '清理过期的物流轨迹';

    public function handle()
    {
        $shopId = null;
        if ($this->option('shop_id')) {
            $shopId = $this->option('shop_id');
        }
        $where = [
            ['send_at' ,'<', date('Y-m-d H:i:s', strtotime('-90 day'))]
        ];
        if (!empty($shopId)) {
            $where[] = ['shop_id', $shopId];
        }
        $limit = 10000;
        $continue = true;

        while ($continue) {
            $count = OrderTraceList::query()->where($where)->limit($limit)->forceDelete();
            \Log::info("删除过期的物流轨迹,count=".strval($count),[$where]);
            if ($count < $limit) {
                $continue = false;
            }
            sleep(1);
        }
//        OrderCipherInfo::query()->where($commonWhere)->forceDelete();
        // 处理操作日志

    }


}
