<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2021/3/29
 * Time: 17:55
 */

namespace App\Console\Commands;


use App\Jobs\Orders\DeleteOrderJob;
use App\Models\AbnormalOrder;
use App\Models\OperationLog;
use App\Models\Order;
use App\Models\OrderCipherInfo;
use App\Models\OrderTraceList;
use App\Models\Shop;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

/**
 * 清理过期订单
 * Class ClearExpiredOrderCmd
 * @package App\Console\Commands
 */
class ClearExpiredOrderCmd extends Command
{
    protected $signature = 'clear:expired-order {--shop_id=}';

    protected $description = '清理过期的订单';

    public function handle()
    {
        \Log::info("开始清理过期订单");
        $shopId = null;
        if ($this->option('shop_id')) {
            $shopId = $this->option('shop_id');
        }
        //处理90天以前的数据
        $commonWhere= [['created_at' ,'<', date('Y-m-d H:i:s', strtotime('-90 day'))]];
        $orderDeadline= [['created_at' ,'<', date('Y-m-d H:i:s', strtotime('-180 day'))]];
        $where = [
            ['order_status','>=', Order::ORDER_STATUS_DELIVERED],
            ['created_at' ,'<', date('Y-m-d H:i:s', strtotime('-90 day'))]
        ];
        if (!empty($shopId)) {
            $shop = Shop::query()->where('id', $shopId)->first();
//            $where[] = ['user_id', $shop->user_id];
            $where[] = ['shop_id', $shopId];
        }
        //这里是删除了90天以前的非未发货订单，考虑到预发货的订单，所以不删除未发货订单
        Order::query()->where($where)->chunkById(500, function ($orders) {
            $idArr = collect($orders)->pluck('id')->toArray();
            \Log::info("删除90天非未发货（已发货，退款，完成）的订单,orderId=",$idArr);
            dispatch(new DeleteOrderJob($orders));
        });
        //这里是删除了180天以前所有的订单，包括未发货订单
        Order::query()->where($orderDeadline)->chunkById(500, function ($orders) {
            $idArr = collect($orders)->pluck('id')->toArray();
            \Log::info("删除180天过期的订单,orderId=",$idArr);
            dispatch(new DeleteOrderJob($orders));
        });

        $limit = 10000;
        $deleteCount = AbnormalOrder::query()->where($commonWhere)->forceDelete();
        \Log::info("删除90天以前的异常订单,数量=".strval($deleteCount));
        $continue = true;
        while ($continue) {
            $count = AbnormalOrder::query()->where($commonWhere)->limit($limit)->forceDelete();
            \Log::info("删除90天以前的异常订单,数量=".strval($deleteCount),$commonWhere);
            if ($count < $limit) {
                $continue = false;
            }
            sleep(1);
        }

        // 处理加密数据
//        OrderCipherInfo::query()->where($commonWhere)->forceDelete();
        // 处理操作日志
        $continue = true;
        while($continue) {
            $count = OperationLog::query()->where($commonWhere)->limit($limit)->forceDelete();
            \Log::info("删除90天以前的操作记录,count=".strval($count),$commonWhere);
            if($count < $limit) {
                $continue = false;
            }
            sleep(1);
        }

//        //删除order_items表里面没有对应orders的数据
//        $continue = true;
//        while($continue) {
//            $count = \DB::delete("delete from order_items where not exists (select 1 from orders where orders.id = order_items.order_id) limit 10000");
//            \Log::info("删除order_items表里面没有对应orders的数据,count=".strval($count));
//            if($count < $limit) {
//                $continue = false;
//            }
//            sleep(1);
//        }

    }


}
