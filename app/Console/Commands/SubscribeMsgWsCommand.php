<?php


namespace App\Console\Commands;

use App\Constants\PlatformConst;
use App\Constants\SubscribeMessageType;
use App\Exceptions\OrderException;
use App\Jobs\Orders\SyncSaveSubscribeMsg;
use App\Models\OrderTraceList;
use App\Services\Order\OrderServiceManager;
use GuzzleHttp\Client;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Workerman\Connection\AsyncTcpConnection;
use Workerman\Lib\Timer;
use Workerman\Worker;

/**
 * 消息订阅websocket版
 * Class SubscribeMsgWsCommand
 * @package App\Console\Commands
 */
class SubscribeMsgWsCommand extends Command
{
    protected $signature = 'command:subscribe-msg-ws {action} {--daemon}';

    protected $description = '消息订阅websocket版';
    /**
     * @var Worker
     */
    private $server;


    /**
     * Execute the console command.
     *
     * @return mixed
     * @throws OrderException
     */
    public function handle()
    {
        global $argv;

        if (!in_array($action = $this->argument('action'), ['start', 'stop', 'restart', 'reload', 'status', 'connections'])) {
            $this->error('Error Arguments');
            exit;
        }

        $argv[0] = 'command:subscribe-msg-ws';
        $argv[1] = $action;
        $argv[2] = $this->option('daemon') ? '-d' : '';

        $this->runWorker();
    }

    private function runWorker()
    {
        Worker::$logFile = '/tmp/workerman.log';
        $orderService = OrderServiceManager::create(PlatformConst::PDD);
        $worker = new Worker();

        $worker->onWorkerStart = function ($worker) use ($orderService) {
            try {
                $con = $orderService->createWebsocketConnection();
                $con->onConnect = function ($con) {
                    $str = $this->getActData('HeartBeat');
                    $con->send($str);

                    Timer::add(30, function () use ($con) {
                        $str = $this->getActData('HeartBeat');
                        $con->send($str);
                        \Log::info('消息订阅HeartBeat:' . $str);
                    });
                };
                $con->onMessage = function ($con, $data) use ($orderService) {
//                    echo $data . PHP_EOL;
                    \Log::debug('消息订阅处理前数据:'.$data);
                    $data = $orderService->handleSubscribeMsg($data);
                    \Log::debug('消息订阅处理后数据:', $data);

                    if (!empty($data)) {
                        switch ($data['type']) {
                            case SubscribeMessageType::ERROR:
                                break;
                            case SubscribeMessageType::ORDER:
                                break;
                            case SubscribeMessageType::LOGISTIC:
                                $this->saveLogisticTrace($data['body']);
                                break;
                        }
                    }
                    $con->send($this->getActData('Ack'));
//                    $con->send('success');
//                    return 'success';
                    // {"id":704946132070136,"commandType":"Common","time":1595258663828,"sendTime":1595258663561,"message":{"type":"pdd_trade_TradeConfirmed","mallID":211145469,"content":"{\"tid\":\"200720-167589479082287\",\"mall_id\":211145469}"}}
                };
                $con->onClose = function ($con) {
                    \Log::info('消息订阅关闭，尝试重连！');
                    // 如果连接断开，则在1秒后重连
                    $con->reConnect(1);
                };
                $con->connect();


            } catch (\Exception $exception) {
                \Log::debug('消息订阅出错:' . $exception->getMessage());
            }

        };
        // Run worker
        Worker::runAll();
    }

    /**
     * 分发所有项目
     * @param array $traceData
     */
    protected function saveLogisticTrace(array $traceData)
    {
        $allPrintApp = config('all_print_app');

        foreach ($allPrintApp as $app) {
            try {
                $client = new \GuzzleHttp\Client();
                $response = $client->post($app['domain'] . '/api/saveTrace', [
                        'json' => [
                            'trace_data' => $traceData
                        ],
                        'verify' => false,
                        'headers' => [
                            'Content-type' => 'application/json',
                            "Accept" => "application/json"
                        ],
                    ]
                );
                $result = json_decode($response->getBody()->getContents(), true);
                Log::info('物流轨迹分发详情' . $app['name'], [$result]);
            } catch (\Exception $e) {
                Log::error('物流轨迹分发错误', ['app' => $app, 'message' => $e->getMessage()]);
                continue;
            }
        }
    }

    /**
     * @param $commandType
     * @return string
     * <AUTHOR>
     */
    private function getActData($commandType)
    {
        $sendTime = 0;
        if ($commandType == 'Ack') {
            $sendTime = time() * 1000;
        }
        return json_encode([
            'id' => time() * 1000,
            'commandType' => $commandType,
            'time' => time() * 1000,
            'sendTime' => $sendTime,
        ]);
    }
}
