<?php

namespace App\Console\Commands\System;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
class ClearFailedJobsCmd extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:clear-failed-jobs';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '清理failed_jobs';


    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $before3Days=date("Y-m-d H:i:s", strtotime("-3 day"));
        $size=500;
        while(true) {
            $deleteCount = DB::delete("delete from failed_jobs where failed_at<? limit ?", [$before3Days, $size]);
            \Log::info("删除 failed_at",[$before3Days,$deleteCount,$size]);
            if($deleteCount<$size){
                return;
            }
        }

    }
}
