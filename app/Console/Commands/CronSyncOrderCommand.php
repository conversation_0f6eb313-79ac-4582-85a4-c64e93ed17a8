<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2020/3/14
 * Time: 16:29
 */

namespace App\Console\Commands;

use App\Constants\ErrorConst;
use App\Constants\PlatformConst;
use App\Exceptions\ApiException;
use App\Jobs\Orders\SyncSaveOrders;
use App\Models\ApiShopBind;
use App\Models\Company;
use App\Models\Order;
use App\Models\Shop;
use App\Models\SystemConfig;
use App\Services\Auth\AuthServiceManager;
use App\Services\Order\OrderRemarkService;
use App\Services\Order\OrderServiceManager;
use App\Services\Order\OrderSyncService;
use App\Utils\Environment;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Throwable;

/**
 * 定时同步订单
 * Class CronSyncOrderCommand
 * @package App\Console\Commands
 */
class CronSyncOrderCommand extends Command
{

    protected $signature = 'command:cron-sync-order {--shop_ids=} {--start_time=} {--platform=}';
    protected $description = '定时同步商家订单';

    const PAGE_SIZE = 100;//每页500条查询
    const MAX_SYNC_TIME = 86400;// 最大同步时间（秒）
    const LIMIT_COUNT = Order::SYNC_ORDER_LIMIT;     //同步上限
    const DELAY_TIME = 60;      //超过100条延迟2分钟发放
    public $timeout = 3600;
    protected $isUpdateLastSyncAt = true;
    private $logPrefix = 'CronSyncOrderCommand';


    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $switch = SystemConfig::getValue(SystemConfig::KEY_SYNC_SWITCH);
        if (!is_null($switch) && $switch != 1) {
            // 系统同步订单开关
            Log::info('系统同步订单开关关闭');
            return;
        }
        $shopIdArr = [];
        $start_time = $this->option('start_time');

        if ($this->option('shop_ids')) {
            $shopIdArr = explode(',', $this->option('shop_ids'));
        }
        // 手动指定店铺不跳过
        if (empty($shopIdArr) && ((Environment::isDy() && config('app.name') != '{dy-kxdd}') || Environment::isJd())) {
            $this->logInfo('数据推送跳过', []);
            return;
        }
        $current_hour = date('G'); // 获取当前时间的小时数，使用 G 格式获取 24 小时制的小时数
        if ($current_hour >= 1 && $current_hour <= 7) {
            $current_minute = (int)date('i');
            if (($current_minute % 30) >= 5) {
                // 01-07点 每30分钟，只在 0-4分钟才执行同步
                $this->logInfo('低峰期跳过', [
                    'date' => date('Y-m-d H:i:s'),
                ]);
                return;
            }

        }
        $platform = $this->option('platform');
        if (!isset($platform)) {
            //如果入参没有指定就默认取配置的
            $platform = config('app.platform');
        } else {
            $platform = strtolower($platform);
        }
        $authService = AuthServiceManager::create($platform);
        $platformType = $authService->getPlatformType();

        $query = $authService->selectAuthAvailableShops($shopIdArr);
        $querySupplement = clone $query;
        // 设定上限,防止一直执行
//        $query->where('last_sync_at', '<', Carbon::now()->toDateTimeString());
        $query->where(DB::raw(' IFNULL(last_sync_at, 0)'), '<', Carbon::now()->toDateTimeString());
        $this->isUpdateLastSyncAt = true;
        $count=$query->count();
        Log::info("开始执行定时同步订单 platform={$platform} count={$count}");
        $query->chunkById(1000, function ($shops) use ($platformType, $start_time, $platform) {
            $shopIds = $shops->pluck('id')->toArray();
            $apiShopBindCountArr = ApiShopBind::query()->whereIn('shop_id', $shopIds)->selectRaw('shop_id, count(*) as count')
                ->groupBy(['shop_id'])->get()->pluck('count', 'shop_id')->toArray();
            $companyCountArr = Company::query()->whereIn('shop_id', $shopIds)->selectRaw('shop_id, count(*) as count')
                ->groupBy(['shop_id'])->get()->pluck('count', 'shop_id')->toArray();
            foreach ($shops as $shop) {
                try {
                    $redis = redis('cache');
                    $redisKey = 'sync_order_run:' . $shop->user_id;
                    $bool = $redis->exists($redisKey);

                    $redisKey2 = 'command:cron-sync-order:' . $shop->id;
                    // $exTime 分钟内不会重复执行
                    $exTime = 60 * 4;
                    if (Environment::isTaoBao() || Environment::isAlbb()) {
                        $exTime = 60 * 1; // 淘宝1分钟
                    }
                    $isSet = $redis->set($redisKey2, 1, 'nx', 'ex', $exTime);
//                if ($bool || !$isSet || empty($shop->access_token) ) {
                    if (empty($shop->access_token)) {
//                    \Log::info('CronSyncOrderCommand:重复执行跳过', [
//                        'shop_id' => $shop->id,
//                        'bool' => $bool,
//                        'bool2' => $bool2,
//                        'access_token' => $shop->access_token,
//                        ]);
                        $this->logInfo('重复执行跳过', [
                            'shop_id' => $shop->id,
                            'bool' => $bool,
                            'isset' => $isSet,
                            'access_token' => $shop->access_token,
                        ]);
                        continue;
                    }
                    // 是api用户且没有绑定电子面单，跳过
                    if (!empty($apiShopBindCountArr[$shop->id]) && empty($companyCountArr[$shop->id])) {
                        if (!in_array(config('app.platform'), ['{albb-kddd}'])) { // albb-kddd 跳过这个逻辑
                            $this->logInfo('api用户跳过', [
                                'shop_id' => $shop->id,
                            ]);
                            continue;
                        }
                    }

                    $shop = Shop::firstById($shop->id);
                    $isLimit = OrderSyncService::isLimitSyncOrder4Shop($shop->id);
                    $count = Order::countByUnshipped($shop->id);
                    if ($isLimit && $count > self::LIMIT_COUNT) {
//                    \Log::info('CronSyncOrderCommand:订单超过上线，不同步', ['shop_id' => $shop->id,'count'=>$count]);
                        $this->logInfo('订单超过上线', [
                            'shop_id' => $shop->id,
                            'count' => $count,
                            'limit' => self::LIMIT_COUNT,
                        ]);
                        continue;
                    }
                    //首次授权拉取未付款
                    $isFirstPull = false;
                    if (is_null($shop->last_sync_at)) {
                        $isFirstPull = true;
                    }
                    if (empty($shopIdArr) && Environment::isDy() && (empty($shop->last_sync_at) || (time() - strtotime($shop['last_sync_at'])) > 86400)) {
                        $this->logInfo('同步间隔太久跳过', [
                            'shop_id' => $shop->id,
                            'last_sync_at' => $shop['last_sync_at'],
                        ]);
                        continue;
                    }
                    if (!empty($start_time)) { // 手动指定开始时间
                        $beginAt = $start_time;
                    } else {
                        $beginAt = $shop->last_sync_at;
                        if (is_null($beginAt)) { // 如果是空的，取3天前
                            $beginAt = date('Y-m-d H:i:s', strtotime('-3 day'));
                        }
                    }

                    $endAt = date('Y-m-d H:i:s', strtotime('-30 second'));
                    if (strtotime($beginAt) < strtotime($endAt)) {
                        $this->handleSyncOrders($shop, $beginAt, $endAt, $isFirstPull, $platform);
                        $this->handleSyncRemarks($shop, $beginAt, $endAt);
                    }
                } catch (Throwable $throwable) {
                    $this->logError('CronSyncOrderCommand:异常', [
                        'shop_id' => $shop->id,
                        'message' => $throwable->getMessage(),
                        'file' => $throwable->getFile(),
                        'line' => $throwable->getLine(),
                        'trace' => $throwable->getTraceAsString(),
                    ]);
                }

                }
            });

            $this->isUpdateLastSyncAt = false;
            $this->logPrefix = 'CronSyncOrderCommand2';
//        if (in_array(config('app.platform'), [PlatformConst::DY, PlatformConst::KS])) {
//        if (config('app.platform') != PlatformConst::TAOBAO) { // 淘宝不补单
            if (true) {
                // 补充同步（补单）
                $querySupplement->chunkById(1000, function ($shops) use ($platformType, $platform) {
                    $shopIds = $shops->pluck('id')->toArray();
                    $apiShopBindCountArr = ApiShopBind::query()->whereIn('shop_id', $shopIds)->selectRaw('shop_id, count(*) as count')
                        ->groupBy(['shop_id'])->get()->pluck('count', 'shop_id')->toArray();
                    $companyCountArr = Company::query()->whereIn('shop_id', $shopIds)->selectRaw('shop_id, count(*) as count')
                        ->groupBy(['shop_id'])->get()->pluck('count', 'shop_id')->toArray();

                    foreach ($shops as $shop) {
                        $beginAt = date('Y-m-d H:i:s', strtotime('-30 minute'));
                        $endAt = date('Y-m-d H:i:s', strtotime('-20 minute'));

                        // 是api用户且没有绑定电子面单，跳过
                        if (!empty($apiShopBindCountArr[$shop->id]) && empty($companyCountArr[$shop->id])) {
                            $this->logInfo('api用户跳过', [
                                'shop_id' => $shop->id,
                            ]);
                            continue;
                        }
                        if (strtotime($beginAt) < strtotime($endAt)) {
                            $this->handleSyncOrders($shop, $beginAt, $endAt, false, $platform);
                        }
                    }
                });
            }

            return;
        }

    /**
     * 同步备注
     * @param $shop
     * @param $beginAt
     * @param $endAt
     * @return void
     */
    public function handleSyncRemarks($shop, $beginAt, $endAt)
    {
        try {
            $orderRemarkService = new OrderRemarkService();
            $orderRemarkService->handleSyncRemarks($shop, $beginAt, $endAt);
        } catch (\Throwable $ex) {
            Log::error("同步备注异常", ["exception" => $ex, "shop" => $shop, "beginAt" => $beginAt, "endAt" => $endAt]);
        }
    }

    public function handleSyncOrders(Shop $shop, $beginAt, $endAt, $isFirstPull = false, string $platform = null)
    {
        Log::info("同步时间范围内的店铺订单", ["shop_id" => $shop->id, "beginAt" => $beginAt, "endAt" => $endAt, "platform" => $platform]);
        $userId = $shop->user_id;
        $orderService = OrderServiceManager::create($platform);
//        $orderService->setUserId($userId);
        $orderService->setShop($shop);
        $orderService->setAccessToken($shop->access_token);
        // 根据时间间隔 计算循环次数
        $len = ceil((strtotime($endAt) - strtotime($beginAt)) / 60 / $orderService->orderIncrTimeInterval);
        $totalCount = 0;
        $delayCounter = 0;

        $currentBeginAt = $beginAt;
        try {
            for ($i = 0; $i < $len; $i++) {
                $orderService->initPage();
                //拉取范围
                $orderTimeInterval = $orderService->orderIncrTimeInterval;
                $addMinutes = $i * $orderTimeInterval;
                $startAt = date('Y-m-d H:i:s', strtotime("+$addMinutes minute", strtotime($beginAt)));

                $currentBeginAt = $startAt;
                $startTime = strtotime($startAt);
                $endTime = strtotime("+$orderTimeInterval minute", $startTime);
                //时间超出当前时间
                if ($endTime > strtotime($endAt)) {
                    $endTime = strtotime($endAt);
                }
                $this->logInfo('beforeDo', [
                    'shop_id' => $shop->id,
                    'startAt' => $startAt,
                    'endAt' => date('Y-m-d H:i:s', $endTime),
                    'isUpdateLastSyncAt' => $this->isUpdateLastSyncAt ? 1 : 0,
                ]);
//                \Log::info('CronSyncOrderCommand:beforeDo:' . $shop->id . '-' . $i . '-' . date('Y-m-d H:i:s', $startTime)
//                    . '-' . date('Y-m-d H:i:s', $endTime).':isUpdateLastSyncAt:'.$this->isUpdateLastSyncAt);
                do {
                    $orders = $orderService->getTradesOrderByIncr($startTime, $endTime, $isFirstPull);
                    //取消授权终止
                    if (isset($orders['code']) && in_array($orders['code'], $orderService->errorCodeArr)) {
                        if ($i == $len - 1 && $this->isUpdateLastSyncAt) {
                            //还原时间，修改授权状态
                            if ($orders['code'] == 1000) {
                                $this->logInfo('code1000', [
                                    'shop_id' => $shop->id,
                                ]);
//                                \Log::info("updateLastSync:",[$shop->id]);
                                Shop::query()->where('id', $shop->id)
                                    ->update([
                                        'last_sync_at' => $beginAt,
                                        'auth_status' => Shop::AUTH_STATUS_ABNORMAL_EXPIRE,
                                    ]);
                            }
                            if ($orders['code'] == 1001) {
                                Shop::updateLastSync($shop->id, $beginAt);
                            }
                        }
//		                \Log::info('用户授权失效[order]', ['result' => $orders, 'shop_id' => $shop->id, 'backAt' => $beginAt]);
                        $this->logInfo('用户授权失效[order]', [
                            'shop_id' => $shop->id,
                            'result' => $orders,
                            'backAt' => $beginAt,
                        ]);

                        break;
                    }

//                    \Log::info('CronSyncOrderCommand:' . $shop->id . ':count:' . count($orders) . ':page:' .
//                        $orderService->getPage() . ':start:' . date('Y-m-d H:i:s', $startTime) . ':end:' .
//                        date('Y-m-d H:i:s', $endTime));
                    $this->logInfo('count', [
                        'shop_id' => $shop->id,
                        'count' => count($orders),
                        'page' => $orderService->getPage(),
                        'start' => date('Y-m-d H:i:s', $startTime),
                        'end' => date('Y-m-d H:i:s', $endTime),
                    ]);
                    if (empty($orders)) {
//                        \Log::info("没有需要更新的订单");
                        break;
                    }
                    $logOrders = array_map(function ($item) {
                        return array_only($item, ['tid', 'order_status', 'order_updated_at']);
                    }, $orders);
//                    \Log::info('CronSyncOrderCommand:orders:'.$shop->id, ['orders' => $logOrders]);
                    $this->logInfo('log_orders', [
                        'shop_id' => $shop->id,
                        'orders' => $logOrders,
                    ]);

                    // 翻页
                    $orderService->pageTurning();

                    Log::info(class_basename($this) . ':newSyncSaveOrders');
                    $syncSaveOrdersJob = new SyncSaveOrders(
                        $userId,
                        $shop->id,
                        $orders,
                        $shop->getPlatform()
                    );
//                    dispatch(($syncSaveOrdersJob));
                    $syncSaveOrdersJob->handle();
                    $isLimit = OrderSyncService::isLimitSyncOrder4Shop($shop->id);
                    $totalCount += count($orders);
                    if ($isLimit && $totalCount > self::LIMIT_COUNT) {
                        if ($this->isUpdateLastSyncAt) {
                            $updateTime = collect($orders)->sortByDesc('order_updated_at')->first()['order_updated_at'];
                            Shop::updateLastSync($shop->id, $updateTime);
                        }
//                        \Log::info('CronSyncOrderCommand:订单超过上线，不同步2', ['shop_id' => $shop->id, 'totalCount' => $totalCount]);
                        $this->logInfo('订单超过上线2', [
                            'shop_id' => $shop->id,
                            'totalCount' => $totalCount,
                        ]);
                        return;
                    }
                } while ($orderService->hasNext);
                // 每次跑修改最后同步时间
                if ($this->isUpdateLastSyncAt) {
                    Shop::updateLastSync($shop->id, date('Y-m-d H:i:s', $endTime));
                }
            }
        } catch (\Exception $e) {
            if ($e instanceof ApiException) {
                // 授权关闭
                if (in_array($e->getCode(), [ErrorConst::PLATFORM_SHOP_AUTH_CANCELED[0], ErrorConst::PLATFORM_SHOP_AUTH_EXPIRED[0]])) {
                    Shop::updateAuthStatus($shop->id, Shop::AUTH_STATUS_ABNORMAL_EXPIRE);
                }
            }
//            if ($this->isUpdateLastSyncAt) {
//                //还原最后拉取时间&修改授权状态
//                Shop::updateLastSync($shop->id, $currentBeginAt, false);
//            }
//            \Log::error('CronSyncOrderCommand:'.$e->getMessage(), [$shop, $beginAt, $endAt]);
            $this->logError($e->getMessage(), [
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTrace(),
                'shop' => $shop,
                'beginAt' => $beginAt,
                'endAt' => $endAt,
            ]);
        }
    }

    private function logInfo(string $string, array $array)
    {
        \Log::info($this->logPrefix . ':' . $string, $array);
    }

    private function logError(string $getMessage, array $array)
    {
        \Log::error($this->logPrefix . ':' . $getMessage, $array);
    }
}
