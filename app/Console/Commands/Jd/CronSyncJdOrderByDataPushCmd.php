<?php

namespace App\Console\Commands\Jd;

use App\Models\Fix\Shop;
use App\Models\Jd\YdPopOrder;
use App\Models\Order;
use App\Services\Order\Impl\JdOrderImpl;
use App\Services\Order\OrderServiceManager;
use App\Utils\Environment;
use App\Utils\LogUtil;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * JD 订单同步
 */
class CronSyncJdOrderByDataPushCmd extends Command
{
    protected $signature = 'command:syncJdOrderByDataPush {--shop_id=}';
    protected $description = '定时同步订单';

    public function handle()
    {
        $log = LogUtil::jdDbSync();
        if (!Environment::isJd()) {
            $log->info('非JD跳过');
            return;
        }
        ini_set('memory_limit', '1024M');
        $this->info('start:' . date('Y-m-d H:i:s'));
        // 查出订单
        $redis = redis('cache');
        $redisKey = 'syncOrderByDataPush:lastTime';
        // 禁用查询日志，避免溢出
//        DB::connection()->disableQueryLog();
//        DB::connection('jd_data_push')->disableQueryLog();
//        $index=100;
        do {
            try {
                $lastTime = max($redis->get($redisKey),'2024-10-01 00:00:00');
                // 预留 1 秒的缓冲时间
                $lastTime = Carbon::parse($lastTime)->subSeconds(1)->toDateTimeString();
                $log->info('syncJdOrderByDataPush:start', [$lastTime]);
                $query = YdPopOrder::query();
                if (!empty($lastTime)) {
                    $query->where('pushModified', '>=', $lastTime);
                }
//            $query->where('order_id','6933222351165921200');
                $query->orderBy('pushModified');
                // 订单同步内测
//            $query->whereIn('shop_id', [4500272, 12682604, 607958, 42129741, 85185732,46940491]);
                $shopId = $this->option('shop_id');
                if ($shopId) {
                    $query->whereIn('venderId', [$shopId]);
                }
                $limit = 4000;

                $popOrders = $query->limit($limit)->get();
                $maxPushModified = $popOrders->max('pushModified');
                $maxPushModified = $maxPushModified ? Carbon::parse($maxPushModified)->toDateTimeString() : null;
                $count = $popOrders->count();
                $log->info("查询SQL",[$query->toSql(),"lastTime"=>$lastTime,'maxPushModified' => $maxPushModified, 'count' => $count]);
                $popOrders->groupBy('venderId')->each(function ($items, $serviceId) use ($maxPushModified, $log) {
                    $orders=[];
                    $shopId=0;
                    try {
                        $shop = Shop::firstByServiceId($serviceId);
                        if (empty($shop)) {
                            $log->info('店铺不存在:' . $serviceId);
                            return;
                        }
                        $shopId=$shop->id;
//                \Log::info('syncOrderByDataPush:group', ['shop_id' => $shop->id, 'count' => count($items)]);
                        /**
                         * @var JdOrderImpl $orderService;
                         */
                        $orderService = OrderServiceManager::create();
                        $orderService->setShop($shop);
                        // 提取订单数据
                        $orders = collect($items)->pluck('responseJson')->map(function ($itemJson) {
                            return  json_decode($itemJson, true);

                        })->toArray();
//                        $log->info('同步到订单',$orders[0]);
                        $batchSellerMemos=$orderService->getBatchSellerMemos($orders);
                        $batchSellerMemos = array_pluck($batchSellerMemos, null, 'orderId');
                        $orders = array_map(function ($orderInfo) use ($batchSellerMemos) {
                            return array_merge($orderInfo, $batchSellerMemos[$orderInfo['orderId']] ?? []);
                        }, $orders);
                        $orders=$orderService->getBatchSkuInfo($orders);
                        $tids = array_column($orders, 'orderId');
                        $refundApplies = $orderService->batchGetRefundAppliesByTid($tids);
                        $orders = array_map(function ($orderInfo) use ($refundApplies) {
                            $refundApplies = $refundApplies[$orderInfo['orderId']] ?? [];
                            return array_merge($orderInfo, ["refundApplies" => $refundApplies]);
                        }, $orders);
                        $orders = $orderService->formatToOrders($orders);
                        $log->info('syncJdOrderByDataPush:tidArr', ['shop_id' =>$shopId, 'count' => count($items), 'tidArr' => array_pluck($orders, 'tid')]);
                        Order::batchSave($orders, $shop->user_id, $shop->id,$shop->getPlatform());
                    } catch (\Throwable $e) {
                        $log->error('syncJdOrderByDataPush:error', ['e' => $e,"shopId"=>$shopId  ,"orders"=>$orders]);
                    }
//                dispatch((new SyncSaveOrders(
//                    $shop->user_id,
//                    $shop->id,
//                    $orders
//                )));
                });
                if (!empty($maxPushModified)) {
                    $redis->set($redisKey, $maxPushModified);
                }
                $log->info('syncJdOrderByDataPush:redis set', ["count"=>$count,'maxPushModified' => $maxPushModified]);
                if ($count <= 500) { // 数据少就间隔长一点
                    sleep(5);
                }else {
                    sleep(1);
                }
            } catch (\Throwable $e) {
                $log->error('syncJdOrderByDataPush:error', ['e' => $e]);
            }

        } while (true);

    }
}
