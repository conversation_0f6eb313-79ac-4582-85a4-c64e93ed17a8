<?php

namespace App\Console\Commands;

use App\Models\Order;
use App\Models\OrderItem;
use Illuminate\Console\Command;

class FixOrderRelationCommand extends Command
{
    protected $signature   = 'command:fixOrderRelationCommand {--shop_id=}';
    protected $description = '定时同步商家订单';

    public function handle()
    {
        OrderItem::query()->where('order_id', 0)
            ->chunk(100, function ($items) {
                foreach ($items as $item) {
                    try {
                        $order = Order::where('tid', $item->oid)->first();
                        if ($order) {
                            $item->user_id  = $order->user_id;
                            $item->shop_id  = $order->shop_id;
                            $item->order_id = $order->id;
                            $item->save();
                        }
                    } catch (\Exception $exception) {
                        \Log::error('订单关联修复失败', [$item]);
                        continue;
                    }
                }
            });
    }
}
