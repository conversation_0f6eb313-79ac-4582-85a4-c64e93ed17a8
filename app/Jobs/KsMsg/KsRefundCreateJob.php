<?php


namespace App\Jobs\KsMsg;


use App\Jobs\Job;
use App\Jobs\Orders\SyncSaveRefundOrders;
use App\Models\Shop;
use App\Services\Order\OrderServiceManager;
use Illuminate\Support\Facades\Log;

class KsRefund<PERSON><PERSON>Job extends Job
{
    protected $msg;

    public function __construct(array $msg)
    {
        $this->msg = $msg;
    }

    public function handle()
    {
//        Log::debug('售后申请消息', $this->msg);

        $msg        = $this->msg;
        $data       = $msg['info'];

//        $shop       = Shop::query()->where('identifier', $msg['userId'])->firstOrFail();
        $shop       = Shop::firstByIdentifier($msg['userId']);
        if ($shop->auth_status == Shop::AUTH_STATUS_SUCCESS) {
            $orderService = OrderServiceManager::create(config('app.platform'));
            $orderService->setUserId($shop->user_id);
            $orderService->setShop($shop);
            $refundOrder = $orderService->getRefundOrderInfo($data['refundId']);

            if ($refundOrder) {
                dispatch((new SyncSaveRefundOrders(
                    $shop->user_id,
                    [$refundOrder]
                )));
            }
        }
    }
}
