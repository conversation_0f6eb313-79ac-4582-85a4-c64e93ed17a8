<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2021/3/19
 * Time: 16:10
 */

namespace App\Jobs\Orders;


use App\Jobs\Job;
use App\Models\Shop;
use App\Services\Order\OderLogicService;
use App\Services\Order\OrderServiceManager;
use GuzzleHttp\DefaultHandler;
use Illuminate\Support\Facades\Log;
use Yurun\Util\Swoole\Guzzle\SwooleHandler;

class SyncOrderByScopeJob extends Job
{
    public $queue = 'swoole';
    private $shopId;
    private $beginTime;
    private $endTime;
    public $timeout = 240;
    protected $isFirstPull = false;

    /**
     * @var string
     */
    private $action;
    /**
     * @var bool
     */
    protected $isSyncLegacyOrder = false;
    private $isSaveLastSync = false;

    /**
     * SyncOrderByScopeJob constructor.
     * @param $shopId
     * @param string $beginTime
     * @param string $endTime
     * @param string $action
     */
    public function __construct($shopId, string $beginTime, string $endTime, string $action = '')
    {
        $this->shopId = $shopId;
        $this->beginTime = $beginTime;
        $this->endTime = $endTime;
        $this->action = $action;
    }

    public function handle()
    {
//        $shopId = $this->argument('shop_id');
//        $beginTime = $this->argument('begin_time');
//        $endTime = $this->argument('end_time');

        $oderLogicService = new OderLogicService();
        $shop = Shop::query()->findOrFail($this->shopId);
        $orderService = OrderServiceManager::create($shop->getPlatform());
        $oderLogicService->setOrderService($orderService);
        $oderLogicService->setAction($this->action);
        $oderLogicService->setIsFirstPull($this->isFirstPull);
        $oderLogicService->setIsSaveLastSync($this->isSaveLastSync);
        $oderLogicService->setIsSyncLegacyOrder($this->isSyncLegacyOrder);
        Log::info("SyncOrderByScopeJob init:$this->shopId:time:" . $this->endTime . '  ' . $this->beginTime);
        $oderLogicService->handleSyncOrders($shop, $this->beginTime, $this->endTime);
//        $orderCount = $oderLogicService->getOrderCount();

    }

    /**
     * @param bool $isFirstPull
     * @return SyncOrderByScopeJob
     */
    public function setIsFirstPull(bool $isFirstPull): SyncOrderByScopeJob
    {
        $this->isFirstPull = $isFirstPull;
        return $this;
    }

    public function setIsSyncLegacyOrder(bool $isSyncLegacyOrder): SyncOrderByScopeJob
    {
        $this->isSyncLegacyOrder = $isSyncLegacyOrder;
        return $this;
    }

    /**
     * @param bool $isSaveLastSync
     */
    public function setIsSaveLastSync(bool $isSaveLastSync): void
    {
        $this->isSaveLastSync = $isSaveLastSync;
    }
}
