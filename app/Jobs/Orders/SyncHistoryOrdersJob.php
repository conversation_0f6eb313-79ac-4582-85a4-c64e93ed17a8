<?php

namespace App\Jobs\Orders;

use App\Exceptions\OrderException;
use App\Jobs\Job;
use App\Models\Order;
use App\Models\Shop;
use App\Services\Order\OrderServiceManager;

/**
 *  订单同步
 */
class SyncHistoryOrdersJob extends Job
{
    protected $userId;
    protected $shopId;
    protected $accessToken;
    protected $beginAt;
    protected $endAt;
    protected $service;
    /**
     * @var Shop
     */
    private $auth;

    /**
     *   constructor.
     * @param Shop   $auth
     * @param string $beginAt
     * @param string $endAt
     */
    public function __construct(Shop $auth, string $beginAt, string $endAt)
    {
        $this->auth = $auth;
        $this->userId = $this->auth->user_id;
        $this->shopId = $this->auth->id;
        $this->accessToken = $this->auth->access_token;
        $this->beginAt = $beginAt;
        $this->endAt = $endAt;
        $this->service = OrderServiceManager::create(config('app.platform'));
        $this->service->setShop($auth);
    }

    /**
     * 授权后订单处理
     * @throws OrderException
     */
    public function handle()
    {
        $conditions = [
            ['shop_id', $this->shopId],
            ['order_status', Order::ORDER_STATUS_PAYMENT],
            ['refund_status', Order::REFUND_STATUS_NO],
            ['pay_at', '>=', $this->beginAt],
            ['pay_at', '<', $this->endAt]
        ];

        $count = Order::query()->where($conditions)->count();
        \Log::info('SyncHistoryOrdersJob count:'.$count);
        if ($count > 0) {
            Order::query()->where($conditions)->chunkById(500, function ($orders){
                $tids = collect($orders)->pluck('tid')->toArray();
                foreach ($tids as $tid) {
                    $data = $this->service->getOrderInfo($tid);
                    if (empty($data)) {
                        \Log::info('同步订单任务 拉取订单详情失败, order_id：'.$tid);
                        continue;
                    }
                    //状态不一致修改状态
                    if ($data['order_status'] != Order::ORDER_STATUS_PAYMENT) {
                        Order::query()
                            ->where('tid', $tid)
                            ->update(['order_status'=>$data['order_status']]);
                    }
                }
            });
        }
    }
}
