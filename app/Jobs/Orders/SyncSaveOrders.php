<?php

namespace App\Jobs\Orders;

use App\Constants\PlatformConst;
use App\Jobs\Job;
use App\Models\Order;

/**
 *
 */
class SyncSaveOrders extends Job
{
    public $queue = 'high';

    public $timeout = 240;
    /**
     * 任务可以尝试的最大次数。
     *
     * @var int
     */
    public $tries = 3;
    private $orders;
    /**
     * @var int
     */
    private $userId;
    private $shopId;
    private $action = '';

    /**
     * @var ?string $platform 平台类型
     */
    private $platform;

    /**
     * @param int $userId
     * @param int $shopId
     * @param array $orders
     * @param string|null $platform
     */
    public function __construct(int $userId, int $shopId, array $orders,?string $platform=null)
    {
        $this->userId = $userId;
        $this->shopId = $shopId;
        $this->orders = $orders;
        $this->platform = $platform;
    }

    /**
     * 授权后订单处理
     * @return bool
     * @throws \Exception
     * @throws \Throwable
     * @throws \Psr\SimpleCache\InvalidArgumentException
     */
    public function handle()
    {
        $start=microtime(true);

        if (empty($this->orders) || (count($this->orders) == 1 && empty($this->orders[0]))) {
            // 过滤掉 [] 和 [null]
            return true;
        }
//        $redis = redis();
//        $redisKey = 'sync_order_run:' . $this->userId;
//        $redis->setex($redisKey, 5 * 60, time());
//        \Log::info('SyncSaveOrders', $this->orders);

        Order::batchSave($this->orders, $this->userId, $this->shopId,$this->platform);
        $redis = redis('cache');
        if (!empty($this->action)) {
            $key = "$this->action:sync_current:{$this->shopId}";
            $redis->incrby($key, count($this->orders));
        }
        $cost = round((microtime(true) - $start) * 1000, 2);
//        \Log::info("SyncSaveOrders.performance",[$cost,sizeof($this->orders)]);

        return true;
    }

    /**
     * @param string $action
     * @return SyncSaveOrders
     */
    public function setAction(string $action): SyncSaveOrders
    {
        $this->action = $action;
        return $this;
    }
}
