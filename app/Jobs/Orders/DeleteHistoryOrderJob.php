<?php
namespace App\Jobs\Orders;

use App\Jobs\Job;
use App\Models\Shop;
use App\Models\Order;
use App\Models\OrderItem;

class DeleteHistoryOrderJob extends Job
{
    protected $userId;
    protected $shopId;
    protected $accessToken;
    protected $beginAt;
    protected $endAt;

    /**
     * @var Shop
     */
    private $auth;

	public function __construct(Shop $auth, string $beginAt, string $endAt)
	{
        $this->auth = $auth;
        $this->userId = $this->auth->user_id;
        $this->shopId = $this->auth->id;
        $this->accessToken = $this->auth->access_token;
        $this->beginAt = $beginAt;
        $this->endAt = $endAt;
	}

	public function handle()
	{
        $conditions = [
//            ['user_id', $this->userId],
            ['shop_id', $this->shopId],
            ['order_status', Order::ORDER_STATUS_PAYMENT],
            ['refund_status', Order::REFUND_STATUS_NO],
            ['pay_at', '>=', $this->beginAt],
            ['pay_at', '<', $this->endAt]
        ];

        $count = Order::query()->where($conditions)->count();
        \Log::info('DeleteHistoryOrderJob count:'.$count);
        if ($count > 0) {
            Order::query()->where($conditions)->chunkById(500, function ($orders){
                $tids = collect($orders)->pluck('tid')->toArray();
                Order::query()->whereIn('tid', $tids)->forceDelete();
                OrderItem::query()->whereIn('tid', $tids)->forceDelete();
            });
        }
	}
}
