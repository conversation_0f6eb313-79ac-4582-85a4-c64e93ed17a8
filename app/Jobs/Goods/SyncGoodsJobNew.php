<?php

namespace App\Jobs\Goods;

use App\Constants\PlatformConst;
use App\Jobs\Job;
use App\Models\Goods;
use App\Models\OrderItem;
use App\Models\Shop;
use App\Services\Goods\GoodsServiceManager;
use Illuminate\Support\Facades\Log;

class SyncGoodsJobNew extends Job
{
    protected $shop;
    protected $action;
    public $timeout = 600;
    protected $beginLastGoodsSync;

    public function __construct(Shop $shop, $beginLastGoodsSync = null)
    {
        $this->shop = $shop;
        $this->beginLastGoodsSync = $beginLastGoodsSync;
    }

    public function handle()
    {
        try {
            $shop = $this->shop;
            $shop_id = $shop->id;
            $redis = redis('cache');
            Shop::updateLastGoodsSync($shop_id, date('Y-m-d H:i:s'));
            //获取需要同步的商品列表
            $goodsService = GoodsServiceManager::newInstance($shop->access_token, $shop);
            // 需要同步总数量
            $query = OrderItem::query()->where('shop_id', $shop_id)->groupBy('num_iid');
            $baseSql = getSqlByQuery($query);
            $count = \DB::select("select count(*) as count from ($baseSql) as base");
            $goodsTotalCount = $count[0]->count;
            $key = "$this->action:sync_total:$shop_id";
            $redis->setex($key, 86400, $goodsTotalCount);
            // 当前同步的数量
            $key = "$this->action:sync_current:$shop_id";
            $redis->setex($key, 86400, 0);

            $key = "$this->action:sync_finish:$shop_id";
            $redis->setex($key, 86400, 1);
            OrderItem::query()->where('shop_id', $shop_id)->groupBy('num_iid')->chunk(100, function ($orderItems) use ($goodsService, $shop) {
                $goodsIds = collect($orderItems)->pluck('num_iid')->toArray();
                //根据商品id获取商品详情
                $result = $goodsService->getGoodsListByGoodsId($goodsIds);
                \Log::Info('syncGoods.shopId='. $shop->id .' count='.count($result).' goodsIds='.json_encode($goodsIds));
                if (!empty($result)) {
                    dispatch((new SaveGoodsJob($result, $shop->user_id, $shop->id))->setAction($this->action));
                }
            });
            if (isset($this->beginLastGoodsSync)) {
                dispatch(new ClearGoodsJob($shop, $this->beginLastGoodsSync));
            }
        } catch (\Exception $e) {
            \Log::error("syncGoods.shopId=" . $shop_id . " 失败 message=" . $e->getMessage(), [$e->getTrace()]);
        }


    }

    /**
     * @param mixed $action
     * @return SyncGoodsJobNew
     */
    public function setAction($action): SyncGoodsJobNew
    {
        $this->action = $action;
        return $this;
    }

    /**
     * @param $goodsService
     * @param int $pageNo
     * @return bool
     */
    protected function canContinue($goodsService, int $pageNo): bool
    {
        return $goodsService->hasNext && ($goodsService->getMaxSyncGoodsCount() === null || ($goodsService->getMaxSyncGoodsCount() !== null && $pageNo * self::PAGE_SIZE < $goodsService->getMaxSyncGoodsCount()));
    }
}
