<?php

namespace App\Jobs\Goods;

use App\Jobs\Job;
use App\Models\GoodsSku;
use App\Models\Goods;
use App\Models\Order;
use App\Models\OrderItem;
use Illuminate\Support\Facades\DB;

/**
 * 删除Goods的JOB
 */
class DeleteGoodsJob extends Job
{
    protected $goods;
    protected $shopId;
    protected $idArr;

    public function __construct($shopId, $goods)
    {
        $this->goods = $goods;
        $this->shopId = $shopId;
        $this->idArr = collect($goods)->pluck('id')->toArray();
    }

    /**
     * 要清理的商品信息再分更新时间30天以内和30以后
     * 对于删除的商品，设置成“不在售”（这里先保留一段时间）
     * 每天同步的时候真下架每天会更新update_at，但假删除下架不会更新updateat
     * 跟订单一样保留时间 对假删除(下架)进行真删除
     * @return void
     */
    public function handle()
    {
        try {
            $goods = $this->goods;

            $before30days = date('Y-m-d H:i:s', strtotime('-30 day'));
            $now = date('Y-m-d H:i:s');
            //用原生SQL主要是不想更新updated_at
            $toContinue = true;
            $goodsUpdateCount = 0;
            $goodsDeleteCount = 0;
            $goodsSkuUpdateCount = 0;
            $goodsSkuDeleteCount = 0;
            $batchSize = 500;
            $this->loop(function () use (&$goodsUpdateCount, $batchSize, $before30days) {
                $updateCount = DB::table("goods")->whereIn('id', $this->idArr)->limit($batchSize)->whereDate('updated_at', '>=', $before30days)->update(['is_onsale' => 1]);
                $goodsUpdateCount += $updateCount;
                if ($updateCount < $batchSize) {
                    return false;
                }
                return true;
            },"更新失效的商品,shopId-".$this->shopId);

            $this->loop(function () use (&$goodsDeleteCount, $batchSize, $before30days) {
                $updateCount = DB::table("goods")->whereIn('id', $this->idArr)->limit($batchSize)->whereDate('updated_at', '<', $before30days)->delete();
                $goodsDeleteCount += $updateCount;
                if ($updateCount < $batchSize) {
                    return false;
                }
                return true;
            },"删除失效的商品,shopId-".$this->shopId);

            GoodsSku::query()->whereIn('goods_id', $this->idArr)->whereDate('updated_at', '>=', $before30days)->orderBy("goods_id")->select(["id","updated_at"])->chunk(500, function ($goodsSkus) use(&$goodsSkuUpdateCount) {
                $ids = collect($goodsSkus)->pluck('id')->toArray();
                $updateCount=DB::table("goods_skus")->whereIn('id', $ids)->update(['is_onsale' => 1]);
                $goodsSkuUpdateCount+=$updateCount;
            });


            GoodsSku::query()->whereIn('goods_id', $this->idArr)->whereDate('updated_at', '<', $before30days)->orderBy("goods_id")->select(["id","updated_at"])->chunk(500, function ($goodsSkus) use(&$goodsSkuDeleteCount) {
                $ids = collect($goodsSkus)->pluck('id')->toArray();
                $updateCount=DB::table("goods_skus")->whereIn('id', $ids)->delete();
                $goodsSkuDeleteCount+=$updateCount;
            });




//            $this->loop(function () use (&$goodsSkuUpdateCount, $batchSize, $before30days) {
//                $updateCount =  DB::table("goods_skus")->whereIn('goods_id', $this->idArr)->limit($batchSize)->whereDate('updated_at', '>=', $before30days)->update(['is_onsale' => 1]);
//                $goodsSkuUpdateCount += $updateCount;
//                if ($updateCount < $batchSize) {
//                    return false;
//                }
//                return true;
//            });
//
//            $this->loop(function () use (&$goodsSkuDeleteCount, $batchSize, $before30days) {
//                $updateCount =  DB::table("goods_skus")->whereIn('goods_id', $this->idArr)->limit($batchSize)->whereDate('updated_at', '<', $before30days)->delete();
//                $goodsSkuDeleteCount += $updateCount;
//                if ($updateCount < $batchSize) {
//                    return false;
//                }
//                return true;
//            });


//            $goodsUpdateCount = DB::table("goods")->whereIn('id', $this->idArr)->limit(500)->whereDate('updated_at', '>=', $before30days)->update(['is_onsale' => 1]);
//            $goodsDeleteCount = DB::table("goods")->whereIn('id', $this->idArr)->whereDate('updated_at', '<', $before30days)->delete();
//            $goodsSkuUpdateCount = DB::table("goods_skus")->whereIn('goods_id', $this->idArr)->whereDate('updated_at', '>=', $before30days)->update(['is_onsale' => 1]);
//            $goodsSkuDeleteCount = DB::table("goods_skus")->whereIn('goods_id', $this->idArr)->whereDate('updated_at', '<', $before30days)->delete();
//            $goodsUpdateCount = Goods::query()->whereIn('id', $idArr)->where('updated_at', '>', $before30days)->update(['is_onsale'=> 1]);
//            $goodsDeleteCount = Goods::query()->whereIn('id', $idArr)->where('updated_at', '<', $before30days)->delete();
//            $goodsSkuUpdateCount = GoodsSku::query()->whereIn('goods_id', $idArr)->where('updated_at', '>', $before30days)->update(['is_onsale'=> 1]);
//            $goodsSkuDeleteCount = GoodsSku::query()->whereIn('goods_id', $idArr)->where('updated_at', '<', $before30days)->delete();
            \Log::info("清理商品shopId=" . $this->shopId . "商品数据成功", ["idArr" => $this->idArr, "shopId" => $this->shopId, "goodsUpdateCount" => $goodsUpdateCount, "goodsDeleteCount" => $goodsDeleteCount, "goodsSkuUpdateCount" => $goodsSkuUpdateCount, "goodsSkuDeleteCount" => $goodsSkuDeleteCount]);

        } catch (\Exception $ex) {
            \Log::error("清理商品hopId=" . $this->shopId . "商品数据失败", ["idArr" => $this->idArr, $ex]);
        }
//		foreach ($orders as $order) {
//			try{
//				$order->forceDelete();
//				$order->orderItem()->forceDelete();
//			}catch (\Exception $e) {
//				Log::warn('订单数据清理失败', [$order->tid, $e->getMessage()]);
//				continue;
//			}
//		}
    }

    private function loop($runner, $message = null)
    {
        $toContinue = true;
        while ($toContinue) {
            $toContinue = $runner();
        }
        if (isset($message)) {
            \Log::info("执行结果:" . $message);
        }
    }
}
