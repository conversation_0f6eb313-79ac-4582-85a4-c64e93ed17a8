<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2021/11/13
 * Time: 09:52
 */

namespace App\Jobs\DoudianMsg;

use App\Jobs\Job;
use App\Models\Order;
use App\Models\OrderTraceList;
use App\Models\Package;
use App\Models\Shop;
use App\Models\WaybillHistory;
use App\Services\Logistic\LogisticDealService;
use App\Services\Order\PackageService;
use App\Services\Waybill\WaybillServiceManager;
use Illuminate\Support\Facades\Log;

/**
 * 物流轨迹实现，主要是用于更新物流规格
 */
class OrderLogisticsChangedJob extends ShopMessageJob
{

    protected $msg;

    /**
     * @var LogisticDealService  $logisticDealService
     */
    protected  $logisticDealService;


    public function __construct(array $msg)
    {
        Log::info("开始执行物流轨迹更新", $msg);
        $this->msg = $msg;
        $this->logisticDealService = new LogisticDealService(new PackageService());
    }

    /**
     * 更新物轨迹
     * @param $shop
     * @param $data
     * @return mixed|void
     */

    function execute($shop, $data)
    {
        $shopId = $shop->id;
        $tid        = $data->p_id;
        $waybill_code = $data->logistics_msg->logistics_code;
        Log::info("开始更新物流信息", ["shopId" => $shopId, "tid" => $tid, "waybillCode" => $waybill_code]);
        $order       = Order::query()->where('tid', $tid)->first();
        $orderTraceData = ["shopId" => $shopId, "tid" => $tid, "waybillCode" => $waybill_code];
        if($order==null){
            Log::info("没有匹配的订单", $orderTraceData);
            return;
        }
        $package=Package::findByWaybillCode($waybill_code);


        if($package==null){
            Log::info("没有匹配的电子面单", $orderTraceData);
            return;
        }

        $waybillHistory = $package->waybillHistory;

        $waybillShopId = (isset($waybillHistory->source_shopid) && $waybillHistory->source_shopid > 0) ? $waybillHistory->source_shopid : $waybillHistory->shop_id;
//        list($waybillData, $order) = WaybillHistory::getWaybillDataByWaybillHistory($waybillHistory);

        $shop = Shop::query()->where('id', $waybillShopId)->first();


        $wpCode = $package->wp_code;
        $waybillData = [
            'express_code' => $wpCode,
            'express_no' => $waybill_code,
            'tid' => $tid,
            'send_at' => $package->send_at,
        ];

        $waybillService = WaybillServiceManager::init($package->auth_source,$shop->access_token);
        $traceData = $waybillService->getOrderTraceList($waybillData);
        OrderTraceList::batchSave([$traceData], $order->user_id, $order->shop_id);
        Log::info("消息更新物流信息",["shopId"=>$shopId,"tid"=>$tid,"waybillCode"=>$waybill_code,"traceData"=>$traceData]);
        $this->logisticDealService->updateLogistic($traceData);
    }
}
