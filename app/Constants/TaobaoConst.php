<?php

namespace App\Constants;

use App\Models\Order;
use App\Models\OrderItem;

/**
 * 淘宝相关的常量
 */
class TaobaoConst
{
    /**
     * 淘宝订单和标准订单状态转换的MAP
     * @var array
     */
    public static $orderStatusMap = [
        'TRADE_NO_CREATE_PAY' => Order::ORDER_STATUS_PADDING,
        'WAIT_BUYER_PAY' => Order::ORDER_STATUS_PADDING,
        'WAIT_SELLER_SEND_GOODS' => Order::ORDER_STATUS_PAYMENT,
        'SELLER_CONSIGNED_PART' => Order::ORDER_STATUS_PART_DELIVERED,
        'WAIT_BUYER_CONFIRM_GOODS' => Order::ORDER_STATUS_DELIVERED,
        'TRADE_BUYER_SIGNED' => Order::ORDER_STATUS_RECEIVED,
        'TRADE_FINISHED' => Order::ORDER_STATUS_SUCCESS,
        'TRADE_CLOSED' => Order::ORDER_STATUS_CLOSE,
        'TRADE_CLOSED_BY_TAOBAO' => Order::ORDER_STATUS_CLOSE,
        'ALL_CLOSED' => Order::ORDER_STATUS_CLOSE,
        'PAID_FORBID_CONSIGN' => Order::ORDER_STATUS_ABNORMAL, // 该状态代表订单已付款但是处于禁止发货状态。
    ];

    /**
     * 淘宝订单退款状态和标准退款状态的转换MAP
     * @var array
     */

    public static $refundStatusMap = [
        'NO_REFUND' => OrderItem::REFUND_STATUS_NO,
        'WAIT_SELLER_AGREE' => OrderItem::REFUND_STATUS_WAIT_SELLER, // 买家已经申请退款，等待卖家同意
        'WAIT_BUYER_RETURN_GOODS' => OrderItem::REFUND_STATUS_PROCESSING, // 卖家已经同意退款，等待买家退货
        'WAIT_SELLER_CONFIRM_GOODS' => OrderItem::REFUND_STATUS_WAIT_SELLER, // 买家已经退货，等待卖家确认收货
        'SELLER_REFUSE_BUYER' => OrderItem::REFUND_STATUS_PROCESSING, // 卖家拒绝退款
        'CLOSED' => OrderItem::REFUND_STATUS_NO, // 退款关闭
//        'CLOSED' => OrderItem::REFUND_STATUS_CLOSED, // 退款关闭
        'SUCCESS' => OrderItem::REFUND_STATUS_SUCCESS, // 退款成功
    ];

    /**
     * 淘宝订单退款状态说明集合
     * @var array
     */
    public static $refundStatusTextMap = [
        'NO_REFUND' => '无退款',
        'WAIT_SELLER_AGREE' => '等待商家同意', // 买家已经申请退款，等待卖家同意
        'WAIT_BUYER_RETURN_GOODS' => '等待用户退货', // 卖家已经同意退款，等待买家退货
        'WAIT_SELLER_CONFIRM_GOODS' => '等待商家确认收货', // 买家已经退货，等待卖家确认收货
        'SELLER_REFUSE_BUYER' => '拒绝退款', // 卖家拒绝退款
        'CLOSED' => '退款关闭', // 退款关闭
        'SUCCESS' => '退款成功', // 退款成功
    ];

    public static $goodsDeliverStatusTextMap = [
        'BUYER_NOT_RECEIVED' => '买家未收到货',
        'BUYER_RECEIVED' => '买家已收到货',
        'BUYER_RETURNED_GOODS' => '买家已退货',
    ];


    /**
     * 转换订单的状态
     * @param $inputStatus
     * @return mixed
     */
    public  static   function formatOrderStatus($inputStatus){
        if (!isset(self::$orderStatusMap[$inputStatus])) {
            throw new InvalidArgumentException('未定义的订单状态：' . $inputStatus);
        }
        return self::$orderStatusMap[$inputStatus];
    }


    /**
     * 转换订单的状态
     * @param $inputStatus
     * @return mixed
     */
    public  static   function formatRefundStatus($inputStatus){
        if (!isset(self::$refundStatusMap[$inputStatus])) {
            throw new InvalidArgumentException('未定义的退款状态：' . $inputStatus);
        }
        return self::$refundStatusMap[$inputStatus];
    }

}
