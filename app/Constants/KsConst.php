<?php

namespace App\Constants;

use App\Models\AftersaleOrder;
use App\Models\Order;
use App\Models\OrderItem;

/**
 * 淘宝相关的常量
 */
class KsConst
{
    /**
     * 快手订单退款状态说明集合
     * @var array
     */
    public static $refundStatusMap = [
        1 => AftersaleOrder::REFUND_STATUS_WAIT, // 待商家处理
        2 => AftersaleOrder::REFUND_STATUS_SUCCESS, // 商家已同意
        3 => AftersaleOrder::REFUND_STATUS_FAIL, // 卖家拒绝退款
    ];

    /**
     * 转换订单的状态
     * @param $inputStatus
     * @return mixed
     */
    public static function formatRefundStatus($inputStatus){
        if (!isset(self::$refundStatusMap[$inputStatus])) {
            return AftersaleOrder::REFUND_STATUS_OTHER;
        }
        return self::$refundStatusMap[$inputStatus];
    }
}
