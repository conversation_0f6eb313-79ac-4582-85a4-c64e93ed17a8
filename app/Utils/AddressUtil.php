<?php
namespace App\Utils;

use App\Models\Address;
use AddressParse;
use App\Services\BusinessException;
use GuzzleHttp\Client;

/**
 *
 */
class AddressUtil
{
    /**
     * 不知道正确性
     * @param string $address
     * @return array
     * @deprecated
     * 省市区提取
     */
    public static function handleAddress($address = '广东省深圳市龙华新区大浪街道同胜科技大厦')
    {
        preg_match('/(.*?(省|自治区|北京市|天津市|上海市|重庆市))/', $address, $matches);
        if (count($matches) > 1) {
            $province = $matches[count($matches) - 2];
            $address = str_replace($province, '', $address);
        }
        preg_match('/(.*?(市|自治州|地区|区划|县))/', $address, $matches);
        if (count($matches) > 1) {
            $city = $matches[count($matches) - 2];
            $address = str_replace($city, '', $address);
        }
        if (empty($city)) {
            $city = isset($province) ? $province : '';
        }
        preg_match('/(.*?(区|县|镇|乡|街道))/', $address, $matches);
        if (count($matches) > 1) {
            $area = $matches[count($matches) - 2];
            $address = str_replace($area, '', $address);
        }

        return [
            'receiver_province' => isset($province) ? $province : '',
            'receiver_city' => isset($city) ? $city : '',
            'receiver_district' => isset($area) ? $area : '',
            'receiver_address' => isset($address) ? $address : '',
        ];
    }

    /**
     * 顺丰地址识别,对识别的结果会增加一个相似度的校验，相似度校验如果比例太低就认为不通过，默认90
     * @param string $address
     * @param $minPercent 最低的相似度比例
     * @return mixed
     * @throws BusinessException
     */
    public static function sfAddress($address,$minPercent=80,$safeMode=true)
    {
        //计算一下解析的耗时
        $start=microtime(true);
        $sfUrl = 'https://www.sf-express.com/sf-service-core-web/service/nlp/address/mainlandChina/resolve?lang=sc&region=cn&translate=sc';
        $client = new Client();
        $cleanedAddress=trim(preg_replace('/\\s+/','',$address));
        $response = $client->post($sfUrl, [
            'json' => [
                'address' => $cleanedAddress
            ],
            'verify' => true,
            'headers' => [
                'Content-type' => 'application/json',
                "Accept" => "application/json"
            ],
        ]);
        $result = json_decode($response->getBody()->getContents(), true);
        $resolved=microtime(true);
//        dd($cleanedAddress,$result);
        if (!isset($result['result'])||count($result['result'])<1) {
            if($safeMode){
                return null;
            }
            throw new BusinessException('自动识别地址有误!');
        }
        $addressInfo = $result['result'][0];
        $addressParseResult=new AddressParseResult();
        $addressParseResult->setDetail($cleanedAddress);
        $addressParseResult->setPassed(false);
        if(empty($addressInfo)){
            return $addressParseResult;
        }
        $province = $addressInfo['province'] ?? '';
        $detail = $addressInfo['address']??'';
        $city = $addressInfo['city'] ?? '';
        $district = $addressInfo['district'] ?? '';

//        if (count($addressInfo['originDestRegions']) < 3) {
//            $city = $addressInfo['originDestRegions'][0]['name'] ?? '';
//            $district = $addressInfo['originDestRegions'][1]['name'] ?? '';
//        } else {
//            $city = $addressInfo['originDestRegions'][1]['name'] ?? '';
//            $district = $addressInfo['originDestRegions'][2]['name'] ?? '';
//        }
        $parsedAddress=$province.$city.$district.$detail;
        $percent =0;
        similar_text($cleanedAddress, $parsedAddress,$percent);
        $addressParseResult->setProvince($province);
        $addressParseResult->setCity($city);
        $addressParseResult->setDistrict($district);
        $addressParseResult->setDetail($detail);
        $addressParseResult->setPercent($percent);
        if (empty($province) || empty($city) || empty($district) || empty($detail)) {
            $addressParseResult->setPassed(false);
            return $addressParseResult;
        }
        if($percent<$minPercent){
            $addressParseResult->setMessage("相似度无法通过验证");
            $addressParseResult->setPassed(false);
        }else{
            $addressParseResult->setPassed(true);
        }
        $end=microtime(true);
        \Log::info('解析地址耗时:',[$resolved-$start,$end-$resolved,$end-$start]);

        return $addressParseResult;
    }

    public static function addressByPHP(string $address)
    {
        $addressParseResult=new AddressParseResult();
        $addressParseResult->setPassed(false);

        $parse = new AddressParse();
        $addressInfo = $parse->setType(1)->parse($address);
        $province = $addressInfo['province'];
        $city = $addressInfo['city'];
        $district = $addressInfo['area'];
        $detail = $addressInfo['detail'];
        if (!empty($province) && !empty($city) && !empty($district) && !empty($detail)) {
            $addressParseResult->setPassed(true);
            $addressParseResult->setProvince($province);
            $addressParseResult->setCity($city);
            $addressParseResult->setDistrict($district);
            $addressParseResult->setDetail($detail);
            return $addressParseResult;
        }
        $addressParseResult->setMessage('自动识别出错：'.$province.$city.$district.$detail);
        return $addressParseResult;
    }

    /**
     * 解析地址通过正则
     * @param string $address
     * @return AddressParseResult
     */
    public static function addressByRegular(string $address)
    {
        $addressParseResult=new AddressParseResult();
        $addressParseResult->setPassed(false);

        $regex = "#(?<province>[^省]+省|.+自治区|[^澳门]+澳门|[^香港]+香港|[^市]+市)?(?<city>[^自治州]+自治州|[^特别行政区]+特别行政区|[^市]+市|.*?地区|.*?行政单位|.+盟|市辖区|[^县]+县)(?<county>[^县]+县|[^市]+市|[^镇]+镇|[^区]+区|[^乡]+乡|.+场|.+旗|.+海域|.+岛)?(?<address>.*)#u";
//        $regex = "#(?<province>[^省]+省|.+自治区|[^澳门]+澳门|[^香港]+香港)?(?<city>[^市]+市|[^自治州]+自治州|[^特别行政区]+特别行政区)?(?<county>[^县]+县|[^市]+市|[^镇]+镇|[^区]+区|[^乡]+乡|.+场|.+旗|.+海域|.+岛)(?<address>.*)#u";

        preg_match($regex, $address, $matches);

//        dd($matches['province'], $matches['city'], $matches['county'], $matches['address']);
        $province = $matches['province'];
        $city = $matches['city'];
        $district = $matches['county'];
        $detail = $matches['address'];
        if (!empty($province) && !empty($city) && !empty($district) && !empty($detail)) {
            $addressParseResult->setPassed(true);
            $addressParseResult->setProvince($province);
            $addressParseResult->setCity($city);
            $addressParseResult->setDistrict($district);
            $addressParseResult->setDetail($detail);
            return $addressParseResult;
        }
        $addressParseResult->setMessage('自动识别出错：'.$province.$city.$district.$detail);
        return $addressParseResult;
    }

    public static function smartParse(string $address)
    {
        $addressParseResult = static::addressByPHP($address);
        if ($addressParseResult->getPassed()) {
            return $addressParseResult;
        }
        $addressParseResult = static::addressByRegular($address);
        if ($addressParseResult->getPassed()) {
            return $addressParseResult;
        }
        return static::sfAddress($address);
    }

    /**
     * 匹配地址（1，2级）和区域编码（到区县）
     * @param Address $address
     * @param string $districtCode
     * @return bool
     */
    public static function matchDistrictCodeAndAddress(Address $address,string $districtCode){
        if($address->level==1) {
            $code = strval($address->code);
//            \Log::info("匹配到加收区域的使用量",[$districtCode,$code]);
            if (strpos(strval($districtCode), $code, 0) === 0) {
                //如果有就加，没有就设置
//                \Log::info("匹配到加收区域的使用量", [$districtCode]);
                return true;
            }
        }
        //如果是市一级，市的code要截取前4位
        if($address->level==2){
            $code=substr(strval($address->code),0,4);
//            \Log::info("匹配到加收区域的使用量",[$districtCode,$code]);
            if (strpos(strval($districtCode), $code ) === 0) {
                //如果有就加，没有就设置
//                \Log::info("匹配到加收区域的使用量", [$districtCode]);
                return true;
            }
        }

        return false;
    }
}
