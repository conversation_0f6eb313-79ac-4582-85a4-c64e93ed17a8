<?php

namespace App\Utils;

use Illuminate\Support\Facades\Log;

class RedisUtil
{

    /**
     * 生成订单号
     * @param string $prefix
     * @param string|null $dateStr
     * @return string
     */
    public static function getOrderSn(string $prefix,string $dateStr=null): string
    {
        if (empty($dateStr)) {
            $dateStr = date('Ymd');
        }

//今日teamId的订单数递增
        $key = "ordersn".$prefix . $dateStr;
        $dailyOrderNum = redis('cache')->incr($key);
//如果不足4位，前面补0
        if ($dailyOrderNum < 1000) {
            $dailyOrderNumStr = str_pad($dailyOrderNum, 4, '0', STR_PAD_LEFT);
        } else {
            $dailyOrderNumStr = strval($dailyOrderNum);
        }

//如果返回的值是1，表示这个key是第一次取，key设置过期时间
        if ($dailyOrderNum == 1) {
//            $redis->expire($key, 86400);
            redis('cache')->expire($key, 86400);
        }
        $orderId =$prefix . $dateStr . $dailyOrderNumStr;
        Log::info("生成订单号", [$orderId]);
        return $orderId;
    }
}
