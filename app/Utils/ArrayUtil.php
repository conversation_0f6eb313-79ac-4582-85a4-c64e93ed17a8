<?php

namespace App\Utils;

use Illuminate\Support\Facades\Log;

class ArrayUtil
{
    /**
     * 对数组进行分组聚合
     * @param $array
     * @param $keys
     * @return array
     */
    static function array_group_by($array, $keys): array
    {
        if(empty($array)){
            return [];
        }
        if (!is_array($keys) || count($keys) == 1) {
            $key = is_array($keys) ? array_shift($keys) : $keys;

            return array_reduce($array, function ($tmp_result, $item) use ($key) {
                $tmp_result[$item[$key]][] = $item;

                return $tmp_result;
            });
        } else {
            $keys = array_values($keys);

            $result = self::array_group_by($array, array_shift($keys));

            foreach ($result as $k => $value) {
                $result[$k] = self::array_group_by($value, $keys);
            }

            return $result;
        }
    }

    /**
     * 判断array的key是否存在，支持数组嵌套，key之间用.分割
     */

    static function array_key_exists($key, $array): bool
    {
        $keys = explode('.', $key);
        $tmp = $array;
        foreach ($keys as $k) {
            if (!isset($tmp[$k])) {
                return false;
            }
            $tmp = $tmp[$k];
        }
        return true;
    }

    /**
     * 判断数组是否是索引数组
     * @param $array
     * @return bool
     */
    static function is_indexed_array($array): bool
    {
        $values = array_values($array); // 将数组转换为索引数组
        $diff = array_diff_key($array, $values); // 比较原数组和转换后的数组的键名
        if (empty($diff)) { // 如果没有差异
            return true; // 返回 true
        } else {
            return false; // 返回 false
        }
    }

    /**
     * 获取数组中的值，支持数组嵌套，key之间用.分割
     * @param array $array
     * @param string $key
     * @param $default
     * @return array|mixed|null
     */
    static function getArrayValue(array $array ,string $key, $default = null)
    {
        $keys = explode('.', $key);
        $tmp = $array;
        foreach ($keys as $k) {
            if (!isset($tmp[$k])) {
                return $default;
            }
            $tmp = $tmp[$k];
        }
        return $tmp;
    }

    /**
     * 只保留最内层的数组。这个函数将检查数组的深度，并适当地访问元素以获取最内层的数组
     * @param $array
     * @return array
     */

    function getInnermostArray($array) {
        // 循环直到数组只有一层
        while (is_array($array) && count($array) === 1 && is_array($array[0])) {
            $array = $array[0];
        }
        return $array;
    }

    /**
     * 把一维数组链接成字符串，数组的key value之间用$separator连接，数组之间用$concatenator链接,
     * [{"name":"雪毅网络","app_id":"16252093235113","shop_id":83188}]
     * @param array|null $array $array
     * @param array $keys
     * @param string $separator
     * @param string $connector
     * @return string
     */
    public static function toJoinString(?array $array=null,array $keys=[],string  $separator=" ",string $connector=","): string{
        if(empty($array)){
            return "";
        }
        $result = [];
        foreach ($array as $item){
            $itemValues = [];
            foreach ($keys as $key){
                if(!isset($item[$key])){
                    continue;
                }
                $itemValues[] = $item[$key];
            }
            $result[] = implode($separator,array_values($itemValues));
        }
        return implode($connector,$result);

    }

}
