<?php

namespace App\Utils;

use App\Constants\ErrorConst;
use App\Exceptions\ErrorCodeException;
use Carbon\Carbon;
use DateTime;
use Illuminate\Support\Facades\Log;

class DateTimeUtil
{
    /**
     * 时间格式
     */
    public const FORMAT_Y_M_D_H_I_S = "Y_m_d_H_i_s";
    public const ymdHms = 'Y-m-d H:i:s';

    public const ymd = 'Y-m-d';


    /**
     * 获取当前时间字符串
     * @param string $format
     * @return string
     */
    public static function strNow(string $format="Y-m-d H:i:s"): string
    {
        return date($format);
    }


    /**
     * 从字符串转换到日期
     * @param string|null $str
     * @param string $format
     * @return DateTime|null
     */
    public static function date(?string $str, string $format = self::ymdHms): ?DateTime
    {
        if (empty($str)) {
            return null;
        }
        return DateTime::createFromFormat($format, $str);
    }

    /**
     * 获取时间的字符串
     * @param $date
     * @return string
     */
    public static function ymdHms($date): string
    {
        return date_format($date, self::ymdHms);
    }


    /**
     * 获取当前时间
     * @return DateTime
     */
    public static function now(): DateTime
    {
        return DateTime::createFromFormat(self::ymdHms,Carbon::now()->toDateTimeString());
    }

    public static function format($date, $format = self::ymdHms): string
    {
        return date_format($date, $format);
    }

    /**
     * 获取一个指定的时间跟今天0点相差的秒数.
     */
    public static function getTodaySecondsByTime(DateTime $dateTime): int
    {
        //用字符串的方式，把时间转换成今天0点的时间
        $strToday = static::format($dateTime, DateTimeUtil::ymd)." 00:00:00";
        $today = static::date($strToday,DateTimeUtil::ymdHms);

        return static::toUnixTime($dateTime) - static::toUnixTime($today);
    }

    /**
     * 一个小时分秒的字符串，相对于今天0点的秒数.
     * 把小时分秒都拼上一个固定的日期(2020-01-01)，然后转换成Unixtime，再减去这个固定日期(2020-01-01)的Unixtime
     */
    public static function getSecondsByHourMinuteSecond(string $hourMinuteSecond): int{
        $fullDate = "2020-01-01 " . $hourMinuteSecond;
        Log::info("fullDate: " . $fullDate);
        $time = static::date($fullDate, DateTimeUtil::ymdHms);
        return static::toUnixTime($time) - static::toUnixTime(static::date('2020-01-01 00:00:00', DateTimeUtil::ymdHms));
    }

    /**
     * 转换到Unix时间(秒）
     * @param DateTime $datetime
     * @return int
     */
    public static function toUnixTime(DateTime $datetime): int
    {
        return $datetime->getTimestamp();
    }
    public static function toDateTimeTimeString(?int $time): ?string
    {
        if (empty($time)) {
            return null;
        }
        // 如果时间长度是毫秒的时间戳转成秒
        if (strlen($time) > 10) {
            $time = (int)($time / 1000);
        }
        return date('Y-m-d H:i:s', $time);
    }
    /**
     * 带时区的时间字符串
     * example $timeString = 20230721174502000+0800
     * @param int $time
     * @return string
     */
    public static function toDateTimeTimezoneString(int $time):string
    {
        // 创建一个 Carbon 对象，并设置时区为 UTC
        $carbonDateTime = \Carbon\Carbon::createFromTimestamp($time);

        // 获取当前日期时间的各个部分
        $year = $carbonDateTime->year;
        $month = str_pad($carbonDateTime->month, 2, '0', STR_PAD_LEFT);
        $day = str_pad($carbonDateTime->day, 2, '0', STR_PAD_LEFT);
        $hour = str_pad($carbonDateTime->hour, 2, '0', STR_PAD_LEFT);
        $minute = str_pad($carbonDateTime->minute, 2, '0', STR_PAD_LEFT);
        $second = str_pad($carbonDateTime->second, 2, '0', STR_PAD_LEFT);
        $millisecond = str_pad(0, 3, '0', STR_PAD_LEFT); // 获取当前毫秒部分
        $timezoneOffset = sprintf('%+03d%02d', $carbonDateTime->offsetHours, 0); // 获取当前时区的偏移量，例如：+0800

        // 构建时间字符串
        $timeString = $year . $month . $day . $hour . $minute . $second . $millisecond . $timezoneOffset;
        // 输出结果
//        echo "生成的时间字符串: " . $timeString;
        return $timeString;
    }

    /**
     * 带时区的时间字符串
     * example $timeString = 20230721174502000+0800
     * @param string $timeString
     * @return Carbon
     */
    public static function fromDateTimeTimezone(string $timeString): Carbon
    {
        // 提取时间信息
        $year = substr($timeString, 0, 4);
        $month = substr($timeString, 4, 2);
        $day = substr($timeString, 6, 2);
        $hour = substr($timeString, 8, 2);
        $minute = substr($timeString, 10, 2);
        $second = substr($timeString, 12, 2);
        $timezoneOffsetSign = substr($timeString, 17, 1);
        $timezoneOffsetHours = intval(substr($timeString, 18, 2));
        $timezoneOffsetMinutes = intval(substr($timeString, 20, 2));

        // 构建 Carbon 对象
        $dateTimeString = "{$year}-{$month}-{$day}T{$hour}:{$minute}:{$second}Z";
        $carbonDateTime = \Carbon\Carbon::createFromFormat('Y-m-d\TH:i:s\Z', $dateTimeString, 'PRC');
        // 根据时区偏移量调整时间
        if ($timezoneOffsetSign === '+') {
            $carbonDateTime->addHours($timezoneOffsetHours);
            $carbonDateTime->addMinutes($timezoneOffsetMinutes);
        } else {
            $carbonDateTime->subHours($timezoneOffsetHours);
            $carbonDateTime->subMinutes($timezoneOffsetMinutes);
        }
        return $carbonDateTime;
    }

    /**
     * 带时区的时间字符串
     * example $timeString = 20230721174502000+0800
     * @param string|null $createTime
     * @return string|null
     */
    public static function fromTimezoneToDateTimeString(?string $createTime): ?string
    {
        if (empty($createTime)){
            return null;
        }
        return static::albbTimeFormat($createTime);
    }

    /**
     * 阿里巴巴的时间转换
     * @param string $timeString 20250323142336000+0800
     * @return string|null
     */
    public static function albbTimeFormat(string $timeString)
    {
        // 创建 DateTime 对象，并指定输入时间字符串的格式
        $carbon = Carbon::createFromFormat('YmdHisuP', $timeString);
        // 检查是否成功创建了 DateTime 对象
        if (!$carbon) {
            return null;
        } else {
            // 将 DateTime 对象格式化为目标格式 Y-m-d H:i:s
            $formattedTime = $carbon->format('Y-m-d H:i:s');
        }
        return $formattedTime;
    }

    /**
     * 获取今天24点的时间字符串
     * @param string $format
     * @return string
     */
    public static function strEndOfDay(string $format="Y-m-d H:i:s"): string
    {
        return date($format, strtotime('today 23:59:59'));
    }


    /**
     * 添加天数到日期,精度到时分秒
     * @param string $date
     * @param int|string $unit
     * @param int $value
     * @return string
     * @throws ErrorCodeException
     */
    public static function addDays(string $date, $unit, int $value): string{
        $result=null;
        if($unit == 'day' || $unit == 0){

            $result =Carbon::parse($date)->addDays($value)->toDateTimeString();
        }
        if($unit == 'month' || $unit == 1){
            $result =Carbon::parse($date)->addMonths($value)->toDateTimeString();
        }
        if($unit == 'year' || $unit == 2){
            $result =Carbon::parse($date)->addYears($value)->toDateTimeString();
        }
        if($result){
            Log::info("添加天数到日期: {$date} 单位：{$unit}, 数量：{$value} 结果：{$result}");
            return $result;
        }
        throw_error_code_exception(ErrorConst::PARAM_ERROR,null, "不支持的单位类型: {$unit}");
    }

    /**
     * 两个日期相减，返回天数
     * @param string $data1
     * @param string $data2
     * @return int
     */
    public static function diffInDays(string $data1, string $data2): int{
        return Carbon::parse($data1)->diffInDays(Carbon::parse($data2));
    }

    /**
     * 获取当前时间字符串
     * @param int|null $timestamp
     * @return string|null
     */
    public static function second2str(?int $timestamp): ?string
    {
        if(!isset($timestamp)){
            return null;
        }
        return static::ymdHms(static::fromUnixTime($timestamp));
    }

    /**
     * 从时间戳获取时间
     * @param int $timestamp
     * @return DateTime
     */
    public static function fromUnixTime(int $timestamp): DateTime
    {
        return Carbon::createFromTimestamp($timestamp);
    }



}
