<?php

namespace App\Services\Client\Jd\Request;

class BuyOrderSopWaybillUpdateRequest
{
    private $apiParas = array();

    public function getApiMethodName()
    {
        return "360buy.order.sop.waybill.update";
    }

    public function getApiParas()
    {
        if (empty($this->apiParas)) {
            return "{}";
        }
        return json_encode($this->apiParas);
    }

    public function check()
    {

    }

    public function putOtherTextParam($key, $value)
    {
        $this->apiParas[$key] = $value;
        $this->$key = $value;
    }

    private $version;

    public function setVersion($version)
    {
        $this->version = $version;
    }

    public function getVersion()
    {
        return $this->version;
    }

    /* @var 订单id
     */
    private $orderId;

    /**
     * @var 物流公司id
     */
    private $logisticsId;

    /**
     * @var 运单号
     */
    private $waybill;

    /**
     * @var 流水号
     */
    private $tradeNo;

    /**
     * @return 订单id
     */
    public function getOrderId()
    {
        return $this->orderId;
    }

    /**
     * @param 订单id $orderId
     */
    public function setOrderId($orderId): void
    {
        $this->orderId = $orderId;
        $this->apiParas["order_id"] = $orderId;
    }

    /**
     * @return 物流公司id
     */
    public function getLogisticsId()
    {
        return $this->logisticsId;
    }

    /**
     * @param 物流公司id $logisticsId
     */
    public function setLogisticsId($logisticsId): void
    {
        $this->logisticsId = $logisticsId;
        $this->apiParas["logistics_id"] = $logisticsId;
    }

    /**
     * @return 运单号
     */
    public function getWaybill()
    {
        return $this->waybill;
    }

    /**
     * @param 运单号 $waybill
     */
    public function setWaybill($waybill): void
    {
        $this->waybill = $waybill;
        $this->apiParas["waybill"] = $waybill;
    }

    /**
     * @return 流水号
     */
    public function getTradeNo()
    {
        return $this->tradeNo;
    }

    /**
     * @param 流水号 $tradeNo
     */
    public function setTradeNo( $tradeNo): void
    {
        $this->tradeNo = $tradeNo;
        $this->apiParas["trade_no"] = $tradeNo;
    }

}
