<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2020/6/9
 * Time: 19:41
 */

namespace App\Services\Client;


use App\Constants\ErrorConst;
use App\Exceptions\ApiException;
use App\Exceptions\ClientException;
use App\Exceptions\OrderException;
use App\Services\BusinessException;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\ConnectException;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Pool;
use GuzzleHttp\Psr7\Request;
use GuzzleHttp\Psr7\Response;
use Illuminate\Support\Facades\Log;
use Psr\Http\Message\StreamInterface;

class XhsClient
{
    public $appkey;

    public $secretKey;

    public $gatewayUrl = "https://ark.xiaohongshu.com/ark/open_api/v3/common_controller";

    protected $apiVersion = "2.0";
    /**
     * @var mixed
     */
    private $accessToken = '';
    /**
     * 默认超时时间 秒
     * @var int
     */
    private $clientTimeout = 8;
    private $clientConnectTimeout = 5; // 客户端连接超时
    private $shopId = 0;

    /**
     * 最大重试次数
     */
    const MAX_RETRIES = 3;

    /**
     * @var Client
     */
    protected $client;

    public static function newInstance($accessToken = '', $appKey = null, $secretKey = null)
    {

        $client = new XhsClient($appKey ?? config('socialite.xhs.client_id'), $secretKey ?? config('socialite.xhs.client_secret'));
        $client->setAccessToken($accessToken);
        return $client;
    }

    public function __construct($appKey, $secretKey, $timeout = 8)
    {
        $this->appkey = $appKey;
        $this->secretKey = $secretKey;
        $this->clientTimeout = $timeout;
        $this->gatewayUrl = config('socialite.xhs.gateway_url');
    }

    /**
     * 执行请求
     * @param string $apiMethod 接口名 例如：product/getGoodsCategory
     * @param array $apiParams 接口参数
     * @return mixed|StreamInterface
     * @throws ClientException
     * @throws OrderException|ApiException
     * <AUTHOR>
     */
    public function execute(string $apiMethod, array $apiParams, string $requestMethod = 'post')
    {
        $params = $this->buildRequestData($apiParams,$apiMethod);

        $httpClient = $this->getHttpClient();
        $response = $httpClient->post($this->gatewayUrl,[
            'json' => $params
        ]);
        $requestArr = [
            'method' => $apiMethod,
            'url' => $this->gatewayUrl,
            'requestData' => $params,
        ];
        return $this->handleResponse($response->getBody(), $requestArr);
    }

    /**
     * 执行请求
     * @param string $apiMethod 接口名 例如：product/getGoodsCategory
     * @param array $apiParams 接口参数
     * @return mixed|StreamInterface
     * @throws ClientException
     * <AUTHOR>
     */
    public function executeByCustom(string $apiMethod, array $apiParams, $requestMethod = 'POST')
    {
        $request_data = $this->buildRequestData($apiParams, $apiMethod);
        $apiMethod = str_replace('.', '/', $apiMethod);
        $base_url = $this->getBaseUrlByApimethod($apiMethod);

        $httpClient = $this->getHttpClient();
        $requestMethod = strtoupper($requestMethod);
        if ($requestMethod == 'GET') {
            $dataType = 'query';
        }else {
            $dataType = 'form_params';
        }
        $response = $httpClient->$requestMethod($base_url, [
            $dataType => $request_data,
        ]);
        $contents = $response->getBody()->getContents();
        return json_decode($contents, true);
    }

    /**
     * 执行异步请求
     * @param string $apiMethod
     * @param array $apiParamsArr
     * @return array
     * <AUTHOR>
     */
    public function executeAsync(string $apiMethod, array $apiParamsArr): array
    {
        $result = [];
        $base_url = $this->getBaseUrlByApimethod($apiMethod);
        $requests = function ($apiParamsArr) use ($base_url, $apiMethod) {
            foreach ($apiParamsArr as $index => $apiParams) {
                $request_data = $this->buildRequestData($apiParams, $apiMethod);
                yield new Request('GET', $base_url . '?' . http_build_query($request_data));
            }
        };
        $httpClient = $this->getHttpClient();
        $pool = new Pool($httpClient, $requests($apiParamsArr), [
            'concurrency' => 10, //并发数
            'fulfilled' => function (Response $response, $index) use (&$result, $apiMethod, $apiParamsArr) {
                $apiParams = $apiParamsArr[$index] ?? [];
                // 请求成功
                try {
                    $data = $this->handleResponse($response->getBody(), compact('apiMethod', 'apiParams'));
                    $result[$index] = $data['data'] ?? [];
                } catch (\Exception $ex) {
                    \Log::info("返回有错误." . $ex->getMessage(), []);
                    $result[$index] = null;
                }
//                echo $response->getBody()->getContents();
            },
            'rejected' => function (RequestException $reason, $index) {
                // 请求失败
                $result[$index] = null;
                \Log::info("执行异常." . $reason->getMessage(), []);
//                throw new OrderException(class_basename($this) . ' executeAsync error:' . $reason->getMessage());
            },
        ]);
        // 初始化并创建promise
        $promise = $pool->promise();
        // 等待所有进程完成
        $promise->wait();
        return $result;
    }

    /**
     * 刷新token
     * @param string $refreshToken
     * @return array
     * @throws ClientException
     * @throws OrderException|ApiException
     */
    public function refreshToken(string $refreshToken)
    {
        $data = [
            'refresh_token' => $refreshToken,
        ];
        $client = self::newInstance();
        $response = $client->execute('oauth.refreshToken', $data);

        return $response;
    }

    /**
     * @return Client
     * <AUTHOR>
     */
    public function getHttpClient()
    {
        // 创建 Handler
//        $handlerStack = HandlerStack::create(new CurlHandler());
        // 创建重试中间件，指定决策者为 $this->retryDecider(),指定重试延迟为 $this->retryDelay()
//        $handlerStack->push(Middleware::retry($this->retryDecider(), $this->retryDelay()));

        return new Client([
            'http_errors' => false,
            'timeout' => $this->clientTimeout, //秒
            'connect_timeout' => $this->clientConnectTimeout, //秒
//            'handler' => $handlerStack //重试策略
        ]);
    }

    /**
     * @param StreamInterface $body
     * @param array $request
     * @return array
     * @throws ApiException
     * @throws OrderException
     * <AUTHOR>
     */
    public function handleResponse($body, $request = [])
    {
        if (!is_array($body)) {
            $body = json_decode($body, true);
        }
        static::handleErrorCode($body,$request);
//        if ($body['code'] != 10000) {
//            $shop_id = $this->shopId;
//            \Log::error(get_class($this) . ' request error:' . $body['msg'] ?? 'message为空', compact('shop_id', 'request', 'body'));
////            throw new ClientException(get_class($this) . ':' . $body['message']);
//        }
        return $body;
    }

    /**
     * @param mixed $accessToken
     * @return XhsClient
     */
    public function setAccessToken($accessToken)
    {
        $this->accessToken = $accessToken;
        return $this;
    }

    /**
     * @param string $apiMethod
     * @return string
     * <AUTHOR>
     */
    public function getBaseUrlByApimethod(string $apiMethod = ''): string
    {
        return $this->gatewayUrl ;
    }

    /**
     * @param array $apiParams
     * @param string $apiMethod
     * @return array
     * <AUTHOR>
     */
    public function buildRequestData(array $apiParams, string $apiMethod): array
    {
        $timestamp = (string)time();
        $sign = $this->genSign($apiMethod, $timestamp);

        $sysParams = [
            'timestamp' => $timestamp,
            'appId' => $this->appkey,
            'sign' => $sign,
            'version' => $this->apiVersion,
            'method' => $apiMethod,
            'accessToken' => $this->accessToken,
        ];
        return array_merge($apiParams, $sysParams);
    }


    /**
     * 处理抖音响应的错误
     * @see https://op.jinritemai.com/docs/guide-docs/10/23
     * <AUTHOR>
     * @param $result
     * @throws ApiException
     * @throws OrderException
     */
    public static function handleErrorCode($body, $request = [])
    {
        if (is_object($body)){
            $body = json_decode(json_encode($body),true);
        }
        switch ($body['error_code'] ?? 0) {
            case 0:
                break;
            case 999:
                if ($body['error_msg'] == 'refreshToken expired'){
                    throw new ApiException(ErrorConst::PLATFORM_SHOP_AUTH_EXPIRED);
                }
                break;
//            case 401:
                // token 过期 {error_code:401,error_msg:根据accessToken和appId获取mercuryUser失败：accessToken expired,success:false}
                // 回收错误 也是 401{"error_msg":"面单已经被揽收,签收或回收","data":{"subErrorCode":"invalid detail status"},"success":false,"error_code":401}
//                throw new ApiException(ErrorConst::PLATFORM_SHOP_AUTH_EXPIRED);
            default:
                Log::error('小红书服务异常', ['request'=>$request,'response'=>$body]);
                throw new OrderException('小红书平台错误：' . $body['error_msg']);
        }
    }

    /**
     * retryDecider
     * 返回一个匿名函数, 匿名函数若返回false 表示不重试，反之则表示继续重试
     * @return \Closure
     */
    protected function retryDecider()
    {
        return function (
            $retries,
            Request $request,
            Response $response = null,
            RequestException $exception = null
        ) {
            // 超过最大重试次数，不再重试
            if ($retries >= self::MAX_RETRIES) {
                return false;
            }

            if ($response) {
                parse_str($request->getUri()->getQuery(), $params);
                if (!empty($params)) {
                    $method = $params['method'] ?? "";
                    //发货失败请求重试
                    if (in_array($method, ['order.logisticsAdd'])) {
                        // 请求失败，继续重试
                        if ($exception instanceof ConnectException) {
                            return true;
                        }
                        $body = !is_array($response->getBody()) ? json_decode($response->getBody(), true) : $response->getBody();
                        // dy 错误码汇总 https://op.jinritemai.com/docs/guide-docs/161/1427
                        if (isset($body['code']) && in_array($body['code'], [20000, 60000])) {
                            return true;
                        }
                    }
                }
            }

            return false;
        };
    }

    /**
     * 返回一个匿名函数，该匿名函数返回下次重试的时间（毫秒）
     * @return \Closure
     */
    protected function retryDelay()
    {
        return function ($numberOfRetries) {
            return 1000 * $numberOfRetries;
        };
    }

    private function genSign(string $apiMethod, string $time): string
    {
        $str = $apiMethod .'?';
        $data = [
            'appId' => $this->appkey,
            'timestamp' => $time,
            'version' => $this->apiVersion
        ];
        $str .= http_build_query($data);
        $str .= $this->secretKey;
        return md5($str);
    }
}
