<?php

namespace App\Services\Client;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\ConnectException;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Handler\CurlHandler;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\Middleware;
use GuzzleHttp\Psr7\Request;
use GuzzleHttp\Psr7\Response;

class AbstractClient
{
    private $clientTimeout = 8;
    private $clientConnectTimeout = 5; // 客户端连接超时


    /**
     * 创建http客户端
     * @return Client
     */
    public function getHttpClient(): Client
    {
        // 创建 Handler
        $handlerStack = HandlerStack::create(new CurlHandler());
        // 创建重试中间件，指定决策者为 $this->retryDecider(),指定重试延迟为 $this->retryDelay()
        $handlerStack->push(Middleware::retry($this->retryDecider(), $this->retryDelay()));

        return new Client([
//            'http_errors' => false,
            'timeout' => $this->clientTimeout, //秒
            'connect_timeout' => $this->clientConnectTimeout, //秒
//            'handler' => $handlerStack //重试策略
        ]);
    }

    protected function retryDecider()
    {
        return function (
            $retries,
            Request $request,
            Response $response = null,
            RequestException $exception = null
        ) {
            // 超过最大重试次数，不再重试
            if ($retries >= PddClient::MAX_RETRIES) {
                return false;
            }

            if ($response) {
                parse_str($request->getUri()->getQuery(), $params);
                if (!empty($params)) {
                    $method = $params['method'] ?? "";
                    //发货失败请求重试
                    if (in_array($method, ['order.logisticsAdd'])) {
                        // 请求失败，继续重试
                        if ($exception instanceof ConnectException) {
                            return true;
                        }
                        $body = !is_array($response->getBody()) ? json_decode($response->getBody(), true) : $response->getBody();
                        // dy 错误码汇总 https://op.jinritemai.com/docs/guide-docs/161/1427
                        if (isset($body['code']) && in_array($body['code'], [20000, 60000])) {
                            return true;
                        }
                    }
                }
            }

            return false;
        };
    }

    protected function retryDelay()
    {
        return function ($numberOfRetries) {
            return 1000 * $numberOfRetries;
        };
    }
}
