<?php

namespace App\Services\ShengYiWang;

use App\Models\User;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;

class ShengYiWangClient
{
    /**
     * @var string
     */
    private $secret;
    /**
     * @var string
     */
    private $gateway;

    private $token;
    private $userId;
    /**
     * @var mixed
     */
    private $source = '';

    public function __construct()
    {
        $this->secret = 'go2CN|20220119thirdprint';
//        $this->gateway = 'http://third-print.go2b2b.com:8313';
//        $this->gateway = 'https://api.thirdprint.ximgs.net';
        $this->gateway = env('SYW_GATEWAY', 'https://api.thirdprint.ximgs.net');

    }

    public function checkSign($timestamp, $nonce, $sign)
    {
        // 生成签名 (SHA1加密)
        $checkSign = sha1($this->secret . $nonce . $timestamp);
        // 比较签名
        if ($checkSign !== $sign) {
            return false;
        }
        return true;
    }

    function sign($secret)
    {
        // 获取当前时间戳
        $timestamp = time();

        // 生成5位随机数字
        $nonce = str_pad(random_int(0, 99999), 5, '0', STR_PAD_LEFT);

        // 生成签名 (SHA1加密)
        $sign = sha1($secret . $nonce . $timestamp);

        // 返回结果作为关联数组
        return [
            'timestamp' => $timestamp,
            'nonce' => $nonce,
            'sign' => $sign
        ];
    }

    function getClient()
    {
        return new Client([
            'http_errors' => false,
        ]);
    }

    public function execute($method, $path, $params = [])
    {
        $sign = $this->sign($this->secret);

        $client = $this->getClient();

        switch ($method){
            case 'GET':
                $dataType = 'query';
                break;
            case 'POST':
                $dataType = 'json';
                break;
            default:
                $dataType = 'json';
        }
        $headers = [
            'TOKEN' => $this->token,
            'USER-ID' => $this->userId,
            'X-REQUEST-SOURCE' => $this->source,
            'timestamp' => $sign['timestamp'],
            'nonce' => $sign['nonce'],
            'sign' => $sign['sign']
        ];

        $response = $client->request($method, $this->gateway . $path, [
            $dataType => $params,
            'headers' => $headers
        ]);
        $responseArr = json_decode($response->getBody()->getContents(), true);
        \Log::info('syw execute', [
            'request' => [
                'method' => $method,
                'gateway' => $this->gateway,
                'path' => $path,
                'paramsType' => $dataType,
                'params' => $params,
                'headers' => $headers,
            ],
            'response' => $responseArr,
        ]);

        return $responseArr;
    }

    public function setUser(User $user): void
    {
        $this->token = $user->open_token;
        $this->userId = $user->open_user_id;
        $this->source = $user->open_user_source;
    }

    /**
     * @throws \Exception
     */
    public function handleResponse($execute): bool
    {
        Log::info('syw handleResponse', [$execute]);
        $errorCode = $execute['errorCode'] ?? '-1';
        switch ($errorCode){
            case 0:
                return true;
            default:
                $errorMsg = $execute['errorMsg'] ?? '无';
                $requestId = $execute['requestId'] ?? '?';
                throw new \Exception('SYW:'.$errorMsg."；$requestId");
        }
    }

}
