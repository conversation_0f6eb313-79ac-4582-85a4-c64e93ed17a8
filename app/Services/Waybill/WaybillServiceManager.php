<?php

namespace App\Services\Waybill;

use App\Constants\PlatformConst;
use App\Services\Waybill\CNLink\CNLinkApi;
use App\Services\Waybill\DY\DYApi;
use App\Services\Waybill\KS\KSApi;
use App\Services\Waybill\JD\JDApi;
use App\Services\Waybill\PDD\PDDApi;
use App\Services\Waybill\PDDWB\PDDWBApi;
use App\Services\Waybill\Taobao\NewTBApi;
use App\Services\Waybill\Taobao\TaoBaoApi;
use App\Services\Waybill\Taobao\TBApi;
use App\Services\Waybill\Wx\WxspApi;
use App\Services\Waybill\XHS\XHSApi;
use Illuminate\Support\Facades\Log;
use InvalidArgumentException;

class WaybillServiceManager
{
    protected static $initMap = [
        PlatformConst::WAYBILL_PDD_WB => PDDWBApi::class,
        PlatformConst::WAYBILL_TB     => NewTBApi::class,
        PlatformConst::WAYBILL_CNLINK => CNLinkApi::class,
        PlatformConst::WAYBILL_PDD    => PDDApi::class,
        PlatformConst::WAYBILL_TB_TOP => TaoBaoApi::class,
        PlatformConst::WAYBILL_DY     => DYApi::class,
        PlatformConst::WAYBILL_KS     => KSApi::class,
        PlatformConst::WAYBILL_JD     => JDApi::class,
        PlatformConst::WAYBILL_WXSP   => WXSPApi::class,
        PlatformConst::WAYBILL_XHS   => XHSApi::class,
    ];

    /**
     * 映射对应平台处理类
     * @param        $source
     * @param string $accessToken
     * @return AbstractWaybillService
     */
    public static function init($source, string $accessToken = ''): AbstractWaybillService
    {
        if (isset(self::$initMap[$source])) {
            return new self::$initMap[$source]($accessToken);
        }
        throw new InvalidArgumentException('不存在的面单source:' . $source);
    }

    /**
     * check source
     * @param $source
     * @return bool
     */
    public static function checkAuthSource($source): bool
    {
        if (isset(self::$initMap[$source])) {
            return true;
        }

        return false;
    }
}
