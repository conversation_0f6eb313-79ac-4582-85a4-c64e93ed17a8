<?php

namespace App\Services\Waybill\CNLink;

use App\Constants\ErrorConst;
use App\Constants\PlatformConst;
use App\Models\Shop;
use App\Services\Bo\PrintDataPackBo;
use App\Services\Bo\PrintPackBo;
use App\Services\Bo\SenderAddressBo;
use App\Services\Bo\WaybillsPrintDataBo;
use App\Services\Order\OrderServiceManager;
use App\Services\Waybill\AbstractWaybillService;
use App\Services\Waybill\Taobao\NewTBApi;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;

class CNLinkApi extends AbstractWaybillService
{
    protected $baseUrl = 'http://link.cainiao.com/gateway/link.do';
    private   $authConfig;
    private   $appKey;
    private   $appSecret;
    protected $is_test = false;
    protected $waybillPlatformType = PlatformConst::WAYBILL_CNLINK; // 电子面单平台类型


    public function __construct(string $accessToken = '')
    {
        $this->accessToken = $accessToken;
        $this->authConfig  = config('waybill.link');
        $this->appKey      = $this->authConfig['appkey'];
        $this->appSecret   = $this->authConfig['secret'];
    }

    protected $errorCodeMap = [
    	'S02' => '电子面单需重新授权',
    	'S23' => '电子面单需重新授权',
    ];

    public function isTest()
    {
        $this->is_test = true;
    }

    /**
     * 授权地址
     * @param $shopId
     * @return string
     */
    public function getLoginUrl($shopId)
    {
	    $state = [
		    'redirect_uri' => $this->authConfig['redirect_url'],
		    'state'        => $shopId,
	    ];

        return $this->authConfig['code_url'] . '?isvAppKey=' . $this->appKey . '&ext=' . base64_encode(json_encode($state)) . '&redirectUrl=' . $this->authConfig['code_redirect_url'];
    }

    /**
     * 获取access_token
     * @param string $code
     * @return array
     * @throws \Exception
     */
    public function getAccessToken(string $code)
    {
        $params   = [
            'accessCode' => $code,
            'isvAppKey'  => $this->appKey,
            'sign'       => md5($code . ',' . $this->appKey . ',' . $this->appSecret),
        ];
        $client   = new Client();
        $response = $client->get($this->authConfig['token_url'], [
            'query'   => $params,
            'verify'  => false,
            'headers' => [
                'Content-type' => 'application/json',
                "Accept"       => "application/json"
            ],
        ]);
        $result   = json_decode($response->getBody()->getContents(), true);
        //Log::info('cnLink access_token get', ['request' => $result]);
        if (!isset($result['accessTokens'])) {
            Log::error('cnLink access_token get Failed', ['request' => $result]);
            throw new \Exception('获取授权码失败，请重试！');
        }

        return [
            'access_token' => $result['accessTokens'][0]['accessToken'],
            'owner_id'     => $result['accessTokens'][0]['grantBy'],
            'owner_name'   => $result['accessTokens'][0]['grantByNick'],
            'expires_in'   => floor($result['accessTokens'][0]['expireDate'] / 1000) - time(),
            'expires_at'   => date('Y-m-d H:i:s', floor($result['accessTokens'][0]['expireDate'] / 1000)),
        ];
    }

	/**
	 * 数据格式转换
	 * @param $data
	 */
	public function convertFormat($data)
	{
		//xml
		if (!isJson($data)) {
			$data = simplexml_load_string($data, 'SimpleXMLElement', LIBXML_NOCDATA);
			$data = json_encode($data);  //objecct转json
		}

		return json_decode($data, true);
    }

	/**
	 * 请求接口
	 * @param $apiName
	 * @param array $data
	 * @return array|mixed
	 * @throws \Exception
	 */
    public function request($apiName, $data = array())
    {
        $param  = $this->createRequestParam($apiName, $data);
        $result = $this->Curl($this->baseUrl, $param, 'POST', true);
        if ($result['http_code'] != 200) {
	        throw new \Exception('HTTP status wrong!');
        }

	    $data = $this->convertFormat($result['data']);
	    if (isset($data['success']) && (!$data['success'] || $data['success'] == "false")) {
	    	//自定义错误提示
	    	$errorMsg = array_get($this->errorCodeMap, $data['errorCode'], false);
	    	if ($errorMsg){
			    throw new \Exception($errorMsg);
		    }
		    throw new \Exception($data['errorMsg']);
	    }

        return $data;
    }

    /**
     * 组装请求数据
     * @param string $apiName
     * @param array  $data
     * @return array
     */
    public function createRequestParam($apiName, $data)
    {
        $request_data = json_encode($data);
        $signature    = $this->sign($request_data);
        $params       = [
            'msg_type'             => $apiName,
            'logistic_provider_id' => $this->accessToken,
            'logistics_interface'  => $request_data,
            'data_digest'          => $signature
        ];
        $this->setRequestData($params);
        return $params;
    }

    /**
     * 签名
     * @param string $content
     * @return mixed
     */
    public function sign($content)
    {
        return base64_encode(md5($content . $this->appSecret, true));
    }

    /**
     * 订购关系查询接口
     * @param string $wpCode
     * @param string $serviceId
     * @return array|mixed
     * @throws \Exception
     */
    public function waybillSubscriptionQuery(string $wpCode = '', string $serviceId = ''):array
    {
        $msg_type     = 'TMS_WAYBILL_SUBSCRIPTION_QUERY';
        $data         = array(
            'cpCode' => $wpCode
        );
        $result       = $this->request($msg_type, $data);
        $waybillsInfo = [];
//        \Log::info(var_export($result, true));
        Log::info('菜鸟查询面单：'.json_encode($result));
        if (!isset($result['waybillApplySubscriptionCols'])) {
            return $waybillsInfo;
        }
        foreach ($result['waybillApplySubscriptionCols'] as $value) {
            $branchAccounts = [];
            foreach ($value['branchAccountCols'] as $key => $item) {
                $branchAccounts[$key] = [
                    'branch_code'        => $item['branchCode'] ?? 0,
                    'branch_name'        => $item['branchName'] ?? '',
                    'quantity'           => $item['quantity'] ?? 0,
                    'cancel_quantity'    => $item['cancelQuantity'] ?? 0,
                    'recycled_quantity'  => $item['printQuantity'] ?? 0,
                    'allocated_quantity' => $item['allocatedQuantity'] ?? 0,
                    'shipp_addresses'    => $item['shippAddressCols']
                ];
            }
            $waybillsInfo[] = [
                'branch_account_cols' => $branchAccounts,
                'wp_code'             => $value['cpCode'],
                'wp_type'             => $value['cpType']
            ];
        }

        return $waybillsInfo;
    }

    /**
     * 获取面单
     * @param     $sender
     * @param     $orders
     * @param     $template
     * @param int $packageNum
     * @return array
     */
	public function assemWaybillPackages($sender, $orders, $template, $packageNum = 1)
	{
		$type   = 'TMS_WAYBILL_GET';
		$data   = [];
        $errorArr = [];
        foreach ($orders as $order) {
			$idStr     = handleOrderIdStr($order);
            try {
                $applyInfo = $this->getWayBillRequestData($sender, $order->toArray(), $template, $packageNum);
                foreach ($applyInfo as $index=>$info) {
                    $tempIdStr = $idStr .'|'. $index;
                    $data[$tempIdStr] = [
                        'params' => $this->createRequestParam($type, $info), //只有一个包裹
                        'url'    => $this->baseUrl,
                    ];
                }
            }catch (\Exception $e){
                $errorArr[$idStr][] = $e->getMessage();
            }
		}
		$response = $this->poolCurl($data, 'POST', true);

		Log::debug('cn_link_response', [$response]);
		foreach ($response as $orderIdStr => $waybill) {
			if (is_array($waybill)) {
				foreach ($waybill as $module) {
					$printData            = json_decode($module->printData, true);
					$waybillsData = [
						'object_id'           => $module->objectId,
						'waybill_code'        => $module->waybillCode,
						'print_data'          => $printData['encryptedData'],
						'parent_waybill_code' => isset($module->parentWaybillCode) ? $module->parentWaybillCode : ''
					];
				}
				$result[$orderIdStr][] = $waybillsData;
			}else {
				Log::error($type . '取号错误', [$orderIdStr => $waybill]);
				$result[$orderIdStr][] = $waybill;
			}
		}
        foreach ($errorArr as $idStr => $msg) {
            $result[$idStr] = $msg;
        }

		return $result;
	}

    /**
     * 电子面单云打印取号
     * @param $sender
     * @param $order
     * @param $template
     * @param $packageNum
     * @return array|bool|mixed
     */
    public function waybillGet($sender, $order, $template, $packageNum)
    {
        $msg_type = 'TMS_WAYBILL_GET';
        $applyInfo = $this->getWayBillRequestData($sender, $order, $template, $packageNum);
        $result    = [];
        foreach ($applyInfo as $info) {
            try {
                $waybill = $this->request($msg_type, $info);
                $waybillsData = [];
                foreach ($waybill['waybillCloudPrintResponseList'] as $module) {
                    $printData            = json_decode($module['printData'], true);
                    $waybillsData = [
                        'object_id'           => $module['objectId'],
                        'waybill_code'        => $module['waybillCode'],
                        'print_data'          => $printData['encryptedData'],
                        'parent_waybill_code' => isset($module['parentWaybillCode']) ? $module['parentWaybillCode'] : ''
                    ];
                }
                $result[] = $waybillsData;
            } catch (\Exception $e) {
                \Log::error("获取电子面单失败" . $msg_type, [$e->getMessage(), $e->getTraceAsString()]);
                $result[] = $e->getMessage();
            }
        }

        return $result;
    }

    /**
     * 请求电子面单
     * @param SenderAddressBo $branchAddressBo
     * @param PrintPackBo[] $printPackBoList
     * @param array $template
     * @param int $packageNum
     * @return PrintDataPackBo[]
     * <AUTHOR>
     */
    public function assemWaybillPackageByPrintPackBo(SenderAddressBo $branchAddressBo, array $printPackBoList, array $template, int $packageNum): array
    {
        $requestDataList = $this->getWayBillRequestDataByPrintPackBo($branchAddressBo, $printPackBoList, $template, $packageNum);
        $requestDataArr = [];

        $type = 'waybill.cloudPrint.applyNew';
//        $requestBatchIndexArr = []; // 批量请求时，根据pack_id获取请求的index
        foreach ($requestDataList as $index => $item) {
            $requestDataArr[$index] = [
                'params' => $this->createRequestParam($type, $item),
                'url' => $this->baseUrl,
            ];
        }
        $responseArr = $this->poolCurl($requestDataArr,'post');
        $responseArr = json_decode(json_encode($responseArr), true);
        Log::debug('cn_response', $responseArr);
        $successInfos = [];
        $errorInfos = [];
        foreach ($responseArr as $index => $response) {
            if (!empty($response['result']['result'])) {
                $successInfos = array_merge($successInfos, $response['result']['result']);
            }
//            if (!empty($response['data']['err_infos'])) {
//                $errorInfos = array_merge($errorInfos, $response['data']['err_infos']);
//            }
        }
        $successInfos = array_pluck($successInfos, null, 'objectId');
        $errorInfos = array_pluck($errorInfos, null, 'objectId');

        $printDataPackBoList = [];
        // 获取面单号
        foreach ($printPackBoList as $index => $printPackBo) {
            $printDataPackBo = new PrintDataPackBo();
            $printDataPackBo->copyByPrintPackBo($printPackBo);
            if (isset($successInfos[$printDataPackBo->request_id])) {
                $successInfo = $successInfos[$printDataPackBo->request_id];
                $printDataPackBo->waybill_code = $successInfo['waybillCode'];
                $printDataPackBo->wp_code = $template['wpCode'];
                $printDataPackBo->package_id = $successInfo['objectId'];
                $waybillsPrintDataBo = new WaybillsPrintDataBo();
                $waybillsPrintDataBo->copyByPrintDataPackBo($printDataPackBo);
                $waybillsPrintDataBo->parent_waybill_code = $successInfo['parentWaybillCode'] ?? '';
                $successPrintData = json_decode($successInfo['printData'],true);
                $waybillsPrintDataBo->encrypted_data = $successPrintData['encryptedData'];
                $waybillsPrintDataBo->sign = $successPrintData['signature'];
                $waybillsPrintDataBo->param_str = 'ver='.$successPrintData['ver'];
                $printDataPackBo->setWaybillsPrintData($waybillsPrintDataBo);
            } else {
//                $requestIndex = $requestBatchIndexArr[$printPackBo->request_id];
                $thisResponse = $responseArr[$index];
                $thisRequest = $requestDataList[$index];
                $msg = '';
                if (is_string($thisResponse)){
                    $msg = $thisResponse;
                }else{
                    try {
                        $this->setRequestData($thisRequest);
                        $this->handleResponse($thisResponse);
                    } catch (\Exception $e) {
                        $msg = $e->getMessage();
                    }
                }

                if (!empty($msg)) {
                    $printDataPackBo->setError([ErrorConst::PLATFORM_ERROR[0], $msg]);
                } else {
                    $printDataPackBo->setError(ErrorConst::WAYBILL_GET_ERROR);
                }
            }
            $printDataPackBoList[] = $printDataPackBo;
        }

        return $printDataPackBoList;
    }
    /**
     * 设置电子面单请求数据
     * @param $sender
     * @param $order
     * @param $template
     * @param $packageNum
     * @return array
     */
    private function getWayBillRequestData($sender, $order, $template, $packageNum)
    {
        //抖音快手平台需要转化收件人信息
        if ((in_array(config('app.platform'), [PlatformConst::DY]) || (config('app.platform')== PlatformConst::KS && ksEncryptSwitch($order['shop_id']))) &&
            empty($order['receiver_phone']) && isset($order['order_cipher_info']) && !empty($order['order_cipher_info'])) {
            $decryptData = [];
            $decryptField = ['tid', 'receiver_name', 'receiver_phone', 'receiver_address'];
            foreach ($decryptField as $column) {
                if ($column == 'tid') {
                    $decryptData[$column] = array_get($order, $column);
                } else {
                    //先置空收件人信息 防止解密失败取到有问题的面单
                    $order[$column] = '';
                    $decryptData[$column] = $order['order_cipher_info'][$column.'_ciphertext'];
                }
            }
            $orderService = OrderServiceManager::create();
            $orderService->setUserId($order['user_id']);
            $shop = Shop::query()->where('id', $order['shop_id'])->first();
            $orderService->setShop($shop);
            $result = $orderService->batchDecrypt($decryptData);
            \Log::info('抖音/快手订单使用菜鸟站外电子面单 解密数据：'.json_encode($result));
            if (!empty($result)) {
                foreach ($result as $key => $val) {
                    $order[$key] = $val;
                }
            }
        }
        $returnArr = [];
        $num       = 1;
        //取真实包裹数量
        if ($packageNum < 0 ) {
            $packageNum = $order['packageNum'];
        }
        while ($num <= $packageNum) {
            //发件人信息
            $senderInfo['mobile']              = $sender['mobile'];
            $senderInfo['phone']               = '';
            $senderInfo['name']                = $sender['sender_name'];
            $senderInfo['address']['province'] = $sender['province'];
            $senderInfo['address']['city']     = $sender['city'];
            $senderInfo['address']['district'] = $sender['district'];
            $senderInfo['address']['town']     = '';
            $senderInfo['address']['detail']   = $sender['address'];

            //面单信息
            $tradeOrderInfoDtos = [];
            //订单信息
            $tradeOrderInfoDto['logisticsServices'] = '';

            //增值服务
            if (!empty($template['service_list'])) {
                $tradeOrderInfoDto['logisticsServices'] = $template['service_list'];
            }
            $tradeOrderInfoDto['objectId']          = isset($order['request_id']) ? $order['request_id'][$num] : $order['id'] .'_' . rand(1111, 9999);
            $tradeOrderInfoDto['orderInfo'] = [
                'orderChannelsType' => 'OTHERS',
                'tradeOrderList'    => [
                    isset($order['tid']) ? $order['tid'] . '-' . rand(0000, 9999) : $order['id'] . '-' . rand(0000, 9999)
                ]
            ];

            $items = [];
            if (array_key_exists('order_item', $order)) {
                foreach ($order['order_item'] as $good) {
                    $temp = [];
                    $temp['count'] = $good['goods_num'];
                    $temp['name']  = $good['goods_title'] ? $good['goods_title'] : '';
                    $temp['name']  = clearStrSpecialChar($temp['name']);
                    $items[]       = $temp;
                }
            }
            if (array_key_exists('production_type', $order)) {
                $items[] = [
	                'count' => is_int($order['num']) ? $order['num'] : 1,
                    'name'  => empty($order['production_type']) ? $order['goods_info'] : $order['production_type'],
                ];
            }

            $tradeOrderInfoDto['packageInfo'] = [
                'id'     => $order['tid'] ?? $order['id'] .'-' . rand(1111, 9999),
                'items'  => $items,
                'volume' => 1,
                'weight' => 1,
            ];
            $tradeOrderInfoDto['recipient']   = [
                'address' => [
                    'city'     => $order['receiver_city'],
                    'detail'   => $order['receiver_address'],
                    'district' => $order['receiver_district'],
                    'town'     => $order['receiver_town'],
                    'province' => $order['receiver_state'] ?? $order['receiver_province'],
                ],
                'mobile'  => $order['receiver_phone'],
                'name'    => $order['receiver_name'],
                'phone'   => $order['receiver_tel'] ?? '',
            ];
            $tradeOrderInfoDto['templateUrl'] = $template['template_url'];
            $tradeOrderInfoDto['userId']      = 12;//userId字段目前无意义，随便传个数字即可
            $tradeOrderInfoDtos[]             = $tradeOrderInfoDto;
            //设置主体信息
            $data                       = [];
            $data['cpCode']             = $template['wp_code'];
            $data['dmsSorting']         = false;
            $data['needEncrypt']        = false;
            $data['resourceCode']       = '无';
            $data['storeCode']          = '无';
            $data['tradeOrderInfoDtos'] = $tradeOrderInfoDtos;
            $data['sender']             = $senderInfo;
            $returnArr[]                = $data;
            ++$num;
        }

        return $returnArr;
    }

	/**
	 * 电子面单更新接口
	 * @param array $order
	 * @param Shipping $shipping
	 * @return array|bool
	 * @throws \Exception
	 */
    public function tmsWaybillUpdate(Array $order, Shipping $shipping)
    {
        $msg_type = 'TMS_WAYBILL_UPDATE';
        $data     = $this->getWayBillUpdateData($order, $shipping);
        $result   = $this->request($msg_type, $data);
        if (!$result['success']) {
            return false;
        }
        $labelData   = $result['printData'];
        $waybillCode = $result['waybillCode'];

        return ['labelData' => json_decode($labelData, true), 'trackNumber' => $waybillCode];
    }

    /**
     * 设置电子面单请求更新数据
     * @param array    $order
     * @param Shipping $shipping
     */
    private function getWayBillUpdateData(Array $order, Shipping $shipping)
    {
        $items = [];
        $data  = [];
        foreach ($order['goods'] as $key => $good) {
            $items[$key]['count'] = $good['goods_count'];
            $items[$key]['name']  = $good['goods_spec'];
        }
        $data['cpCode']            = $shipping->cn_code;
        $data['waybillCode']       = $order['tracking_number'];
        $data['objectId']          = 1;
        $data['logisticsServices'] = '';
        $data['packageInfo']       = [
            'items'  => $items,
            'volume' => 1,
            'weight' => 1,
        ];
        $data['sender']            = [
            'mobile' => $order['sender_info']['mobile'],
            'phone'  => $order['sender_info']['phone'],
            'name'   => $order['sender_info']['name'],
        ];
        $data['recipient']         = [
            'address' => [
                'city'     => $order['receiver_info']['city'],
                'detail'   => $order['receiver_info']['address'],
                'district' => $order['receiver_info']['town'],
                'province' => $order['receiver_info']['province'],
                'town'     => '',
            ],
            'mobile'  => $order['receiver_info']['mobile'],
            'name'    => $order['receiver_info']['name'],
            'phone'   => $order['receiver_info']['phone'],
        ];
        $data['templateUrl']       = $shipping->cn_url;

        return $data;
    }

    /**
     * 作废电子面单
     * @param string $cpCode
     * @param string $waybillCode
     * @param string $platformWaybillId
     * @return bool|mixed
     * @throws \Exception
     */
    public function wayBillCancelDiscard(string $cpCode, string $waybillCode,string $platformWaybillId = '')
    {
        $msg_type = 'TMS_WAYBILL_DISCARD';
        $data     = array(
            'cpCode'      => $cpCode,
            'waybillCode' => $waybillCode
        );
        $result   = $this->request($msg_type, $data);
        Log::info($msg_type, [$result]);
        if (!$result['success']) {
            throw new \Exception($result['errorMsg'] ?? "取消失败");

            return false;
        }

        return $result['discardResult'];
    }

    public function getCloudPrintStdTemplates(string $cpCode = ''):array
    {
        $msg_type  = 'CLOUDPRINT_STANDARD_TEMPLATES';
        $data      = array(
            'cpCode' => $cpCode
        );
        $result    = $this->request($msg_type, $data);
        if (!$result['success']) {
            throw new \Exception('标准模板查询失败!');
        }
        $standardTemplates = [];
        foreach ($result['data'][0]['standardTemplateDOs'] as $template) {
            $standardTemplates[$template['standardWaybillType']] = [
                'standard_waybill_type'  => $template['standardWaybillType'],
                'standard_template_name' => $template['standardTemplateName'],
                'standard_template_url'  => $template['standardTemplateUrl'],
            ];
        }

        return $standardTemplates;
    }

    public function getCloudPrintStdTemplatesNew(string $cpCode = '',?string $extendedInfo=null)
    {
        $msg_type  = 'CLOUDPRINT_STANDARD_TEMPLATES';
        $data      = array(
            'cpCode' => $cpCode
        );
        $result    = $this->request($msg_type, $data);
        if (!$result['success']) {
            throw new \Exception('标准模板查询失败!');
        }
        $standardTemplates = [];
        foreach ($result['data'][0]['standardTemplateDOs'] as $template) {
            $standardTemplates[] = [
                'standard_waybill_type'  => $template['standardWaybillType'],
                'standard_template_name' => $template['standardTemplateName'],
                'standard_template_url'  => $template['standardTemplateUrl'],
            ];
        }

        return $standardTemplates;
    }

	public function sendOrderLogisticsTraceMsg(string $receiverPhone, string $expressCode, string $expressNo)
	{
		//todo 物流轨迹订阅
	}


    public function assemWaybillPackagesForOpenApi($platform, $sender, $orders, $wpCode, $waybillType, $waybillTemp, $packageNum = 1,$productType=null)
    {
        //并行异步请求
        $type   = 'TMS_WAYBILL_GET';
        $data   = [];
        foreach ($orders as $order) {
            $idStr     = handleOrderIdStr($order);
            $applyInfo = $this->getWayBillRequestDataForOpenApi($platform, $sender, $order->toArray(), $wpCode, $waybillType, $waybillTemp, $packageNum);

            $data[$idStr] = [
                'params' => $this->createRequestParam($type, $applyInfo[0]), //只有一个包裹
                'url'    => $this->baseUrl,
            ];
        }
        $response = $this->poolCurl($data, 'POST', true);

        Log::debug('cn_link_response', [$response]);
        foreach ($response as $orderIdStr => $waybill) {
            if (is_array($waybill)) {
                foreach ($waybill as $module) {
                    $printData            = json_decode($module->printData, true);
                    $insertPrint = [
                        'encryptedData' =>$printData['encryptedData'],
                        'templateUrl'   =>$printData['templateUrl'],
                        'signature'     => $printData['signature'],
                        'ver'           => $printData['ver']
                    ];
                    $waybillsData = [
                        'object_id'           => $module->objectId,
                        'waybill_code'        => $module->waybillCode,
                        'print_data'          => json_encode($insertPrint),
                        'parent_waybill_code' => isset($module->parentWaybillCode) ? $module->parentWaybillCode : ''
                    ];
                }
                $result[$orderIdStr][] = $waybillsData;
            }else {
                Log::error($type . '取号错误', [$orderIdStr => $waybill]);
                $result[$orderIdStr][] = $waybill;
            }
        }

        return $result;
    }

    private function getWayBillRequestDataForOpenApi($platform, $sender, $order, $wpCode, $waybillType, $waybillTemp, $packageNum)
    {
        $returnArr = [];
        $num       = 1;
        while ($num <= $packageNum) {
            //发件人信息
            $senderInfo['mobile']              = $sender['mobile'];
            $senderInfo['phone']               = '';
            $senderInfo['name']                = $sender['sender_name'];
            $senderInfo['address']['province'] = $sender['province'];
            $senderInfo['address']['city']     = $sender['city'];
            $senderInfo['address']['district'] = $sender['district'];
            $senderInfo['address']['town']     = '';
            $senderInfo['address']['detail']   = $sender['address'];

            //面单信息
            $tradeOrderInfoDtos = [];
            //订单信息
            $tradeOrderInfoDto['logisticsServices'] = '';
            $tradeOrderInfoDto['objectId']          = $order['package_id']??$order['tid'] ?? $order['id'] .'-' . rand(1111, 9999);
            $tradeOrderInfoDto['orderInfo'] = [
                'orderChannelsType' => 'OTHERS',
                'tradeOrderList'    => [
                    isset($order['tid']) ? $order['tid'] . '-' . rand(0000, 9999) : $order['id'] . '-' . rand(0000, 9999)
                ]
            ];

            $items = [];
            if (array_key_exists('order_item', $order)) {
                foreach ($order['order_item'] as $good) {
                    $temp = [];
                    $temp['count'] = $good['goods_num'];
                    $temp['name']  = $good['goods_title'] ? $good['goods_title'] : '';
                    $temp['name']  = clearStrSpecialChar($temp['name']);
                    $items[]       = $temp;
                }
            }
            if (array_key_exists('production_type', $order)) {
                $items[] = [
                    'count' => is_int($order['num']) ? $order['num'] : 1,
                    'name'  => empty($order['production_type']) ? $order['goods_info'] : $order['production_type'],
                ];
            }

            $tradeOrderInfoDto['packageInfo'] = [
                'id'     => $order['tid'] ?? $order['id'] .'-' . rand(1111, 9999),
                'items'  => $items,
                'volume' => 1,
                'weight' => 1,
            ];
            $tradeOrderInfoDto['recipient']   = [
                'address' => [
                    'city'     => $order['receiver_city'],
                    'detail'   => $order['receiver_address'],
                    'district' => $order['receiver_district'],
                    'town'     => '',
                    'province' => $order['receiver_state'] ?? $order['receiver_province'],
                ],
                'mobile'  => $order['receiver_phone'],
                'name'    => $order['receiver_name'],
                'phone'   => $order['receiver_tel'] ?? '',
            ];
            $tradeOrderInfoDto['templateUrl'] = $this->getTemplateUrl($waybillType, $waybillTemp, $platform, $wpCode);
            $tradeOrderInfoDto['userId']      = 12;//userId字段目前无意义，随便传个数字即可
            $tradeOrderInfoDtos[]             = $tradeOrderInfoDto;
            //设置主体信息
            $data                       = [];
            $data['cpCode']             = $wpCode;
            $data['dmsSorting']         = false;
            $data['needEncrypt']        = false;
            $data['resourceCode']       = '无';
            $data['storeCode']          = '无';
            $data['tradeOrderInfoDtos'] = $tradeOrderInfoDtos;
            $data['sender']             = $senderInfo;
            $returnArr[]                = $data;
            ++$num;
        }

        return $returnArr;
    }

    public function updateWaybillData($sender, $order, $template, $waybillCode)
    {
        $msg_type = 'TMS_WAYBILL_UPDATE';
        $applyInfo = $this->getWayBillUpdateRequestData($sender, $order, $template, $waybillCode);
        $result    = [];
        foreach ($applyInfo as $info) {
            try {
                $waybill = $this->request($msg_type, $info);
                \Log::info('CNLinkApi 更新单子面单接口:',[$waybill]);
                if (!isset($waybill['printData'])) {
                    Log::error('API=>' . $msg_type, [$waybill]);
                }
                $printData = json_decode($waybill['printData'], true);
                $result = [
                    'waybill_code'        => $waybill['waybillCode'],
                    'print_data'          => $printData['encryptedData'],
                ];
            } catch (\Exception $e) {
                \Log::error("更新电子面单失败" . $msg_type, [$e->getTraceAsString()]);
            }
        }
        return $result;
    }
    private function getWayBillUpdateRequestData($sender, $order, $template, $waybillCode)
    {
        $returnArr = [];
        //发件人信息
        $senderInfo['mobile']              = $sender['mobile'];
        $senderInfo['phone']               = '';
        $senderInfo['name']                = $sender['sender_name'];
        $senderInfo['address']['province'] = $sender['province'];
        $senderInfo['address']['city']     = $sender['city'];
        $senderInfo['address']['district'] = $sender['district'];
        $senderInfo['address']['town']     = '';
        $senderInfo['address']['detail']   = $sender['address'];
        $items = [];
        if (array_key_exists('order_item', $order)) {
            foreach ($order['order_item'] as $good) {
                $temp = [];
                $temp['count'] = $good['goods_num'];
                $temp['name']  = $good['goods_title'] ? $good['goods_title'] : '';
                $temp['name']  = clearStrSpecialChar($temp['name']);
                $items[]       = $temp;
            }
        }
        if (array_key_exists('production_type', $order)) {
            $items[] = [
                'count' => is_int($order['num']) ? $order['num'] : 1,
                'name'  => empty($order['production_type']) ? $order['goods_info'] : $order['production_type'],
            ];
        }
        $packageInfo = [
            'id'     => $order['tid'] ?? $order['id'] .'-' . rand(1111, 9999),
            'items'  => $items,
            'volume' => 1,
            'weight' => 1,
        ];
        $recipient   = [
            'address' => [
                'city'     => $order['receiver_city'],
                'detail'   => $order['receiver_address'],
                'district' => $order['receiver_district'],
                'town'     => '',
                'province' => $order['receiver_state'] ?? $order['receiver_province'],
            ],
            'mobile'  => $order['receiver_phone'],
            'name'    => $order['receiver_name'],
            'phone'   => $order['receiver_tel'] ?? '',
        ];
        //设置主体信息
        $data                       = [];
        $data['cpCode']             = $template['wp_code'];
        $data['needEncrypt']        = false;
        $data['sender']             = $senderInfo;
        $data['recipient']          = $recipient;
        $data['packageInfo']        = $packageInfo;
        $data['waybillCode']       = $waybillCode;
        $data['templateUrl']        = $template['template_url'];
        $returnArr[]                = $data;
        return $returnArr;
    }

    public function assemFactoryWaybillPackages($sender, $factoryOrders, $template)
    {
        // TODO: Implement assemFactoryWaybillPackages() method.
    }

    public function getAllCompany(string $wpCode = ''): array
    {
        // TODO: Implement getAllCompany() method.
    }

    /**
     * 获取电子面单请求数据
     * @param SenderAddressBo $sender
     * @param PrintPackBo[] $printPackBoList
     * @param array $template
     * @param int $packageNum
     * @return array
     * <AUTHOR>
     */
    private function getWayBillRequestDataByPrintPackBo(SenderAddressBo $sender, array $printPackBoList, array $template, int $packageNum): array
    {
        //发件人信息
        $senderInfo = [
            'address' => [
                'province' => $sender->province,
                'city' => $sender->city,
                'district' => $sender->district,
                'town' => $sender->street ?? '',
                'detail' => $sender->address,
            ],
            'name' => $sender->sender_name,
            'mobile' => $sender->mobile,
            'phone' => ''
        ];
        $returnArr = [];
        $num       = 1;
//        $packageNum = 1;
        //幂等性问题后面加上随机数
//        $tradeOrderList = isset($order['tid']) ? $order['tid'] . '-' . rand(0000, 9999) : $order['id'] . '-' . rand(0000, 9999);
//        $isPtOrder = true;
        //取真实包裹数量
//        if ($packageNum < 0 ) {
//            $packageNum = $order['packageNum'];
//        }
        foreach ($printPackBoList as $printDataPackBo) {
            //面单信息
            $tradeOrderInfoDtos = [];
            //订单信息
            $tradeOrderInfoDto['logisticsServices'] = '';
//            //增值服务
//            if (!empty($template['service_list'])) {
//                $tradeOrderInfoDto['logistics_services'] = $template['service_list'];
//            }

            $serviceListStr = $template['service_list'];
            if (!empty($serviceListStr)) {
                $tradeOrderInfoDto['logisticsServices'] =NewTBApi::buildOrderVasList($serviceListStr,$printDataPackBo->getInsureAmount());
            }


            $order = $printDataPackBo->master_order_info;
            $order = $order->toArray();
            $tid = $this->getTidByOrder($order);
            $isPtOrder = $printDataPackBo->isPlatformOrder();

            $tradeOrderInfoDto['objectId']          = $printDataPackBo->request_id;
            $tradeOrderInfoDto['orderInfo']         = [
                'orderChannelsType' => 'OTHERS',
                'tradeOrderList'    => [
                    $tid
                    //子母件订单列表必须相同
//                    in_array($template['wp_code'],self::ZIMUJIANMAP) ? $tradeOrderList :
//                        (isset($order['tid']) ? $order['tid'] . rand(0000, 9999) : $order['id'] . rand(0000, 9999))
                ]
            ];
            $items = [];
            foreach ($printDataPackBo->print_order_item_bo_list as $printOrderItemBo) {
                $items[] = [
                    'name' => $printOrderItemBo->order_item_info['sku_value'],
                    'count' => $printOrderItemBo->num,
                ];
            }
//            if (array_key_exists('production_type', $order)) {
//                $items[] = [
//                    'count' => is_int($order['num']) ? $order['num'] : 1,
//                    'name'  => empty($order['production_type']) ? $order['goods_info'] : $order['production_type'],
//                ];
//            }

            $tradeOrderInfoDto['packageInfo'] = [
//                'id'                   => $num,
                'id'                   => $printDataPackBo->request_id,
                'items'                => $items,
                'volume'               => 1,
                'weight'               => 1,
                'totalPackagesCount' => $packageNum,
            ];
            //快运包裹必传参数不然会报错
            if (in_array($template['wp_code'], NewTBApi::ZIMUJIANMAP)) {
                $tradeOrderInfoDto['packageInfo'] = array_merge($tradeOrderInfoDto['packageInfo'],[
                    'packagingDescription' => '包装方式',//大件快运包装方式
                    'goodsDescription'     => '描述',//大件快运中的货品描述
                    'length'                => '100',//包裹长
                    'width'                 => '100',//包裹宽
                    'height'                => '50',//包裹高
                    'goodValue'            => $order['payment']??'',//物品价值，单位元
                ]);
            }
            $tradeOrderInfoDto['recipient']    = [
                'address' => [
                    'city'     => $order['receiver_city'],
                    'detail'   => $order['receiver_address'],
                    'district' => $order['receiver_district'],
                    'town'     => $order['receiver_town'],
                    'province' => $order['receiver_state'] ?? $order['receiver_province'],
                ],
                'mobile'  => $order['receiver_phone'],
                'name'    => $order['receiver_name'],
                'phone'   => $order['receiver_tel'] ?? '',
                'caid'   => $order['order_cipher_info']['oaid'] ?? '',
            ];
            $tradeOrderInfoDto['templateURL'] = $template['template_url'];
            $tradeOrderInfoDto['userId']      = 1;//userId字段目前无意义，随便传个数字即可
            $tradeOrderInfoDtos[]              = $tradeOrderInfoDto;
            //设置主体信息
            $data                          = [];
            $data['cpCode']               = $template['wp_code'];
            $data['brandCode']            = $template['company']['branch_code'];
            $data['needEncrypt']          = true;
            $data['tradeOrderInfoDTOs'] = $tradeOrderInfoDtos;
            $data['sender']                = $senderInfo;
            $temp = [];
            $temp['waybillCloudPrintApplyNewRequest'] = json_encode($data);
            $temp['clientInfoDTO'] = '{}';
            $returnArr[]                   = $temp;
            ++$num;
        }

        return $returnArr;
    }
}
