<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2022/1/19
 * Time: 14:47
 */

namespace App\Services\Traits;


trait CopyAttributeTrait
{
    /**
     * 复制对象的属性
     * <AUTHOR>
     * @param $class
     * @return $this
     * @throws \ReflectionException
     */
    public function copyByObject($class)
    {
        $reflectionClass = new \ReflectionClass($class);
        $properties = $reflectionClass->getProperties();

        foreach ($properties as $property) {
            $name = $property->getName();
            if (is_string($name) && property_exists($this, $name)) {
                $method = 'get' . ucfirst($name);
                if ($reflectionClass->hasMethod($method)) {
                    $value = $reflectionClass->getMethod($method)->invoke($class);
                }else{
                    $value = $property->getValue($class);
                }
                $method = 'set' . ucfirst($name);
                if (method_exists($this, $method)) {
                    $this->$method($value);
                }else{
                    $this->$name = $value;
                }
            }
        }
        return $this;
    }
    public function copyByArr(array $data)
    {
        foreach ($data as $name => $value) {
            if (is_string($name) && property_exists($this, $name)) {
                $this->$name = $value;
            }
        }
    }

}
