<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2022/1/17
 * Time: 17:07
 */

namespace App\Services\Traits;

trait ErrorInfoTrait
{
    public $message = '';
    public $error_code = 0;
    public $code = 200;

    private $has_error = false;

    /**
     * 设置错误
     * <AUTHOR>
     * @param array $error ErrorConst
     * @param string $appendMsg 附加的错误信息
     * @return $this
     */
    public function setError(array $error, string $appendMsg = '')
    {
        $this->has_error = true;
        $this->error_code = $error[0];
        $msg = $error[1];
        if (!empty($appendMsg)) {
            $msg .= '：' . $appendMsg;
        }
        $this->message = $msg;
        $this->code = 500;
        return $this;
    }

    public function getErrorMessage(): string
    {
        return $this->message;
    }
    public function getErrorCode(): int
    {
        return $this->error_code;
    }
    public function getError(): array
    {
        return [$this->error_code, $this->message];
    }
    public function hasError(): bool
    {
        return $this->has_error;
    }
}
