<?php

namespace App\Services\Goods;

/**
 * 备货单的请求
 */
class PickingGoodsRequest
{
    public $keyword;
    public $lockedStatus;
    public $orderStatus;
    public $printStatus;
    public $refundStatus;
    public $skuValueList;
    public $timeField;
    public $beginAt;
    public $endAt;
    public $ownerIdList = [];
    public $currentShopId;
    public $skuKeyword;
    public $offset;
    public $limit;
    public $sort;
    public $goodsSort;
    public $goodsId;
    /**
     * @var ?int 是否预售 null 不限 0不预售 1预售是
     */
    public $preSaled;


    /**
     * @param $keyword
     * @param $lockedStatus
     * @param $orderStatus
     * @param $printStatus
     * @param $refundStatus
     * @param $skuValueList
     * @param $timeField
     * @param $beginAt
     * @param $endAt
     * @param array $ownerIdList
     * @param $currentShopId
     * @param $skuKeyword
     * @param int $limit
     * @param int $offset
     * @param string $sort
     * @param int $goodsId
     * @param string $goodsSort
     * @param null $preSaled
     */
    public function __construct($keyword, $lockedStatus, $orderStatus, $printStatus, $refundStatus, $skuValueList,
                                $timeField, $beginAt, $endAt, array $ownerIdList, $currentShopId, $skuKeyword,
                                $limit = 0, $offset = 0, $sort = 'pay_at_min asc', $goodsId = 0, $goodsSort = '',$preSaled = null)
    {
        $this->keyword = $keyword;
        $this->lockedStatus = $lockedStatus;
        $this->orderStatus = $orderStatus;
        $this->printStatus = $printStatus;
        $this->refundStatus = $refundStatus;
        $this->skuValueList = $skuValueList;
        $this->timeField = $timeField;
        $this->beginAt = $beginAt;
        $this->endAt = $endAt;
        $this->ownerIdList = $ownerIdList;
        $this->currentShopId = $currentShopId;
        $this->skuKeyword = $skuKeyword;
        $this->limit = $limit;
        $this->offset = $offset;
        $this->sort = $sort;
        $this->goodsId = $goodsId;
        $this->goodsSort = $goodsSort;
        $this->preSaled = $preSaled;

    }
}
