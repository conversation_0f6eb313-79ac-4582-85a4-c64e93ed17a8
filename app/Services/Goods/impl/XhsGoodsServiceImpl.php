<?php

namespace App\Services\Goods\impl;

use App\Constants\ErrorConst;
use App\Constants\PlatformConst;
use App\Exceptions\ApiException;
use App\Exceptions\ClientException;
use App\Exceptions\OrderException;
use App\Http\Controllers\Goods\GoodsSearchRequest;
use App\Http\Controllers\Goods\GoodsSearchResponse;
use App\Models\Goods;
use App\Models\Shop;
use App\Services\Client\DyClient;
use App\Services\Client\XhsClient;
use GuzzleHttp\Client;
use GuzzleHttp\Pool;
use GuzzleHttp\Psr7\Response;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;

/**
 * 小红书的Goods 服务
 */
class XhsGoodsServiceImpl extends \App\Services\Goods\AbstractGoodsService
{
    /**
     * 是否存在下一页
     * @var bool
     */
    public $hasNext = false;


    protected $platformType = PlatformConst::PLATFORM_TYPE_XHS;

    /**
     * 获取商品列表
     * @param int $pageSize ,
     * @param int $currentPage
     * @return array
     * @throws ApiException
     * @throws ClientException
     * @throws OrderException
     */
    protected function sendGetGoods(int $pageSize, int $currentPage = 1)
    {
        $this->hasNext = false;
        $client = XhsClient::newInstance($this->accessToken);

        $data = [
            'pageNo' => $currentPage,
            'pageSize' => $pageSize,
        ];
        $result = $client->execute('product.getDetailSkuList', $data);
        $client->handleErrorCode($result);
        if (isset($result['data']['total'])) {
            $this->goodsTotalCount = $result['data']['total'];
        } else {
            $this->goodsTotalCount = 0;
        }
        if (count($result['data']['data']) == $result['data']['pageSize']) {
            $this->hasNext = true;
        }
        return $result['data']['data'];
    }


    public function formatToGoods(array $goods): array
    {
        $list = [];
        foreach ($goods as $index => $good) {
            if (!empty($good)) {
                $list[] = $this->formatToGood($good);
            }
        }

        return $list;
    }

    /**
     * 商品构建
     * @param array $goods
     * @return array
     */
    public function formatToGood(array $goods): array
    {
        $skus = [];
        $skuValue = '';
        foreach ($goods['sku']['variants'] as $value) {
            $skuValue .= $value['value'].';';
        }
        $skus[] = [
            "type" => $this->platformType,
            "sku_id" => $goods['sku']['id'],
            "sku_value" => $skuValue,
            "outer_id" => $goods['sku']['erpCode'],
            "outer_goods_id" => '',
            "sku_pic" => $goods['item']['images'][0],
            "is_onsale" => $goods['sku']['buyable'] ? Goods::IS_ONSALE_YES : Goods::IS_ONSALE_NO,
        ];

        return [
            "type" => $this->platformType,
            'num_iid' => $goods['item']['id'],
            'outer_goods_id' => '',
            'goods_title' => $goods['item']['name'],
            'goods_pic' => $goods['item']['images'][0],
            'is_onsale' => Goods::IS_ONSALE_YES,
            'goods_created_at' => date('Y-m-d H:i:s', $goods['item']['createTime']/1000),
            'goods_updated_at' => date('Y-m-d H:i:s', $goods['item']['updateTime']/1000),
            'skus' => $skus
        ];
    }

    /**
     * 获取单个商品
     * @param String $numIid
     * @param String $outProductId
     * @param String $showDraft
     * @return \App\Services\Goods\mixe|array|mixed|\Psr\Http\Message\StreamInterface
     * @throws ClientException
     */
//    function  sendGetSingleGoods(String $numIid,String $outProductId="",String $showDraft="false"){
//        $client = DyClient::newInstance($this->accessToken);
//        $params=['show_draft'=>$showDraft];
//        if(!empty($numIid)){
//            $params[ 'product_id'] = $numIid;
//        }
//        if(!empty($outProductId)){
//            $params[ 'out_product_id'] = $outProductId;
//        }
//        $res = $client->execute('product/detail', $params);
//        DyClient::handleErrorCode($res);
//        return $res['data'];
//    }


    /**
     * 发送获取商品列表请求
     * @param array $goodsId
     * @return mixed
     * @throws \Exception
     */
    protected function sendGetGoodsByGoodsId(array $goodsId)
    {
        return $this->sendGetItemsByIds($goodsId);
    }

    /**
     * 通过SKUId获取
     * @param array $skuId
     * @return array
     * @throws ApiException
     * @throws ClientException
     * @throws OrderException
     */


    function getGoodsBySkuIds(array $skuIds): array
    {
        $client = XhsClient::newInstance($this->accessToken);
        //并行异步请求
        $data = $result = [];
        foreach ($skuIds as $skuId) {
            $param = [
                'id' => $skuId
            ];
            $data[$skuId] = [
                'params' =>$client->buildRequestData($param,'product.getDetailSkuList'),
                'url' =>$client->getBaseUrlByApimethod(),
            ];
        }

        $responses = $this->poolCurl($data, 'POST', 5, $data);
        foreach ($responses as $response) {

//            Log::info('response', [$response]);


            /**
             * @var \GuzzleHttp\Psr7\Stream $responseData
             */
            $responseData = $response['data'];
            if (empty($responseData)) {
                continue;
            }

            $trade=$responseData['data'][0];

            $result[] = $trade;
            Log::info('trade', [$trade]);
//            $result[$orderId] = $this->formatToOrder($trade);
        }

        return $result;
    }
    /**
     * @param array $params
     * @param string $method
     * @param null $timeout
     * @param bool $safe
     * @return array
     */
    public function poolCurl($params = [], $method = 'GET', $timeout = null, $safe = false)
    {

        $headers = array();
        $keyArr = array_keys($params);
        switch ($method) {
            case 'get':
                $postKey = 'query';
                break;
            case 'post':
                $postKey = 'json';
                $headers = [
                    'Content-type' => 'application/json',
                    "Accept" => "application/json"
                ];
                break;
            case 'post_form':
                $method = 'post';
                $postKey = 'form_params';
                break;
            default:
                $postKey = 'json';
                break;
        }
        if (isset($timeout)) {
            $client = new Client(['timeout' => $timeout]);
        } else {
            $client = new Client();
        }
//        if (config('app.platform') == PlatformConst::JD) {
//            $client = $this->getJdClient()->getHttpClient();
//        } else if (config('app.platform') == PlatformConst::TAOBAO) {
//            $client = $this->getTbClient()->getHttpClient();
//        } else {
//            $client = $this->getClient()->getHttpClient();
//        }
        $requests = function ($data) use ($client, $method, $headers, $postKey) {
            foreach ($data as $index => $datum) {
                yield function () use ($client, $method, $postKey, $headers, $datum) {
                    Log::info('item', [$datum]);
                    return $client->requestAsync($method, $datum['url'], [
                        $postKey => $datum['params'],
                        'headers' => $headers
                    ]);
                };
            }
        };

        $result = [];
        $pool = new Pool($client, $requests($params), [
            'concurrency' => $this->concurrency, //并发数
            'fulfilled' => function (Response $response, $index) use (&$result, $params, $keyArr) {
                $client = $this->getClient();
                $idStr = $keyArr[$index];


                $content = $client->handleResponse( $response->getBody(), true);
                $result[$idStr] = [
                    'sort' => $index,
                    'idStr' => $idStr,
                    'content' => $content,
                ];
                return $result;
            },
            'rejected' => function (\Exception $e, $index) use ($safe, $keyArr) {
                $orderIdStr = $keyArr[$index];
                if ($safe) {
                    //遇到安全模式，只记录log啥也不干
                    Log::warning("遇到异常了，记录一下", ["orderIdStr" => $orderIdStr, "message" => $e->getMessage()]);
                } else {
                    throw new \Exception($e->getMessage());
                }

            },
        ]);
        // 初始化并创建promise
        $promise = $pool->promise();
        // 等待所有进程完成
        $promise->wait();

        array_multisort(array_column($result, 'sort'), SORT_ASC, $result);
        //$result = Arr::pluck($result, 'content', 'sort');
        $result = Arr::pluck($result, 'content' );
        //Log::info('result', [$result]);

        return $result;
    }

    protected function getClient(): XhsClient
    {
        $client = XhsClient::newInstance($this->getAccessToken());
        return $client;
    }

    /**
     * @throws OrderException
     * @throws ApiException
     * @throws ClientException
     */
    function sendSearchGoods(GoodsSearchRequest $request): GoodsSearchResponse
    {
        $goodsSearchResponse=new GoodsSearchResponse();
        $currentPage = $request->currentPage;
        $pageSize = $request->pageSize;
        $this->hasNext = false;
        $client = XhsClient::newInstance($this->accessToken);

        $param = [
            'pageNo' => $currentPage,
            'pageSize' => $pageSize,
        ];

        if($request->numIid){
            $param['itemId'] = $request->numIid;
            $result = $client->execute('product.getItemInfo', $param);
            $client->handleErrorCode($result);
            $item= $result['data']['itemInfo'];
            $item['skus']=$result['data']['skuInfos'];
            $goodsSearchResponse->data=[$item];
        }else {
            if($request->goodsTitle) {
                $searchParam = [];
                $searchParam['keyword'] = $request->goodsTitle;
                $param['searchParam'] =$searchParam;
            }
            $result = $client->execute('product.searchItemList', $param);
            Log::info('result', [$result]);
            $client->handleErrorCode($result);
            $itemIds = array_map(function ($itemDetail) {
                return $itemDetail['id'];
            }, $result['data']['itemDetailV3s']??[]);
            $goodsSearchResponse->data = $this->sendGetItemsByIds($itemIds);

        }


        if (isset($result['data']['total'])) {
            $goodsSearchResponse->total = $result['data']['total'];
        } else {
            $goodsSearchResponse->total = 0;
        }
        return $goodsSearchResponse;

    }

    public function formatItem($item):array{
        $skus = [];
        foreach ($item['skus'] as $sku) {
            $skus[] = [
                "type" => $this->platformType,
                "sku_id" => $sku['id'],
                "sku_value" => $sku['name'],
                "outer_id" => $sku['erpCode'],
                "outer_goods_id" => '',
                "sku_pic" =>$sku['specImage'],
                "is_onsale" => $sku['buyable'] ? Goods::IS_ONSALE_YES : Goods::IS_ONSALE_NO,
            ];
        }


        return [
            "type" => $this->platformType,
            'num_iid' => $item['id'],
            'outer_goods_id' => '',
            'goods_title' => $item['name'],
            'goods_pic' => $item['images'][0],
            'is_onsale' => Goods::IS_ONSALE_YES,
            'goods_created_at' => date('Y-m-d H:i:s', $item['createTime']/1000),
            'goods_updated_at' => date('Y-m-d H:i:s', $item['updateTime']/1000),
            'skus' => $skus
        ];
    }
    public function formatItems(array $data): array
    {
        $result=[];
       foreach ($data as $item){
           $result[]=$this->formatItem($item);
       }
       return $result;
    }

    public function sendGetItemsByIds(array $itemIds): array{
        Log::info('itemIds', [$itemIds]);
        $client = XhsClient::newInstance($this->accessToken);
        //并行异步请求
        $data = $result = [];
        foreach ($itemIds as $itemId) {
            if (empty($itemId)){
                continue;
            }
            $param = [
                'itemId' => $itemId
            ];
            $data[$itemId] = [
                'params' =>$client->buildRequestData($param,'product.getItemInfo'),
                'url' =>$client->getBaseUrlByApimethod(),
            ];
        }
        Log::info('pull data', [$data]);

        $responses = $this->poolCurl($data, 'POST', 5);
        foreach ($responses as $response) {

            Log::info('response', [$response]);


            /**
             * @var \GuzzleHttp\Psr7\Stream $responseData
             */
            $responseData = $response['data'];
            if (empty($responseData)) {
                continue;
            }

            $itemInfo=$responseData['itemInfo'];
            $skuInfos=$responseData['skuInfos'];
            $itemInfo['skus']=$skuInfos;
            $result[] = $itemInfo;
            Log::info('trade', [$itemInfo]);
//            $result[$orderId] = $this->formatToOrder($trade);
        }

        return $result;
    }
}
