<?php

namespace App\Services\Printing\Payload;

use App\Models\Company;

/**
 * 打印上下文的基类
 */
class AbstractPrintPayload
{
    protected $packageNum;
    private $templateId;
    private $newPrint;
    private $totalCount;
    private $orderIds;
    private $templateInfo;
    private $waybillAuth;
    private $currentShopId;
    private $userId;
    private $company;
    /**
     * 打印的方式 1 用新单号打印 2用旧单号打印
     * @var
     */
    private $printWaybillStatus;

    /**
     * @return mixed
     */
    public function getCompany()
    {
        return $this->company;
    }

    /**
     * @param mixed $company
     */
    public function setCompany(Company  $company): void
    {
        $this->company = $company;
    }

    /**
     * @return mixed
     */
    public function getUserId()
    {
        return $this->userId;
    }

    /**
     * @param mixed $userId
     */
    public function setUserId($userId): void
    {
        $this->userId = $userId;
    }

    /**
     * @return mixed
     */
    public function getCurrentShopId()
    {
        return $this->currentShopId;
    }

    /**
     * @param mixed $currentShopId
     */
    public function setCurrentShopId($currentShopId): void
    {
        $this->currentShopId = $currentShopId;
    }

    /**
     * @return mixed
     */
    public function getWaybillAuth()
    {
        return $this->waybillAuth;
    }

    /**
     * @param mixed $waybillAuth
     */
    public function setWaybillAuth($waybillAuth): void
    {
        $this->waybillAuth = $waybillAuth;
    }

    /**
     * @return mixed
     */
    public function getTemplateInfo()
    {
        return $this->templateInfo;
    }

    /**
     * @param mixed $templateInfo
     */
    public function setTemplateInfo($templateInfo): void
    {
        $this->templateInfo = $templateInfo;
    }
    /**
     * @return mixed
     */
    public function getTemplateId()
    {
        return $this->templateId;
    }

    /**
     * @param mixed $templateId
     */
    public function setTemplateId($templateId): void
    {
        $this->templateId = $templateId;
    }


    /**
     * @return mixed
     */
    public function getIsNewPrint()
    {
        return $this->newPrint;
    }

    /**
     * @param mixed $newPrint
     */
    public function setNewPrint($newPrint): void
    {
        $this->$newPrint = $newPrint;
    }

    /**
     * @return mixed
     */
    public function getTotalCount()
    {
        return $this->totalCount;
    }

    /**
     * @param mixed $totalCount
     */
    public function setTotalCount($totalCount): void
    {
        $this->totalCount = $totalCount;
    }

    /**
     * @return mixed
     */
    public function getOrderIds()
    {
        return $this->orderIds;
    }

    /**
     * @param mixed $orderIds
     */
    public function setOrderIds($orderIds): void
    {
        $this->orderIds = $orderIds;
    }

    /**
     * @return mixed
     */
    public function getPrintWaybillStatus()
    {
        return $this->printWaybillStatus;
    }

    /**
     * @param mixed $printWaybillStatus
     */
    public function setPrintWaybillStatus($printWaybillStatus): void
    {
        $this->printWaybillStatus = $printWaybillStatus;
    }

    /**
     * @return mixed
     */
    public function getPackageNum()
    {
        return $this->packageNum;
    }

    /**
     * @param mixed $packageNum
     */
    public function setPackageNum($packageNum): void
    {
        $this->packageNum = $packageNum;
    }

}
