<?php

namespace App\Services\Printing\Stage\CustomizeOrder;

use App\Constants\ErrorConst;
use App\Exceptions\ApiException;
use App\Models\CustomizeOrder;
use App\Models\PrintRecord;
use App\Models\Template;
use App\Models\WaybillHistory;
use App\Services\PrintDataService;
use App\Services\Printing\Payload\CustomizeOrderPrintPayload;
use Illuminate\Support\Facades\Log;

/**
 * 自由打印对已经取号的获取打印数据
 */
class CustomizeOrderPrintedPrintDataStage
{
    public function __invoke(CustomizeOrderPrintPayload $payload){


        $hasWaybillHistories =$this->hasWaybillHistories($payload);
        $printData = $hasWaybillHistories['print_data'];

//        $failedData = [];
        $newOrderIds = $hasWaybillHistories['new_order_ids'];
//        if (!empty($newOrderIds)) {
//
//            $orderIdsArr         = array_chunk($newOrderIds, 5);
//            $newWaybillHistories = self::newWaybillHistories(
//                $userId,
//                $shopId,
//                $orderIdsArr,
//                $addressInfo,
//                $template,
//                $auth->access_token,
//                $batchNo,
//                $packageNum
//            );
//            $printData           = array_merge($hasWaybillHistories['print_data'],
//                $newWaybillHistories['print_data']);
//
//            //取号失败信息
//            if (count($newWaybillHistories['failed_data']) > 0) {
//                $failedData = $newWaybillHistories['failed_data'];
//            }
//        }
//
//        if (!$printData && !$failedData) {
//            throw new ApiException(ErrorConst::PRINTED_DATA_ABNORMAL);
////            throw new \Exception('订单获取打印数据异常！');
//        }
//
//        return ['orders' => $printData, 'company' => $company, 'failed' => $failedData];

        $payload->setPrintData($printData);
        $payload->setNewOrderIds($newOrderIds);
        \Log::info("老单号打印成功");
        return  $payload;
    }
    /**
     * 已经取号订单打印数据
     * @param $orderIds
     * @param $sender
     * @param $template
     * @return array
     * @throws \Exception
     */
    public  function hasWaybillHistories(CustomizeOrderPrintPayload $payload)
    {
        $printRecords = [];
        $printData    = [];
        $senderAddress = $payload->getSenderAddress();
        $orderIds =$payload->getOrderIds();
        $order        = CustomizeOrder::query()->whereIn('id', $orderIds)->first();
        $oldTemplate  = Template::where('id', $order->template_id)->first();
        $template =$payload->getTemplateInfo();
        //重新打印的时候不能换面单来源抖音不能换到PDD
        if ($oldTemplate && $oldTemplate->auth_source != $template['auth_source']) {
            throw new ApiException(ErrorConst::CROSS_PLATFORM_PRINTING_NOT_SUPPORTED);
//            throw new \Exception('暂不支持跨平台模板打印！');
        }
        $waybillHistories = WaybillHistory::where('waybill_status', WaybillHistory::WAYBILL_RECOVERY_NO)
            ->where('package_id', 0)
            ->whereIn('order_id', $orderIds)
            ->get()
            ->toArray();
        if (empty($waybillHistories)) {
            return ['print_data' => $printData, 'new_order_ids' => $orderIds];
        }
        $batchNo = $batchNo ?? (int)(microtime(true) * 1000);
        $ordersKeyById               = CustomizeOrder::query()->findMany($orderIds)->keyBy("id");
        $hasWaybillIds = array_column($waybillHistories, 'order_id');
        foreach ($waybillHistories as $history) {
            $orderId = $history['order_id'];
            $order=$ordersKeyById->get($orderId);
            if(!isset($order)){
                continue;
            }
            $printData[]    = PrintDataService::templateData($senderAddress[$orderId], $order, $template, $history, [], true);
            $printRecords[] = [
                'wp_code'      => $history['wp_code'],
                'waybill_code' => $history['waybill_code'],
                'order'        => $order->toArray(),
                'user_id'      => $history['user_id'],
                'package_id'   => $history['package_id'],
                'history_id'   => $history['id'],
                'batch_no'     => $batchNo,
                'outer_order_no' => $history['outer_order_no'] ?? '',
            ];
        }

        //添加打印记录
        $userId =$payload->getUserId();
        $shopId =$payload->getCurrentShopId();
        $records = PrintRecord::generate($printRecords, $userId, $shopId);
        if (!$records) {
            Log::error('打印记录添加失败!', ['data' => $printRecords]);
            throw new ApiException(ErrorConst::PRINTED_RECORD_ADD_FAIL);
//            throw new \Exception('打印记录添加失败');
        }

        return ['print_data' => $printData, 'new_order_ids' => array_diff($orderIds, $hasWaybillIds)];
    }
}
