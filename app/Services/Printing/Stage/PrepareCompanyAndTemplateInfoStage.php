<?php

namespace App\Services\Printing\Stage;

use App\Models\Template;
use App\Services\Printing\Payload\AbstractPrintPayload;

/**
 * 获取打印模板信息以及关联的电子面单账号信息的Stage
 */
class PrepareCompanyAndTemplateInfoStage
{
    public function __invoke(AbstractPrintPayload $payload){
        \Log::info("执行PrepareCompanyAndTemplateInfoStage");
        $templateId=$payload->getTemplateId();
        $templateInfo = Template::findOrFail($templateId);
        $payload->setTemplateInfo($templateInfo);
        $company = $templateInfo->company;
        $payload->setCompany($company);
        \Log::info("PrepareCompanyAndTemplateInfoStage 执行成功",["templateInfo"=>$templateInfo,"company"=>$company]);
        return $payload;
    }
}
