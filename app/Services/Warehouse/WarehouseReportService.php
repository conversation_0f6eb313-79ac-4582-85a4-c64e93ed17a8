<?php

namespace App\Services\Warehouse;

use App\Constants\LogisticsConst;
use App\Exceptions\ErrorCodeException;
use App\Http\Controllers\Warehouse\Request\WarehouseReportRequest;
use App\Http\StatusCode\StatusCode;
use App\Models\Address;
use App\Models\ExportTask;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Package;
use App\Models\Shop;
use App\Models\ShopExtra;
use App\Models\Template;
use App\Models\WaybillHistory;
use App\Services\Order\OrderServiceManager;
use App\Utils\ExpressCompanyUtil;
use App\Utils\ReportUtil;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Rap2hpoutre\FastExcel\FastExcel;

class WarehouseReportService
{

    /**
     * 分页查询
     * @param WarehouseReportRequest $request
     * @return array
     */
    public function search(WarehouseReportRequest $request): array
    {
        /**
         * 订单店铺ID
         */
        $orderShopId = $request->orderShopId;
        /**
         * 运单店铺ID
         */
        $waybillShopIds = $request->waybillShopIds;
        $batchNo = $request->batchNo;
        $productName = $request->productName;
        $limit = $request->limit;
        $offset = $request->offset;
        $query = WaybillHistory::query()->leftJoin('package_orders', 'package_orders.package_id', '=', 'waybill_histories.package_id')
            ->with(['order', 'orderCipherInfo','packageOrders','shop','package','package.packageOrderItems'])
            ->whereIn("waybill_histories.version", [2, 3])
            ->where('waybill_histories.to_shop_id', $orderShopId)
            ->whereIn('waybill_histories.shop_id', $waybillShopIds)
            ->where('waybill_status', WaybillHistory::WAYBILL_RECOVERY_NO);
        if($request->begin){
            $query->where('waybill_histories.created_at', '>=', $request->begin);
        }
        if($request->end){
            $query->where('waybill_histories.created_at', '<=', $request->end);
        }
        if($batchNo){
            $query->where('waybill_histories.batch_no', $batchNo);
        }

        $query->selectRaw("waybill_histories.id,
			waybill_histories.auth_source,
			waybill_histories.package_id,
			waybill_histories.order_id,
			waybill_histories.order_type,
			waybill_histories.shop_id,
			waybill_histories.waybill_code,
			waybill_histories.wp_code,
			waybill_histories.waybill_status,
			waybill_histories.receiver_name,
			waybill_histories.receiver_phone,
			waybill_histories.receiver_province,
			waybill_histories.receiver_city,
			waybill_histories.receiver_district,
			waybill_histories.receiver_address,
			waybill_histories.print_data_items,
			waybill_histories.created_at,
			waybill_histories.batch_no,
			waybill_histories.waybill_index,
			waybill_histories.waybill_count,
			waybill_histories.print_data,
			waybill_histories.template_id,
			waybill_histories.outer_order_no,
			waybill_histories.sub_waybill_codes,
			max(waybill_histories.soft_remark) as soft_remark,
			GROUP_CONCAT(DISTINCT(package_orders.tid)) as order_no_str,
			GROUP_CONCAT(DISTINCT(package_orders.order_id)) as order_id_str,
			GROUP_CONCAT(DISTINCT(waybill_histories.id)) as id_str"
        //			waybill_histories.print_data
        );
//        if (!empty($toShopIdList)) {
//            $query->whereIn('waybill_histories.to_shop_id', $toShopIdList);
//        }


        if ($batchNo) {
            $arr = explode('-', $batchNo);
            $query->where('waybill_histories.batch_no', $arr[0]);
        }
        if ($productName) {
            $query->where('waybill_histories.soft_remark', $productName);
        }

        $query->groupBy(['waybill_histories.waybill_code']);

        //有group by不能直接count
        if (isset($limit)) {
            $query->limit($limit);
        }
        if (isset($offset)) {
            $query->offset($offset);
        }
        $baseSql = getSqlByQuery($query);
        \Log::info("面单记录查询", [$baseSql]);
        $ret = $query
            ->orderBy('waybill_histories.id', 'asc')
//            ->orderBy('waybill_histories.id', $sortArr[1])
            ->get();
        return [$ret, 0];
    }

    /**
     * 导出数据
     * @param WarehouseReportRequest $request
     * @return WarehouseSummaryReport
     * @throws ErrorCodeException
     */
    public function export(WarehouseReportRequest $request): WarehouseSummaryReport
    {
        $warehouseSummaryReport = $this->buildWarehouseSummaryReport($request);

        $next=true;
        $exportRet=[];
        $index=1;
        while($next){
            list($ret,$count)=$this->search($request);
            \Log::info('包裹导出中：', ["offset"=>$request->offset]);
            if(sizeof($ret)==0){
                $next=false;
                \Log::info('包裹导出结束');
                $warehouseSummaryReport->build();
            }
            $request->offset+=$request->limit;
            $ret = $this->handleLines($ret, $index,$warehouseSummaryReport);
            $exportRet=array_merge($exportRet,$ret);
        }

        // 打开文件流
        $path = base_path() . '/storage/app/export/';
        $fileName = time() . '.xlsx';
        $downloadFileName = $warehouseSummaryReport->getExportFileName().'.xlsx';
        $fullFileName=$path.$fileName;
        // 打开文件流
        $path = base_path() . '/storage/app/export/';
        (new FastExcel($exportRet))->export($fullFileName,[$this,'getHeadMap']);
        ExportTask::where('id', $request->exportTaskId)->update(['status' => ExportTask::STATUS_SUCCESS, 'file_path' => $path . $fileName,'name'=> $downloadFileName]);

        return $warehouseSummaryReport;




    }

    /**
     * 云仓包裹汇总统计
     * @param WarehouseReportRequest $request
     * @return void
     * @throws ErrorCodeException
     */
    public function summaryReport(WarehouseReportRequest $request):WarehouseSummaryReport
    {
        \Log::info('包裹汇总统计中：', [json_encode($request)]);
        $waybillHistorySummaryReport = $this->buildWarehouseSummaryReport($request);

        $next = true;
        $offset = 0;
        $index = 1;
        $request->limit = 1000;
        $request->offset = $offset;
        while ($next) {
            list($ret, $count) = $this->search($request);
            \Log::info('包裹汇总统计中：', ["offset" => $request->offset]);
            $this->handleLines($ret, $index,$waybillHistorySummaryReport);
            \Log::info('处理行数据中：', ["offset" => $request->offset]);
            if (sizeof($ret) == 0) {
                $next = false;
                \Log::info('包裹汇总统计结束');
            }else{
                $offset += $request->limit;
                $request->offset = $offset;
            }
        }
        $waybillHistorySummaryReport->build();
        return $waybillHistorySummaryReport;
    }


//    /**
//     * 汇总数据处理
//     * @param WarehouseSummaryReport $waybillHistorySummaryReport
//     * @param $ret
//     * @param bool $fullData
//     * @return mixed
//     */
//    public static function summaryMerge(WarehouseSummaryReport &$waybillHistorySummaryReport, $ret, bool $fullData = false)
//    {
//        if (empty($ret)) {
//            return $ret;
//        }
//        $templateInfoMap = [];
//
//        foreach ($ret as $item) {
//            $orderItems = $sellerMemo = $buyerMessage = [];
//            $orderIdArr = explode(',', $item['order_id_str']);
//            $item['idArr'] = explode(',', $item['id_str']);
//            $item['orderNoArr'] = explode(',', str_replace('A', '', $item['order_no_str']));
//            $item['orderNoStr'] = str_replace('A', '', $item['order_no_str']);
//            //关联平台订单
//            if ($item->package_id > 0) {
//                //关联order
////                $item->order;
////                if (isset($item->order)) {
////                    $order = $item->order;
////                    $item->receiver_province = $order->receiver_state;
////                    $item->receiver_city = $order->receiver_city;
////                    $item->receiver_district = $item->order->receiver_district;
////                    $item['seller_nick']=$item->order->seller_nick;
////
////                } else {
////                    \Log::info("有问题的数据", [$item]);
////                }
////                if($fullData&&isset($item->order)) {
////                    $item->order->shop;
////                }
//                //关联package
//                $packageOrders = $item->packageOrders;
//                $packageOrders;
//                if (!isFactory()) {
//                    //关联order
//                    $item->order;
//                    $item->receiver_province = $item->order->receiver_state;
//                    $item->receiver_city = $item->order->receiver_city;
//                    $item->receiver_district = $item->order->receiver_district;
////                    if ($fullData && isset($item->order)) {
////                        $item->order->shop;
////                    }
//
//                    //关联orderItem
//                    $item->orderItem;
//                    //从packageOrders中取出orderItemsId
//                    $orderItemsId = array_column($packageOrders->toArray(), 'order_item_id');
//                    $packageOrderItems = array_column($packageOrders->toArray(), null, 'order_item_id');
//                    //替换掩码地址
//                    if ($item->orderCipherInfo) {
//                        $item->receiver_name = $item->orderCipherInfo->receiver_name_mask;
//                        $item->receiver_phone = $item->orderCipherInfo->receiver_phone_mask;
//                        $item->receiver_address = $item->orderCipherInfo->receiver_address_mask;
//                    }
//                    $orderItemArr = OrderItem::query()->whereIn('id', $orderItemsId)->get();
//                    foreach ($orderItemArr as $orderItem) {
//                        $orderItem['goods_num'] = $packageOrderItems[$orderItem->id]['num'];
//                        $orderItems[] = $orderItem;
//                    }
//                    //卖家备注
//                    if ($item->order && $item->order['seller_memo'] != '[]') {
//                        $tmpSellerMemo = json_decode($item->order['seller_memo'], true);
//                        $sellerMemo[] = $tmpSellerMemo[0];
//                    }
//                    //买家留言
//                    if ($item->order && $item->order['buyer_message']) {
//                        $buyerMessage[] = $item->order['buyer_message'];
//                    }
//                }
//                $item['merge_order_item'] = $orderItems;
//            } else {
//                \Log::info('没有关联订单', [$item]);
//            }
////            \Log::info('订单行数据' ,$item['merge_order_item']);
//            $item['seller_memo_arr'] = $sellerMemo;
//            $item['buyer_message_arr'] = $buyerMessage;
//        }
//
//        return $ret;
//    }


    /**
     * 合并数据，并生成汇总报表
     * @param Collection $ret
     * @param int $index
     * @param WarehouseSummaryReport $warehouseSummaryReport
     * @return void
     */
    private function handleLines(Collection $ret, int &$index, WarehouseSummaryReport &$warehouseSummaryReport): array
    {

        if ($ret->isEmpty()) {
            return[];
        }

        $buffer = [];

        foreach ($ret as $item) {
            $shop = $item->shop;
            $date=  $item->created_at->format('Y-m-d'); //取号日期
            $orderItems = $sellerMemo = $buyerMessage = [];
            $strCreatedAt=isset($item['created_at'])?$item['created_at']->format('Ymd H:i:s'):'';
            $orderIdArr = explode(',', $item['order_id_str']);
            $item['idArr'] = explode(',', $item['id_str']);
            $item['orderNoArr'] = explode(',', $item['order_no_str']);
            $item['orderNoStr'] = $item['order_no_str'];
            $productName = $item['soft_remark']??"没有备注";
            //看$warehouseSummaryReport的产品名是不是已经有了，没有就添加
            if(!empty($productName)&&!in_array($productName,$warehouseSummaryReport->goodsNames)){
                $warehouseSummaryReport->addProductName($productName);
            }

            $skuArr = $skuOutIdArr = [];

            //关联平台订单
            if ($item->package_id <= 0) {
                continue;
            }
            if(sizeof($orderIdArr)>1){
                $warehouseSummaryReport->mergeOrderNum++;
            }
            //关联order
            $order=$item->order;
            if(!isset($order)){
                \Log::info('订单信息不存在', [$item]);
               continue;
            }
            //区县吗
            $districtCode=$order->district_code;
            //取$districtCode前面的两位
            if (isset($order)) {
                $item->receiver_province = $order->receiver_state;
                $item->receiver_city = $order->receiver_city;
                $item->receiver_district = $item->order->receiver_district;
                $item['seller_nick'] = $item->order->seller_nick;

            } else {
                \Log::info("有问题的数据", [$item]);
            }
//                if($fullData&&isset($item->order)) {
//                    $item->order->shop;
//                }
            //关联package
            $packageOrders = $item->packageOrders;
//            \Log::info('获取包裹商品', [$packageOrders]);

            $goodsNum= 0;
            $orderItems = $item->package->packageOrderItems;
//            \Log::info('获取包裹订单行', [$orderItems]);


            //把$item->orderItem里面的赠品sku_id取出来，goods_type=2是赠品
            array_map(function ($orderItem) use (&$giftSkuIds) {
                if ($orderItem['goods_type'] == 2) {
                    $giftSkuIds[] = $orderItem['sku_id'];
                }
            }, $orderItems->toArray());

            $giftSkuIds= [];
            //把包裹里面的商品数量加起来，
            foreach ($packageOrders as $packageOrder) {
                //累计商品数量的时候，如果sku_id在赠品sku_id里面，就跳过
                if (in_array($packageOrder->sku_id, $giftSkuIds)) {
                    continue;
                }
                $goodsNum += $packageOrder->num;
            }
            $warehouseSummaryShopGoodItem = $warehouseSummaryReport->getWarehouseSummaryShopGoodItemInstance($productName, $goodsNum, $date, $item->shop_id, $order->seller_nick);
            //累计包裹数量
            $warehouseSummaryShopGoodItem->addPackageNum(1);
            //累计快递使用量
            $warehouseSummaryShopGoodItem->addWpUsage($item->wp_code,1);
            $warehouseSummaryShopGoodItem->addWaybillUsage($districtCode,1);

            //关联order
            $item->receiver_province = $item->order->receiver_state;
            $item->receiver_city = $item->order->receiver_city;
            $item->receiver_district = $item->order->receiver_district;
//                    if ($fullData && isset($item->order)) {
//                        $item->order->shop;
//                    }

            //关联orderItem

            //从packageOrders中取出orderItemsId
            $orderItemsId = array_column($packageOrders->toArray(), 'order_item_id');
            $packageOrderItems = array_column($packageOrders->toArray(), null, 'order_item_id');
            //替换掩码地址
            if ($item->orderCipherInfo) {
                $item->receiver_name = $item->orderCipherInfo->receiver_name_mask;
                $item->receiver_phone = $item->orderCipherInfo->receiver_phone_mask;
                $item->receiver_address = $item->orderCipherInfo->receiver_address_mask;
            }
            $company = collect(config('express_company'))->where('wpCode', $item['wp_code'])->values()->first();
            $companyName = !empty($company) ? $company['name'] : ($item['wp_code'] ?? '');
            $waybillCode = $item['waybill_code'] ?? '';
//            \Log::info('获取订单行数据', ["waybillCode"=>$waybillCode, "orderItemsId"=>$orderItemsId]);
            $orderItemArr = OrderItem::query()->whereIn('id', $orderItemsId)->get();
//            \Log::info('获取订单行数据结束', [$orderItemsId]);
            //把订单行数据放到一个数组里面，但数量用packageOrders里面的数量
            $orderItems = [];
            foreach ($orderItemArr as $orderItem) {
                $orderItem['goods_num'] = $packageOrderItems[$orderItem->id]['num'];
                $orderItems[] = $orderItem;
            }
            //卖家备注
            if ($order && $order['seller_memo'] != '[]') {
                $tmpSellerMemo = json_decode($order['seller_memo'], true);
                $sellerMemo[] = $tmpSellerMemo[0];
            }
            //买家留言
            if ($order && $order['buyer_message']) {
                $buyerMessage[] = $order['buyer_message'];
            }

            $item['merge_order_item'] = $orderItems;

//            \Log::info('订单行数据' ,[ "waybillCode"=>$waybillCode, "orderItems"=>$item['merge_order_item']]);
            $item['seller_memo_arr'] = $sellerMemo;
            $item['buyer_message_arr'] = $buyerMessage;

            $list = [
                'index' => $index ++,
                'batch_no' => isset($item['package']) ? $item['package']['batch_no'] : '',
                'order_no' => $item['orderNoStr'] ?? '',
                'outer_order_no' => $item['outer_order_no'],
                'shop_name' => $shop->shop_name ?? '',
                'wp_code' => $companyName,
                'waybill_code' => $waybillCode,
                'receiver_name' => $item['receiver_name'] ?? '',
                'receiver_phone' => $item['receiver_phone'] ?? '',
                'address' => $item['receiver_province'] . $item['receiver_city'] . $item['receiver_district'] . $item['receiver_address'],
                'soft_remark' => $item['soft_remark'] ?? '',
//                'goods_title' => implode('|', array_pluck($item['orderItem'], 'goods_title')),
                'goods_num' => ReportUtil::buildGoodsNum($item['merge_order_item']),
                'sku_info' => ReportUtil::buildSkuTitle($item['merge_order_item']),
                'sku_out_id' => implode('|', $skuOutIdArr),
                'buyer_message' => isset($item['merge_order_item']) ? implode("|", array_pluck($item['merge_order_item'], 'buyer_message')) : '',
                'seller_memo' => isset($item['merge_order_item']) ? implode("|", array_pluck($item['merge_order_item'], 'seller_memo')) : '',
                'created_at' => $strCreatedAt,
                'waybill_status' => $item['waybill_status'] == 0 ? '正常' : '已回收',
                'seller_nick' => $item['seller_nick'] ?? '',
                'template_name' => $item['template_name'] ?? '',

                'sender_name'=>Arr::get(!empty($printDataContent)?$printDataContent[0]:[],'addData.senderInfo.contact.name',''),
            ];

            $list = ReportUtil::handleNumberNotFormat($list);
            $buffer[] = $list;
        }

        return $buffer;

    }

    /**
     * 构建仓库汇总报表的初始化对象
     * @param WarehouseReportRequest $request
     * @return WarehouseSummaryReport
     * @throws ErrorCodeException
     */
    public function buildWarehouseSummaryReport(WarehouseReportRequest $request): WarehouseSummaryReport
    {
        $warehouseSummaryReport = new WarehouseSummaryReport();
        $warehouseSummaryReport->productName= $request->productName;
        $orderShop = Shop::find($request->orderShopId);
//        \Log::info('订单店铺信息', [$orderShop]);
        $warehouseSummaryReport->shopName=$orderShop->shop_name;
        //全部转成大写
        $warehouseSummaryReport->defaultWpCodes= [ExpressCompanyUtil::findExpressCompanyName(LogisticsConst::YUNDA)];

        $shopExtra = ShopExtra::firstByShopId($request->currentShopId);
        $warehouseName = $shopExtra->warehouse_name;
        $waybillExtraProvince = $shopExtra->waybill_extra_province;
        if (empty($waybillExtraProvince)) {
            throw_error_code_exception([StatusCode::PARAM_MISS, '缺少快递加收省份']);
        }
        $waybillExtraProvinceNames = explode(',', $waybillExtraProvince);
        $warehouseSummaryReport->waybillExtraProvinceColumns =$waybillExtraProvinceNames;
        $warehouseSummaryReport->xinJiangXiZangProvinceColumns = explode(',', $shopExtra->xinjiang_xizang);

        $provinceAddress = Address::loadAddressByLevel([1,2]);
        $waybillExtraAddress = [];
        //把匹配的省份代码取出来，如果有某个省份不在地址表里面，就报错
        foreach ($waybillExtraProvinceNames as $province) {
            $provinceAddress = $provinceAddress->where('name', $province)->first();
            if (empty($provinceAddress)) {
                throw_error_code_exception([StatusCode::PARAM_MISS, $province . '快递加收省份不在地址表里面']);
            }
            $waybillExtraAddress[] = $provinceAddress;
        }
        //把Code都提取出来
        $warehouseSummaryReport->waybillExtraProvinceCityCodes=array_column($waybillExtraAddress,'code');
        $warehouseSummaryReport->waybillExtraProvinceCityAddress = $waybillExtraAddress;
        $xinJiangXiZangProvinceNames = explode(',', $shopExtra->xinjiang_xizang);
        $xinJiangXiZangAddress = [];
        foreach ($xinJiangXiZangProvinceNames as $province) {
            $provinceAddress = $provinceAddress->where('name', $province)->first();
            if (empty($provinceAddress)) {
                throw_error_code_exception([StatusCode::PARAM_MISS, $province . '不在地址表里面']);
            }
            $xinJiangXiZangAddress[] = $provinceAddress;
        }
        $warehouseSummaryReport->xinJiangXiZangProvinceAddress = $xinJiangXiZangAddress;

        if (empty($warehouseName)) {
            throw_error_code_exception([StatusCode::PARAM_MISS, '缺少仓库名称']);
        }

        $warehouseSummaryReport->warehouseName = $warehouseName;
        return $warehouseSummaryReport;
    }

    public function getHeadMap($line)
    {
        return ['序号'=>$line[ 'index'],
            '批次号'=>$line[ 'batch_no'],
            '订单编号'=>$line[ 'order_no'],
            '外部订单号'=>$line[ 'outer_order_no'],
            '店铺名称'=>$line[ 'shop_name'],
            '物流公司'=>$line[ 'wp_code'],
            '运单号'=>$line[ 'waybill_code'],
            '收件人'=>$line[ 'receiver_name'],
            '联系电话'=>$line[ 'receiver_phone'],
            '地址'=>$line[ 'address'],
//            '商品名称'=>$line[ 'goods_title'],
            '商品名称'=>$line[ 'soft_remark'],
            '商品数量'=>$line[ 'goods_num'],
//            '商品规格x数量'=>$line[ 'sku_info'],
//            '商品编码|sku编码'=>$line[ 'sku_out_id'],
            '买家留言'=>$line[ 'buyer_message'],
            '卖家备注'=>$line[ 'seller_memo'],
            '取号时间'=>$line[ 'created_at'],
            '运单状态'=>$line[ 'waybill_status'],
            '订单店铺'=>$line[ 'seller_nick']??'',
            '模板名称'=>$line[ 'template_name']??'',
            '发件人姓名'=>$line[ 'sender_name']??'',
        ];
    }

}
