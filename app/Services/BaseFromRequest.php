<?php

namespace App\Services;

use App\Utils\ObjectUtil;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;
use <PERSON>vel\Lumen\Routing\ProvidesConvenienceMethods;

class BaseFromRequest
{
    use ProvidesConvenienceMethods;

    public function rules():array
    {
        return [];
    }

    public function validate(Request $request): array
    {
        $rules = $this->rules();
        $validator = $this->getValidationFactory()->make($request->all(), $rules);

        if ($validator->fails()) {
            throw new ValidationException($validator, $this->buildFailedValidationResponse(
                $request, $this->formatValidationErrors($validator)
            ));
        }
        // 暂时先把所有参数返回，理论上只返回验证通过的参数
        $params = $request->all();

        ObjectUtil::mapToObject($params, $this,true);

        return $params;
    }

}
