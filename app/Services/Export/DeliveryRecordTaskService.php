<?php
/**
 * Created by PhpStorm.
 * User: x<PERSON><PERSON><PERSON><PERSON>
 * Date: 2022/9/7
 * Time: 15:12
 */

namespace App\Services\Export;


use App\Constants\PlatformConst;
use App\Models\DeliveryRecord;
use App\Models\Fix\Order;
use App\Models\Fix\Shop;
use App\Models\OrderItem;
use App\Models\Package;
use App\Models\WaybillHistory;
use App\Services\DeliveryRecord\DeliveryRecordQueryService;
use App\Services\DeliveryRecord\Request\DeliveryRecordExportRequest;
use App\Services\DeliveryRecord\Request\DeliveryRecordSearchRequest;
use App\Services\Order\OrderQueryBuilder;
use App\Services\Order\OrderServiceManager;
use App\Services\Order\Request\OrderSearchRequest;
use App\Utils\OrderUtil;
use App\Utils\ReportUtil;
use App\Utils\ShopUtil;
use Illuminate\Support\Facades\Log;
use JsonMapper;

/**
 * 发货记录导出
 */
class DeliveryRecordTaskService extends AbstractExcelExportTaskService
{
    private $buffer = [];
    private $continue = true;
    private $offset = 0;
    private $index = 1;
    /**
     * @var OrderQueryBuilder
     */
    private $orderQueryBuilder;

    /**
     * @var DeliveryRecordQueryService
     */
    private $deliveryRecordQueryService;
    /**
     * @var DeliveryRecordExportRequest
     */
    private $deliveryRecordExportRequest;

    /**
     * @var DeliveryRecordSearchRequest
     */
    private $deliverRecordSearchRequest;
    /**
     * @var OrderSearchRequest
     */
    private $orderSearchRequest;

    /**
     * 任务类型
     * 1 按快递单号导出
     * 2 按商品导出
     * @var
     */
    private $type;

    /**
     * @throws \JsonMapper_Exception
     */
    public function __construct($userId, $shopId, $data, $name, $exportType)
    {
        Log::info("导出发货记录,userId=" . $userId . ",shopId=" . $shopId . ",data=" . json_encode($data) . ",exportType=" . $exportType, [$data['condition'] ?? '']);
        parent::__construct($userId, $shopId, $data, $name, $exportType);

        $this->orderQueryBuilder = new OrderQueryBuilder();
        $this->deliveryRecordQueryService = new DeliveryRecordQueryService();
        $mapper = new JsonMapper();
        $strConditionJson = $data['condition'];
        $mapper->bStrictNullTypes = false;
        $this->deliveryRecordExportRequest = $mapper->map(json_decode($strConditionJson), new
        DeliveryRecordExportRequest());
        $this->type = $this->deliveryRecordExportRequest->type ?? '';
        $this->deliverRecordSearchRequest = $mapper->map(json_decode($strConditionJson), new
        DeliveryRecordSearchRequest());
        $this->deliverRecordSearchRequest->limit = 5000;
        $this->orderSearchRequest = $mapper->map(json_decode($strConditionJson), new
        OrderSearchRequest());
        $this->orderSearchRequest->timeField = 'send_at';
        Log::info("导出发货记录,type=" . strval($this->type), [$this->type == 1 ? $this->deliverRecordSearchRequest : $this->orderSearchRequest]);
    }

    public function getHeadMap($line)
    {

        //按包裹导出
        if ($this->type == 1) {
            return ['序号' => $line['index'],
                '订单编号' => $line['order_no'],
                '店铺名称' => $line['shop_name'],
                '物流公司' => $line['wp_code'],
                '运单号' => $line['waybill_code'],
                '收件人' => $line['receiver_name'],
                '联系电话' => $line['receiver_phone'],
                '地址' => $line['address'],
                '商品数量' => $line['goods_num'],
                '商品规格x数量' => $line['sku_info'],
                '商品编码|sku编码' => $line['sku_out_id'],
                '买家留言' => $line['buyer_message'],
                '卖家备注' => $line['seller_memo'],
                '发货时间' => $line['created_at'],
            ];
        } else {
            //type=2按商品导出
            return ['序号' => $line['index'],
                '商品名称' => $line['goods_title'],
                '规格名称|sku编码' => $line['sku_value'],
                '单价' => $line['goods_price'],
                '数量' => $line['sku_num'],
            ];
        }

    }

    public function getExportData()
    {
        Log::info('导出已发货开始', ["shopId" => $this->shopId, "data" => $this->data]);
        while (true) {
            $nextItem = $this->nextItem();
            if (isset($nextItem)) {
                yield $nextItem;
            } else {
                if (!$this->continue) {
                    break;
                }
            }

        }
        Log::info('导出已发货结束' . $this->shopId);
    }

    public function nextItem()
    {
        loop:
        if (sizeof($this->buffer) > 0) {
            return array_shift($this->buffer);
        }
        if (!$this->continue) {
            return;
        }
        if ($this->type == 1) {
            $this->deliverRecordSearchRequest->offset = $this->offset;
            ["data" => $ret, "count" => $count] = $this->deliveryRecordQueryService->search
            ($this->deliverRecordSearchRequest, null);
            Log::info("按快递单号获取发货记录", ["data.size" => count($ret), "count" => $count, "offset" => $this->offset]);
            if (sizeof($ret) == 0) {
                $this->continue = false;
                return;
            }
            $this->offset += count($ret);
            //运单号相同合并
            $ret = collect($ret)->groupBy(['waybill_code']);
            foreach ($ret as $waybillCode => $item) {
                $orderNoArr = collect($item)->pluck('order_no')->toArray();
                $firstDeliveryRecord = $item[0];
                $result = json_decode($firstDeliveryRecord['result'], true);
                $company = collect(config('express_company'))->where('wpCode', $firstDeliveryRecord['wp_code'])
                    ->values()
                    ->first();
                $companyName = !empty($company) ? $company['name'] : ($waybillCode);
                $orderId = $firstDeliveryRecord['order_id'];
//                $order = Order::find($orderId);
                $order = $item[0]['order'];
                // 拼接商品
                $goodsInfo = "";
                $orderItems = OrderItem::query()->whereIn('tid', $orderNoArr)->get(['sku_id', 'goods_title', 'sku_value', 'goods_num']);
                foreach ($orderItems as $value) {
                    $goodsInfo .= $value['goods_title'] . ' ' . $value['sku_value'] . ' x' . $value['goods_num'] . "\n";
                }
//                \Log::info("订单",[$orderId,$order]);
                if (isset($order)) {
                    $orderCipherInfo = $order['order_cipher_info'];
                }
                $address = $order['receiver_state'] . $order['receiver_city'] . $order['receiver_district'] .
                $orderCipherInfo['receiver_address_mask'] ?? '';
                $list = [
                    'index' => $this->index++,
                    'order_no' => implode(',', $orderNoArr) ?? '',
                    'shop_name' => ShopUtil::firstByIdWithCache($firstDeliveryRecord['shop_id'])->shop_name,
                    'waybill_code' => $waybillCode,
                    'wp_code' => $companyName,
                    'goods_info' => $goodsInfo,
                    'receiver_name' => $orderCipherInfo['receiver_name_mask'] ?? "",
                    'receiver_phone' => $orderCipherInfo['receiver_phone_mask'] ?? "",
                    'address' => $address,
                    'sku_info' => ReportUtil::buildSkuTitle($orderItems),
                    'goods_num' => ReportUtil::buildGoodsNum($orderItems),
                    'sku_out_id' => ReportUtil::buildSkuOutId($orderItems),
                    'buyer_message' => ReportUtil::buildBuyerMessages($orderItems),
                    'seller_memo' => ReportUtil::buildSellerMemos($orderItems),
                    'created_at' => $firstDeliveryRecord['created_at'] ?? '',
                    'result' => $result == 1 ? '发货成功' : '发货失败',
                ];
                $list = ReportUtil::handleNumberNotFormat($list);
                $this->buffer[] = $list;
            }


            goto loop;
        } else {
            $orderQuery = $this->orderQueryBuilder->buildPlainOrderQuery($this->orderSearchRequest, [], true)
                ->groupBy(['sku_id'])->orderBy('order_created_at_min');
            $ret = $orderQuery
                ->with(['orderItem.customGoodsSkus', 'orderItem.customGoods', 'shop', 'trace'])
                ->get([
                    'orders.id',
                    'order_items.sku_id',
                    'order_items.sku_value',
                    'order_items.num_iid',
                    'order_items.goods_title',
                    'order_items.goods_pic',
                    'order_items.goods_price',
                    'order_items.outer_iid',
                    'order_items.outer_sku_iid',
                    \DB::raw('min(orders.order_created_at) order_created_at_min'),
                    \DB::raw('sum(goods_num) goods_nums')
                ])->groupBy('sku_id');

            $index = 1;
            foreach ($ret as $item) {
                //规格名称
                $skuValue = $skuNum = $goodsPrice = '';
                foreach ($item as $value) {
                    $skuNum .= $value['goods_nums'] . "\n";
                    $skuValue .= $value['sku_value'] . '/' . $value['sku_id'] . "\n";
                    $goodsPrice .= $value['goods_price'] . "\n";
                    $list = [
                        'index' => $index++,
                        'goods_title' => $item[0]['goods_title'],
                        'sku_value' => $skuValue,
                        'sku_num' => $skuNum,
                        'goods_price' => $goodsPrice,
                    ];
                    $list = ReportUtil::handleNumberNotFormat($list);
                    $this->buffer[] = $list;
                    $this->index++;
                }

            }
            Log::info("按商品获取发货记录", ["data.size" => count($ret), "count" => count($this->buffer), "sql" => $orderQuery->toSql
            ()]);
            $this->continue = false;
            goto loop;
        }
    }


}
