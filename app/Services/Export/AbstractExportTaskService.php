<?php
/**
 * Created by PhpStorm.
 * User: x<PERSON><PERSON><PERSON><PERSON>
 * Date: 2022/3/15
 * Time: 15:05
 */

namespace App\Services\Export;

use App\Models\ExportTask;
use Illuminate\Support\Facades\Log;

abstract class AbstractExportTaskService
{
    protected $userId;
    protected $shopId;
    protected $data;
    protected $limit = 5000;
    protected $name;

    protected function __construct($userId, $shopId, $data, $name = null)
    {
        $this->userId = $userId;
        $this->shopId = $shopId;
        $this->data = $data;
        $this->name = $name;
    }

    abstract public function getHead();

    abstract public function getExportData($fp);

    public function export()
    {
        try {
            Log::info('导出任务：' . $this->data['shop_id']);
            // 防止内存溢出
            ini_set('memory_limit', '1024M');
            // 更改任务状态
            ExportTask::where('id', $this->data['id'])->update(['status' => ExportTask::STATUS_DOING]);
            // 打开文件流
            $fileName = time() . '.csv';
            $path = base_path() . '/storage/app/export/';
            $fp = fopen($path . $fileName, 'a');//打开output流
            fwrite($fp, chr(0xEF) . chr(0xBB) . chr(0xBF)); // 添加 BOM
            // 写入标题
            fputcsv($fp, $this->getHead());
            // 获取导出数据
            $this->getExportData($fp);
            // 关闭文件
            fclose($fp);
            //修改任务状态
            ExportTask::where('id', $this->data['id'])->update(['status' => ExportTask::STATUS_SUCCESS, 'file_path' => $path . $fileName]);
            // 清理数据
            self::clean();
        } catch (\Throwable $ex) {
            Log::info("导出文件失败", $ex->getTrace());
            ExportTask::where('id', $this->data['id'])->update(['status' => ExportTask::STATUS_ERROR, 'memo' =>
                $ex->getMessage()]);
        }
    }

    public static function handleNumberNotFormat(array $list): array
    {
        // 避免数字转成科学记数法
        $list = array_map(function ($item) {
            // 有的订单号 是通过逗号链接，excel 会有问题。如： 111111,222222
            $arr = explode(',', $item);
            if ((is_numeric($item) && strlen($item) >= 10) ||
                (!empty($arr[0]) && is_numeric($arr[0]) && strlen($arr[0]) >= 10)) {
                return $item . "\t";
            }
            return $item;
        }, $list);
        return $list;
    }

    public static function clean()
    {
        //只保留7天数据
        $time = date("Y-m-d H:i:s", strtotime("-7 day"));
        $historyList = ExportTask::where([['created_at', '<=', $time]])->get();
        if (!empty($historyList)) {
            //删除文件
            foreach ($historyList as $item) {
                @unlink($item['file_path']);
            }
            //删除数据
            ExportTask::where([['created_at', '<=', $time]])->delete();
        }
    }
}
