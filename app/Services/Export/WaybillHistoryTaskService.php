<?php
///**
// * Created by PhpStorm.
// * User: xu<PERSON><PERSON><PERSON>
// * Date: 2022/3/15
// * Time: 15:11
// */
//
//namespace App\Services\Export;
//
//use App\Models\Shop;
//use App\Models\WaybillHistory;
//use App\Services\WaybillHistory\Request\WaybillHistorySearchRequest;
//use App\Services\WaybillHistory\WaybillHistoryQueryService;
//use App\Utils\ReportUtil;
//
//class WaybillHistoryTaskService extends AbstractExcelExportTaskService
//{
//    private $waybillHistoryQueryService;
//
//    public function __construct($userId, $shopId, $data,$name,$exportType)
//    {
//        parent::__construct($userId, $shopId, $data,$name,$exportType);
//        $this->waybillHistoryQueryService = new WaybillHistoryQueryService();
//    }
//
//    private $continue = true;
//    private $offset = 0;
//    private $realCount = 0;
//    private  $buffer = [];
//    public function getHeadMap($line)
//    {
//        return ['序号'=>$line[ 'index'],
//            '批次号'=>$line[ 'batch_no'],
//            '订单编号'=>$line[ 'order_no'],
//            '外部订单号'=>$line[ 'outer_order_no'],
//            '店铺名称'=>$line[ 'shop_name'],
//            '物流公司'=>$line[ 'wp_code'],
//            '运单号'=>$line[ 'waybill_code'],
//            '收件人'=>$line[ 'receiver_name'],
//            '联系电话'=>$line[ 'receiver_phone'],
//            '地址'=>$line[ 'address'],
//            '发货内容'=>$line[ 'send_content'],
//            '商品名称'=>$line[ 'goods_title'],
//            '商品数量'=>$line[ 'goods_num'],
//            '商品规格x数量'=>$line[ 'sku_info'],
//            '商品编码|sku编码'=>$line[ 'sku_out_id'],
//            '买家留言'=>$line[ 'buyer_message'],
//            '卖家备注'=>$line[ 'seller_memo'],
//            '取号时间'=>$line[ 'created_at'],
//            '运单状态'=>$line[ 'waybill_status'],
//            '订单店铺'=>$line[ 'seller_nick']??'',
//            '模板名称'=>$line[ 'template_name']??'',
//        ];
//    }
//
//    public function nextItem()
//    {
//
//        loop:
//        if($this->continue&&sizeof($this->buffer)>0){
//             return array_shift($this->buffer);
//        }
//        $condition = [];
//        $where = json_decode($this->data['condition'], true);
//        \Log::info('导出取号记条件：', [json_encode($where)]);
//        $waybillStatus = $where['waybill_status'] ?? '';
//        $wpCode = $where['wp_code'] ?? '';
//        $batchNo = $where['batch_no'] ?? '';
//        $keyword = $where['keyword'] ?? '';
//        $orderBy = $where['order_by'] ?? '';
//        $printMode = $where['printMode'] ?? 1;
//        $mode = $where['mode'] ?? 1;
//        $ownerIdList = $where['ownerIdList'] ?? [];
//        $templateIds = $where['template_ids'] ?? [];
//        $limit = $this->limit;
//
//        if(!empty($where['begin_at'])&&!empty($where['end_at'])) {
//            $condition[] = ['waybill_histories.created_at', '>=', $where['begin_at']];
//            $condition[] = ['waybill_histories.created_at', '<=', $where['end_at']];
//        }
//        $shopIdList = $where['shopIdList'] ?? [];
//        $request = new WaybillHistorySearchRequest();
//        if(!empty($condition)) {
//            $request->setCondition($condition);
//        }
//        $request->setOrderBy($orderBy);
//        $request->setLimit($limit);
//
//        $request->setKeyword($keyword);
//        $request->setPrintMode($printMode);
//        $request->setOwnerIdList($ownerIdList);
//        $request->setBatchNo($batchNo);
//        $request->setWpCode($wpCode);
//        $request->setWaybillStatus($waybillStatus);
//        $request->setShopIdList($shopIdList);
//        $request->setTemplateIds($templateIds);
//        $request->setMode($mode);
//
//
////        if($continue&&empty($buffer)) {
//
//
//        $request->setOffset($this->offset);
//        \Log::info('导出取号记录中：', [json_encode($request)]);
//        list($ret, $count) = $this->waybillHistoryQueryService->search($request,false);
//
//        $result = WaybillHistoryQueryService::handleMerge($ret, true);
//        \Log::info('导出取号记录数据处理：' . $this->offset);
//        if(sizeof($result)==0){
//            $this->continue=false;
//            return ;
//
//        }
//        foreach ($result as $index => $item) {
//            $skuArr = $skuOutIdArr = [];
//            if (isset($item['merge_order_item'])) {
//                foreach ($item['merge_order_item'] as $value) {
////                    $skuArr[] = $value['sku_value'] . "x" . $value['goods_num'];
//                    $skuOutIdArr[] = $value['outer_iid'] . "|" . $value['outer_sku_iid'];
//                }
//            }
//            $shop = Shop::query()->where('id', $item['shop_id'])->first();
//            $company = collect(config('express_company'))->where('wpCode', $item['wp_code'])->values()->first();
//            $companyName = !empty($company) ? $company['name'] : ($item['wp_code'] ?? '');
//            $strCreatedAt=isset($item['created_at'])?$item['created_at']->format('Ymd H:i:s'):'';
////            \Log::info("取号记录",[$item]);
//            $list = [
//                'index' => $index + $this->realCount + 1,
//                'batch_no' => isset($item['package']) ? $item['package']['batch_no'] : '',
//                'order_no' => $item['orderNoStr'] ?? '',
//                'outer_order_no' => $item['outer_order_no'],
//                'shop_name' => $shop->shop_name ?? '',
//                'wp_code' => $companyName,
//                'waybill_code' => $item['waybill_code'] ?? '',
//                'receiver_name' => $item['receiver_name'] ?? '',
//                'receiver_phone' => $item['receiver_phone'] ?? '',
//                'address' => $item['receiver_province'] . $item['receiver_city'] . $item['receiver_district'] . $item['receiver_address'],
//                'send_content' => $item['send_content'],
//                'goods_title' => implode('|', array_pluck($item['orderItem'], 'goods_title')),
//                'goods_num' => ReportUtil::buildGoodsNum($item['merge_order_item']),
//                'sku_info' => ReportUtil::buildSkuTitle($item['merge_order_item']),
//                'sku_out_id' => implode('|', $skuOutIdArr),
//                'buyer_message' => isset($item['orderItem']) ? implode("|", array_pluck($item['orderItem'], 'buyer_message')) : '',
//                'seller_memo' => isset($item['orderItem']) ? implode("|", array_pluck($item['orderItem'], 'seller_memo')) : '',
//                'created_at' => $strCreatedAt,
//                'waybill_status' => $item['waybill_status'] == 0 ? '正常' : '已回收',
//                'seller_nick' => $item['seller_nick'] ?? '',
//                'template_name' => $item['template_name'] ?? '',
//            ];
//
//            $list = ReportUtil::handleNumberNotFormat($list);
//            $this->buffer[] = $list;
//        }
//        \Log::info('导出取号记录数据结束：' . $this->offset);
//        $this->offset += count($ret);
//        $this->realCount += count($result);
//        goto loop;
////        }
//    }
//
//    public function getExportData()
//    {
//        \Log::info('导出取号记录开始', ["shopId" => $this->shopId, "data" => $this->data]);
//        while ($this->continue) {
//            $nextItem = $this->nextItem();
//            yield $nextItem;
//
//        }
//        \Log::info('导出取号记录结束' . $this->shopId);
//    }
//
//}
