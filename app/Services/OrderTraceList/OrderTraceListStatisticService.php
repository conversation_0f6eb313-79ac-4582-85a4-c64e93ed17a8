<?php
namespace App\Services\OrderTraceList;
use App\Models\OrderTraceList;
use Illuminate\Support\Facades\Log;


class OrderTraceListStatisticService
{
    function statisticCount(OrderTraceListStatisticRequest $request): array
    {
        Log::info('statisticCount', [$request]);

        $query = OrderTraceList::query()->whereIn('shop_id', $request->shopIds)->where('status', [OrderTraceList::STATUS_DISCARD]);
        if ($request->keyword) {
            $query->where (function ($query)use($request) {
               $query->where('express_no', $request->keyword)
                ->orWhere('tid', $request->keyword);
            });
        }
        if (sizeof($request->wpCode)>0) {
            $query->whereIn('express_code', $request->wpCode);
        }
        if ($request->beginAt) {
            $query->where('send_at', '>=', date('Y-m-d H:i:s', strtotime($request->beginAt)));
        }
        if ($request->endAt) {
            $query->where('send_at', '<=', date('Y-m-d H:i:s', strtotime($request->endAt)));
        }
        $queryGot = clone $query;
        $querySend = clone $query;
        $querySign = clone $query;
//        $res = $query->get();
        $totalCount = $query->count();
        Log::info('statisticCount', ["sql"=>getSqlByQuery($query)]);
        $gotCount = $queryGot->whereIn('status', $this->getStatusArrByTab(1))->count();
        $sendCount = $querySend->whereIn('status', $this->getStatusArrByTab(2))->count();
        $signCount = $querySign->whereIn('status', $this->getStatusArrByTab(3))->count();
        return array('totalCount' => $totalCount, 'gotCount' => $gotCount,
            'sendCount' => $sendCount, 'signCount' => $signCount);
    }
    function dayStatisticGet(OrderTraceListStatisticRequest  $request):array{
        $query = OrderTraceList::query()->whereIn('shop_id', $request->shopIds)->whereNotIn('status', [OrderTraceList::STATUS_DISCARD]);
        if ($request->keyword) {
            $query->where('express_no', $request->keyword)
                ->orWhere('tid', $request->keyword);
        }
        if (sizeof($request->wpCode)>0) {
            $query->whereIn('express_code', $request->wpCode);
        }
        if ($request->beginAt) {
            $query->where('send_at', '>=', date('Y-m-d H:i:s', strtotime($request->beginAt)));
        }
        if ($request->endAt) {
            $query->where('send_at', '<=', date('Y-m-d H:i:s', strtotime($request->endAt)));
        }
        if($request->status){
            $query->whereIn('status', $request->status);
        }
        $rowsFound = $query->count();
        $sortArr = explode(' ', $request->orderBy);
        $ret = $query->limit($request->limit)
            ->offset($request->offset)
            ->orderBy($sortArr[0], $sortArr[1])
            ->get();
        $pagination = [
            'rows_found' => $rowsFound,
            'offset' => $request->offset,
            'limit' => $request->limit
        ];

        return array('pagination' => $pagination, $ret);
    }
    public function getStatusArrByTab($tab): array
    {
        $status = [];
        switch ($tab) {
            case 1: //未揽件
                $status = [OrderTraceList::STATUS_SHIPPED];
                break;
            case 2: // 运输中
                $status = OrderTraceList::STATUS_TRANSPORTING_ARRAY;
                break;
            case 3: // 已签收
                $status = [OrderTraceList::STATUS_SIGN];
                break;
            default:
                break;
        }
        return $status;
    }

    public function discardOrderTrace(?array $orderId)
    {
        OrderTraceList::whereIn('id', $orderId)->update(['status' => OrderTraceList::STATUS_DISCARD]);
    }

}
