<?php

namespace App\Services\Order\Request;

use App\Services\Order\Request\DeliveryOrderItem;

/**
 * 预发货的发货请求，这个请求对接了预发货页面的手动发货和自动发货两个场景
 * 对应请求JSON格式如下
 * //{"list":[{"waybillCode":"运单号","wpCode":"快递公司 code","items":[{"orderId":"订单id","orderItemId":"子订单id","num":"数量"}]}]}
 *
 */
class PreshipmentDeliveryRequest
{
    public $shopId;
    /**
     * @var PreshipmentWaybillItem[] $waybillItems
     */
    public $waybillItems=[];

    /**
     *$data = $this->validate($request, [
     * 'list' => 'required|array',
     * 'list.*.orderId' => 'required|integer',
     * 'list.*.waybillCode' => 'required|string',
     * 'list.*.wpCode' => 'required|string',
     * 'list.*.items' => 'required|array',
     * 'list.*.items.*.orderId' => 'required|integer',
     * 'list.*.items.*.orderItemId' => 'required|integer',
     * 'list.*.items.*.num' => 'required|integer',
     * ]);
     */
    public static function fromPreshipmentDeliveryRequest($data,$shopId):PreshipmentDeliveryRequest{
        $preshipmentDeliveryRequest = new PreshipmentDeliveryRequest();
        $preshipmentDeliveryRequest->shopId = $shopId;

        foreach($data['list'] as $item) {
            $preshipmentWaybillItem = new PreshipmentWaybillItem();
            $preshipmentWaybillItem->waybillCode = $item['waybillCode'];
            $preshipmentWaybillItem->wpCode = $item['wpCode'];
            foreach ($item['items'] as $deliveryOrderItems) {
                $deliveryOrderItem=new DeliveryOrderItem();
                $deliveryOrderItem->orderId = $deliveryOrderItems['orderId'];
                $deliveryOrderItem->orderItemId = $deliveryOrderItems['orderItemId'];
                $deliveryOrderItem->num = $deliveryOrderItems['num'];
                $preshipmentWaybillItem->addItem($deliveryOrderItem);
            }
            $preshipmentDeliveryRequest->addItem($preshipmentWaybillItem);

        }

        return $preshipmentDeliveryRequest;


    }

    public function addItem(PreshipmentWaybillItem $item){
        $this->waybillItems[] = $item;
    }

    /**
     * @return array 返回订单id数组
     */
    public function getOrderIds():array{
        $orderIds = [];
        foreach ($this->waybillItems as $item) {
            foreach($item->items as $deliveryOrderItem){
                if(!in_array($deliveryOrderItem->orderId, $orderIds)){
                    $orderIds[]=$deliveryOrderItem->orderId;
                }
            }
        }
        return $orderIds;
    }

    public function getOrderItemIds():array{
        $orderItemIds = [];
        foreach ($this->waybillItems as $item) {
            foreach($item->items as $deliveryOrderItem){
                if(!in_array($deliveryOrderItem->orderItemId, $orderItemIds)){
                    $orderItemIds[]=$deliveryOrderItem->orderItemId;
                }
            }
        }
        return $orderItemIds;
    }

    public function getWaybillCodes():array{
        $waybillCodes = [];
        foreach ($this->waybillItems as $item) {
            if(!in_array($item->waybillCode, $waybillCodes)){
                $waybillCodes[]=$item->waybillCode;
            }
        }
        return $waybillCodes;
    }

    /**
     * @param $waybillCode
     * @return PreshipmentWaybillItem[] 返回waybillCode对应的items
     */
    public function getWaybillItemsByWaybillCode($waybillCode):array{
       $waybillItems = [];
        foreach ($this->waybillItems as $item) {
            if($item->waybillCode == $waybillCode){
                $waybillItems[]=$item;
            }
        }
        return $waybillItems;
    }

    public function isEmpty():bool{
        return empty($this->waybillItems);
    }
}
