<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2020/3/18
 * Time: 20:50
 */

namespace App\Services\Order\Impl;


use App\Constants\ErrorConst;
use App\Constants\OperationLogTypeConst;
use App\Constants\PlatformConst;
use App\Constants\RefundSubStatusConst;
use App\Events\Event;
use App\Events\Orders\OrderCreateEvent;
use App\Events\Orders\OrderDecryptEvent;
use App\Events\Orders\OrderPrintEvent;
use App\Events\Orders\OrderQueryEvent;
use App\Events\Orders\OrderUpdateEvent;
use App\Events\SqlLogEvent;
use App\Events\Users\UserLoginEvent;
use App\Exceptions\ApiException;
use App\Exceptions\ClientException;
use App\Exceptions\OrderException;
use App\Http\StatusCode\StatusCode;
use App\Models\FactoryOrder;
use App\Models\Goods;
use App\Models\Order;
use App\Models\OrderExtra;
use App\Models\OrderItem;
use App\Models\OrderItemExtra;
use App\Models\Shop;
use App\Models\UserExtra;
use App\Services\Bo\LogisticsDataBo;
use App\Services\Bo\LogisticsDataProductBo;
use App\Services\Bo\OrderResponseBo;
use App\Services\Bo\WaybillRecoveryResponseBo;
use App\Services\Client\DyClient;
use App\Services\CommonResponse;
use App\Services\Order\AbstractOrderService;
use App\Services\Order\OrderCipherInterface;
use App\Services\Order\Request\OrderDeliverAgainRequest;
use App\Services\Order\Request\ReportOrderSecurityEventRequest;
use App\Utils\RandomUtil;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use InvalidArgumentException;

class DyOrderImpl extends AbstractOrderService implements OrderCipherInterface
{
    private $shopId;

//    protected $gatewayUrl = 'https://openapi-fxg.jinritemai.com';
//    protected $gatewayUrl = 'http://proxy-dy.mayiapps.cn';

    protected $page = 0;
    protected $platformType = PlatformConst::PLATFORM_TYPE_DY;

    protected $orderStatusMap = [
        0 => Order::ORDER_STATUS_UNKNOWN,
        1 => Order::ORDER_STATUS_PADDING,
        2 => Order::ORDER_STATUS_PAYMENT,
        3 => Order::ORDER_STATUS_DELIVERED,
        4 => Order::ORDER_STATUS_CLOSE,
        5 => Order::ORDER_STATUS_SUCCESS,
        101 => Order::ORDER_STATUS_PART_DELIVERED,
        105 => Order::ORDER_STATUS_PAYMENT_REVIEW,
    ];

    protected $distStatusMap = [
        1 => FactoryOrder::DIST_STATUS_ASSIGNED,
        2 => FactoryOrder::DIST_STATUS_RETURNED,
        3 => FactoryOrder::DIST_STATUS_CANCELLED,
    ];


//    protected $refundStatusMap = [
//        6 => OrderItem::REFUND_STATUS_WAIT_SELLER, // (退货) 退货中-用户申请
//        7 => OrderItem::REFUND_STATUS_PROCESSING, // (退货) 退货中-商家同意退货
//        8 => OrderItem::REFUND_STATUS_PROCESSING, // (退货) 退货中-客服仲裁
//        9 => OrderItem::REFUND_STATUS_CLOSED, // (退货) 已关闭-退货失败
//        10 => OrderItem::REFUND_STATUS_PROCESSING, // (退货) 退货中-客服同意
//        11 => OrderItem::REFUND_STATUS_PROCESSING, // (退货) 退货中-用户填写完物流
//        12 => OrderItem::REFUND_STATUS_PROCESSING, // (退货) 已关闭-商户同意
//        13 => OrderItem::REFUND_STATUS_PROCESSING, // (退货) 退货中-再次客服仲裁
//        14 => OrderItem::REFUND_STATUS_PROCESSING, // (退货) 已关闭-客服同意
//        15 => OrderItem::REFUND_STATUS_CLOSED, // (退货) 取消退货申请
//        16 => OrderItem::REFUND_STATUS_PROCESSING, // (退款) 申请退款中 (商家还未发货, 用户申请退款)
//        17 => OrderItem::REFUND_STATUS_PROCESSING, // (退款) 商户同意退款
//        18 => OrderItem::REFUND_STATUS_PROCESSING, // (退款) 订单退款仲裁中
//        19 => OrderItem::REFUND_STATUS_PROCESSING, // (退款) 退款仲裁支持用户
//        20 => OrderItem::REFUND_STATUS_PROCESSING, // (退款) 退款仲裁支持商家
//        21 => OrderItem::REFUND_STATUS_SUCCESS, // (退款) 订单退款成功 (即用户在商家出库前可发起退款, 最终退款成功)
//        22 => OrderItem::REFUND_STATUS_SUCCESS, // (退款) 售后退款成功 (即用户在退款后, 正常走完退货流程, 退款成功)
//        23 => OrderItem::REFUND_STATUS_PROCESSING, // (退货) 退货中-再次用户申请
//        24 => OrderItem::REFUND_STATUS_CLOSED, // (退货) 已关闭-退货成功
//        25 => OrderItem::REFUND_STATUS_PROCESSING, // 备货中-用户取消
//        26 => OrderItem::REFUND_STATUS_PROCESSING, // 备货中-退款商家拒绝
//        27 => OrderItem::REFUND_STATUS_PROCESSING, // 退货中-商家拒绝退货
//        28 => OrderItem::REFUND_STATUS_CLOSED, // 售后关闭
//        29 => OrderItem::REFUND_STATUS_PROCESSING, // 退货中-商家再次拒绝
//        30 => OrderItem::REFUND_STATUS_PROCESSING, // 退款中-退款申请
//        31 => OrderItem::REFUND_STATUS_CLOSED, // 退款申请取消
//        32 => OrderItem::REFUND_STATUS_PROCESSING, // 退款成功-商家同意
//        33 => OrderItem::REFUND_STATUS_PROCESSING, // 退款中-商家拒绝
//        34 => OrderItem::REFUND_STATUS_PROCESSING, // 退款中-客服仲裁
//        35 => OrderItem::REFUND_STATUS_PROCESSING, // 退款中-客服同意
//        36 => OrderItem::REFUND_STATUS_CLOSED, // 退款中-退款失败
//        37 => OrderItem::REFUND_STATUS_CLOSED, // 已关闭-退款失败
//        38 => OrderItem::REFUND_STATUS_SUCCESS, // 退款中-线下退款成功
//        39 => OrderItem::REFUND_STATUS_SUCCESS, // 退款中-退款成功
//    ];

    protected $refundStatusMap = [
        0 => Order::REFUND_STATUS_NO,//售后初始化
        6 => Order::REFUND_STATUS_YES, // (退货) 退货中-用户申请
        7 => Order::REFUND_STATUS_YES, //售后退货中
        27 => Order::REFUND_STATUS_NO, //拒绝售后申请
        12 => Order::REFUND_STATUS_YES, //售后成功
        28 => Order::REFUND_STATUS_NO, //售后失败(取消售后)
        11 => Order::REFUND_STATUS_YES, //售后已发货
        29 => Order::REFUND_STATUS_YES, //退货后拒绝退款
        13 => Order::REFUND_STATUS_YES, //售后换货商家发货
        14 => Order::REFUND_STATUS_YES, //售后换货用户收货
        51 => Order::REFUND_STATUS_YES, //取消成功
        53 => Order::REFUND_STATUS_YES, //逆向交易完成
    ];

    protected $orderFlagMap = [
        0 => Order::FLAG_GRAY,
        1 => Order::FLAG_PURPLE,
        2 => Order::FLAG_CYAN,
        3 => Order::FLAG_GREEN,
        4 => Order::FLAG_ORANGE,
        5 => Order::FLAG_RED,
    ];

  const ORDER_FLAG_MAP_REVERT=[
      Order::FLAG_GRAY => 0,
      Order::FLAG_PURPLE => 1,
      Order::FLAG_CYAN => 2,
      Order::FLAG_GREEN => 3,
      Order::FLAG_ORANGE => 4,
      Order::FLAG_RED => 5
  ];

    const OTHER_CODE = 9999;  //其他

    const CODE_NORMAL = 1000; //正常
    const CODE_UNBIND_AUTH = 30006; //取消授权错误码
    const CODE_TOKEN_EXPIRED = 30002; //access_token已过期
    const CODE_SERVER_ERROR = 7;     //服务器开了个小差
    const CODE_SERVER_HIGH_PRESSURE = 8;     //系统当前正处于访问高峰，稍后重试即可成功

    //错误码映射
    protected $errorCodeMap = [
        self::CODE_UNBIND_AUTH => self::ERROR_CODE_UNBIND,
        self::CODE_TOKEN_EXPIRED => self::ERROR_CODE_UNBIND,
        self::CODE_SERVER_ERROR => self::ERROR_CODE_SERVER,
        self::CODE_SERVER_HIGH_PRESSURE => self::ERROR_CODE_SERVER,
    ];

    protected $expressCodeList
        = [
            'HT' => 20,
            'SF' => 12,
            'ZTO' => 15,
            'YTO' => 7,
            'yuantong' => 7,
            'STO' => 8,
            'shentong' => 8,
            'YUNDA' => 9,
            'yunda' => 9,
            'EMS' => 17,
            'JD' => 30,
            'jd' => 30,
            'TT' => 14,
            'ZJS' => 21,
            'YZXB' => 19,
            'YS' => 11,
            'DB' => 13,
            'AIR' => self::OTHER_CODE,  //todo 亚风速递 平台不支持
            'HTKY' => 20,
            'POSTB' => 19,
            'youzhengguonei' => 19,
            'YDKY' => 9,
            'ZTOKY' => 15,
            'zhongtong' => 15,
            'HOAU' => self::OTHER_CODE,
            'BESTQJT' => 20,
            'huitongkuaidi' => 20,
            'RRS' => self::OTHER_CODE,
            'KYE' => self::OTHER_CODE,
            'SDSD' => 1018,
            'ANKY' => 32,
            'OTP' => self::OTHER_CODE,
            'AXWL' => self::OTHER_CODE,
            'SZKKE' => self::OTHER_CODE,
            'SXJD' => self::OTHER_CODE,
            'DEBANGWULIU' => 13,
            'SFKY' => 12,
            'ZTOINTER' => 15,
            'YDGJ' => 9,
            'STOINTER' => 8,
            'JIUYE' => self::OTHER_CODE,
            'GJ' => self::OTHER_CODE,
            'CN7000001021040' => 9,
            'CN7000001003751' => self::OTHER_CODE,
            '100007887' => self::OTHER_CODE,
            'SURE' => self::OTHER_CODE,
            'CP570969' => self::OTHER_CODE,
            '2608021499_235' => 32,
            'CP468398' => 7,
            'FAST' => 26,
            '3108002701_1011' => 15,
            'DBKD' => 13,
            'CN7000001000869' => 32,
            'CP471906' => self::OTHER_CODE,
            'GTO' => 25,
            'QFKD' => 16,
            'EYB' => 17,
            'ems' => 17,
            'CP457538' => self::OTHER_CODE,
            'SNWL' => 202,
            'CN7000001017817' => 8,
            '100004928' => 22,
            'CP443514' => 20,
            'FEDEX' => 34,
            '5000000007756' => 19,
            'TTKDEX' => 14,
            'UC' => 11,
            'JTSD' => 1021,
            'jtexpress' => 1021,
            'shunfeng' => 12,
            'youshuwuliu' => 11,
            'fengwang' => 1033,
        ];

    protected $expressCodeMap = array(
        'HT' => 'huitongkuaidi',
        'SF' => 'shunfeng',
        'ZTO' => 'zhongtong',
        'YTO' => 'yuantong',
        'yuantong' => 'yuantong',
        'STO' => 'shentong',
        'shentong' => 'shentong',
        'YUNDA' => 'yunda',
        'yunda' => 'yunda',
        'EMS' => 'ems',
        'JD' => 'jd',
        'jd' => 'jd',
        'TT' => 'tiantian',
        'ZJS' => 'zhaijisong',
        'YZXB' => 'youzhengguonei',
        'YS' => 'youshuwuliu',
        'DB' => 'debangwuliu',
        'AIR' => '',
        'HTKY' => 'huitongkuaidi',
        'POSTB' => 'youzhengguonei',
        'youzhengguonei' => 'youzhengguonei',
        'YDKY' => 'yunda',
        'ZTOKY' => 'zhongtong',
        'zhongtong' => 'zhongtong',
        'HOAU' => '',
        'BESTQJT' => 'huitongkuaidi',
        'huitongkuaidi' => 'huitongkuaidi',
        'RRS' => '',
        'KYE' => '',
        'SDSD' => 'dsukuaidi',
        'ANKY' => 'annengwuliu',
        'OTP' => '',
        'AXWL' => '',
        'SZKKE' => '',
        'SXJD' => '',
        'DEBANGWULIU' => 'debangwuliu',
        'SFKY' => 'shunfeng',
        'ZTOINTER' => 'zhongtong',
        'YDGJ' => 'yunda',
        'STOINTER' => 'shentong',
        'JIUYE' => '',
        'GJ' => '',
        'CN7000001021040' => 'yunda',
        'CN7000001003751' => '',
        '100007887' => '',
        'SURE' => '',
        'CP570969' => '',
        '2608021499_235' => 'annengwuliu',
        'CP468398' => 'yuantong',
        'FAST' => '',
        '3108002701_1011' => 'zhongtong',
        'DBKD' => 'debangwuliu',
        'DBKY' => 'debangkuaiyun',
        'CN7000001000869' => 'annengwuliu',
        'CP471906' => '',
        'GTO' => '',
        'QFKD' => '',
        'EYB' => 'ems',
        'ems' => 'ems',
        'CP457538' => '',
        'SNWL' => 'suning',
        'CN7000001017817' => 'shentong',
        '100004928' => '',
        'CP443514' => 'huitongkuaidi',
        'FEDEX' => '',
        '5000000007756' => 'youzhengguonei',
        'TTKDEX' => 'tiantian',
        'UC' => 'youshuwuliu',
        'JTSD' => 'jtexpress',
        'jtexpress' => 'jtexpress',
        'shunfeng' => 'shunfeng',
        'youshuwuliu' => 'youshuwuliu',
        'fengwang' => 'fengwang',
        'FENGWANG' => 'fengwang',
        'shunfengkuaiyun' => 'shunfengkuaiyun',
        'debangwuliu' => 'debangwuliu',
        'yundakuaiyun' => 'yundakuaiyun',
        'debangkuaiyun' => 'debangkuaiyun',
    );
    /**
     * 官方的 code to id 的映射
     * @see https://op.jinritemai.com/docs/api-docs/16/541
     * @var array
     */
    protected $expressCodeToIdMap = [
        "yimidida" => 897,
        "danniao" => 1017,
        "XMTC" => 1047,
        "jinguangsudikuaijian" => 517,
        "savor" => 1025,
        "jd" => 30,
        "yzguonei" => 854,
        "sxjdfreight" => 1024,
        "yijiuyijiu" => 1029,
        "stosolution" => 968,
        "xinfengwuliu" => 36,
        "dsukuaidi" => 1018,
        "wanxiangwuliu" => 31,
        "yuantongguoji" => 1034,
        "zhihuashi" => 1045,
        "suning" => 202,
        "zhongtongguoji" => 597,
        "lntjs" => 289,
        "sfwl" => 1026,
        "NZSY" => 1048,
        "chuanhua" => 1049,
        "youzhengbk" => 683,
        "anxl" => 631,
        "zilegongmao" => 1038,
        "shunfengguoji" => 1040,
        "zhongtongkuaiyun" => 846,
        "subida" => 716,
        "xlair" => 1051,
        "zhongtong" => 15,
        "yundakuaiyun" => 952,
        "ztocc" => 1052,
        "taijin" => 1032,
        "fengwang" => 1033,
        "exfresh" => 284,
        "annengwuliu" => 32,
        "ndwl" => 317,
        "huitongkuaidi" => 20,
        "shunfengchengpei" => 1053,
        "xlobo" => 475,
        "yuantongkuaiyun" => 909,
        "ems" => 17,
        "lyh" => 1050,
        "youshuwuliu" => 11,
        "shunfengkuaiyun" => 1035,
        "xintianweng" => 1039,
        "shentong" => 8,
        "ztky" => 140,
        "kuayue" => 397,
        "jiuyescm" => 191,
        "tiandihuayu" => 518,
        "shenghuiwuliu" => 244,
        "youzhengguonei" => 19,
        "jtexpress" => 1021,
        "meiriyouxian" => 1044,
        "debangwuliu" => 13,
        "debangkuaiyun" => 1100,
        "zhaijisong" => 21,
        "shunfeng" => 12,
        "yunda" => 9,
        "shanhuodidi" => 1042,
        "huangmajia" => 37,
        "baishiwuliu" => 637,
        "jiayiwuliu" => 10,
        "suer" => 243,
        "annto" => 861,
        "jiayunmeiwuliu" => 327,
        "linshiwuliu" => 1031,
        "udalogistic" => 1043,
        "gujiajiaju" => 1030,
        "nongfushanquan" => 1036,
        "rrs" => 641,
        "tiantian" => 14,
        "zhimakaimen" => 440,
        "zhongyouex" => 1022,
        "yuantong" => 7,
        "shanxijianhua" => 1037,
    ];

    /**
     * 每次拉取订单间隔的分钟
     * @var int
     */
    public $orderTimeInterval = 120;

    /**
     * 每次拉取增量订单间隔的分钟
     * @var int
     */
    public $orderIncrTimeInterval = 60;

    /**
     * 每次拉取退款订单间隔的分钟
     * @var int
     */
    public $refundOrderTimeInterval = 60;

    /**
     * 每次拉取厂家订单间隔的分钟
     * @var int
     */
    public $factoryOrderTimeInterval = 30;

    /**
     * 临时的游标
     * @var
     */
    private $tmp_cursor;
//    private $gateway = 'https://openapi-fxg.jinritemai.com';
//    private $gateway = 'http://proxy-dy.mayiapps.cn';


    public function formatToAfterSale(array $trade)
    {
        $itemRefundStatus = OrderItem::REFUND_STATUS_NO;
        $orderInfo = array_get($trade, 'order_info');
        if (!$orderInfo) {
            return $itemRefundStatus;
        }

        if ($this->hasRefundStatus($orderInfo['final_status']) > 0) {
            $itemRefundStatus = $this->formatRefundStatus($orderInfo['final_status']);
        }

        return $itemRefundStatus;
    }

    /**
     * @inheritDoc
     */
    public function formatToOrder(array $trade): array
    {
        $orderItems = [];
        $promiseShipAt = [];
        $preSaleTypeArr = []; // 预售类型
        $tid = $trade['order_id'];

        $is_home_delivery = 0; // 是否送货上门
        $is_live_order = 0; // 是否直播订单
        $is_gift_order = 0; // 是否赠品订单
        $is_give_gift=0;//是否是赠送礼品单
        $is_remote_transit = 0; // 是否偏远中转
        $urge_shipment_at = null; // 催发货时间
        $room_id = 0; // 直播间 id
        $refundStatusArr = [];
        foreach ($trade['sku_order_list'] as $index => $item) {
            $total = $item['pay_amount'];
//            $sku_value = '';
//            foreach ($item['spec'] as $desc) {
//                $sku_value .= $desc['value'] . ';';     //不带规格名：XL;红色;
//            }
            $skuList = [];
            if (!empty($item['spec'])){
                foreach ($item['spec'] as $desc) {
                    $skuList[] = ['name'=>$desc['name'], 'value'=>$desc['value']];
                }
            }
            list($skuValue, $skuValue1, $skuValue2) = $this->getSkuValueAnd12($skuList);
            $status = $this->formatOrderStatus($item['order_status']);
            $refundStatus = $this->hasRefundStatus($item['after_sale_info']['after_sale_status']);
            $goodsType = (!empty($item['given_product_type']) && $item['given_product_type'] == 'FREE') ? OrderItem::GOODS_TYPE_GIFT : OrderItem::GOODS_TYPE_NORMAL;
            if ($goodsType == OrderItem::GOODS_TYPE_GIFT && $skuValue == '默认;'){
                $skuValue = $item['product_name'];
            }
            $is_pre_sale = 0; // 是否预售
            // 判断exp_ship_time - 当前时间是大于48小时
            if (!empty($item['pre_sale_type']) && (!empty($item['exp_ship_time']) && ($item['exp_ship_time'] - time()) > 172800)) {
                $is_pre_sale = 1;
            }

            $orderItem = [
                "tid" => $tid, //主订单
                "oid" => (string)$item['order_id'], //子订单号
                "type" => $this->platformType, //订单类型
                "payment" => formatToYuan($total), //实付金额
                "total_fee" => formatToYuan($total), //总金额
                "discount_fee" => formatToYuan($item['promotion_amount']), //优惠金额
                "goods_type" => $goodsType,
                "goods_pic" => $item['product_pic'] ?? "", //商品图片
                "goods_title" => $item['product_name'], //商品标题
                "goods_price" => formatToYuan($item['origin_amount']), //商品单价
                "goods_num" => $item['item_num'], //商品数量
                "num_iid" => $item['product_id'], //商品id
                "sku_id" => $item['sku_id'] ?? '', //sku id
                "sku_value" => $skuValue,
                "sku_value1" => $skuValue1,
                "sku_value2" => $skuValue2,
                "outer_iid" => '', //
                "outer_sku_iid" => $item['code'], //平台文档有问题，实际是 sku 编码。 商家外部商品编码,商家编码code  $item['out_sku_id'] ?? '', //商家外部sku编码
                "order_created_at" => date('Y-m-d H:i:s', $trade['create_time']), //订单创建时间
                "order_updated_at" => date('Y-m-d H:i:s', $trade['update_time']), //订单修改时间
                "status" => $status,
                "refund_status" => $refundStatus, //
                "refund_sub_status" => $this->formatSubRefundStatus($item), //
                'send_at' => (isset($item['ship_time']) && $item['ship_time']) ? date('Y-m-d H:i:s', $item['ship_time']) : null,//发货时间
                'is_comment' => (isset($item['is_comment']) && $item['is_comment'] == Order::IS_COMMENT_YES) ? Order::IS_COMMENT_YES : 0, // 是否评价
                'send_num' => 0,
                'send_remain_num' => $item['item_num'],
                'author_id' => $item['author_id'] ?? null,
                'author_name' => $item['author_name'] ?? null,
                'is_pre_sale' => $is_pre_sale,
                'promise_ship_at' => !empty($item['exp_ship_time']) ? date('Y-m-d H:i:s', $item['exp_ship_time']) : null,
            ];

//            !empty($item['pre_sale_type']) && $preSaleTypeArr[] = $item['pre_sale_type'];

            //子订单发货时间
            if ($item['exp_ship_time']) {
                $promiseShipAt[] = $item['exp_ship_time'];
            }
            if (!empty($item['room_id'])){
                $room_id = $item['room_id'];
            }
            if ($goodsType == 2){
                $is_gift_order = 1;
            }else{
                // 只有主商品才计算退款
                $refundStatusArr[] = $refundStatus;
            }
            $orderItems[] = $orderItem;
        }
        $orderItems = array_pluck($orderItems, null, 'oid');
        foreach ($trade['logistics_info'] as $item) {
            foreach ($item['product_info'] as $productInfo) {
                $tempOrderItem = $orderItems[$productInfo['sku_order_id']];
                $tempOrderItem['send_num'] += $productInfo['product_count'];
                $tempOrderItem['send_remain_num'] = $tempOrderItem['goods_num'] - $tempOrderItem['send_num'];
                $orderItems[$productInfo['sku_order_id']] = $tempOrderItem;
            }
        }
        $orderItems = array_values($orderItems);
        !empty($promiseShipAt) && sort($promiseShipAt);
        $orderStatus = $this->formatOrderStatus($trade['order_status']);

        //主订单退款状态
        $refund_status = in_array(Order::REFUND_STATUS_YES, $refundStatusArr) ? Order::REFUND_STATUS_YES : Order::REFUND_STATUS_NO;

        //判读部分退款还是全部退款 有一个未退款就是部分退款(主订单状态是退款或者子订单中有退款 并且有未退款的子订单)
        if (($refund_status == Order::REFUND_STATUS_YES || in_array(Order::REFUND_STATUS_YES, $refundStatusArr)) && in_array(Order::REFUND_STATUS_NO, $refundStatusArr)) {
            $refund_status = Order::REFUND_STATUS_PART;
        }
        //部分发货且部分退款 修正订单状态
        if ($orderStatus == Order::ORDER_STATUS_PART_DELIVERED) {
            $noOrderItem = collect($orderItems)->whereIn('status', [Order::ORDER_STATUS_PAYMENT, Order::ORDER_STATUS_FAILED, Order::ORDER_STATUS_CLOSE]);
            $hasNoRefund = collect($noOrderItem)->where('refund_status', 0);
            //剩下未发货子订单 全部退款了 修正订单状态为已发货
            if ($noOrderItem->count() > 0 && count($hasNoRefund) == 0) {
                // 不知道这段逻辑是干嘛的 导致订单状态不对 先注释了
//                $orderStatus = Order::ORDER_STATUS_DELIVERED;
            }
            //部分发货主订单上没有发货时间 取子订单最后发货时间
            $shipTime = $this->getShipTime($trade['sku_order_list']);

        }
        // 状态订正 订单完成且售后完成  订正成交易关闭
        if ($refund_status == Order::REFUND_STATUS_YES && $orderStatus == Order::ORDER_STATUS_SUCCESS) {
            $orderStatus = Order::ORDER_STATUS_CLOSE;
        }

        $receiverState = $trade['post_addr']['province']['name'] ?? '';
        $receiverCity = $trade['post_addr']['city']['name'] ?? '';
        $receiverDistrict = $trade['post_addr']['town']['name'] ?? '';
        $receiverTown = $trade['post_addr']['street']['name'] ?? '';
        $cipher_info = [
            'receiver_phone_ciphertext' => $trade['encrypt_post_tel'] ?? $trade['encrypt_encrypt_encrypt_post_tel'],
            'receiver_name_ciphertext' => $trade['encrypt_post_receiver'] ?? $trade['encrypt_encrypt_encrypt_post_receiver'],
            'receiver_address_ciphertext' => $trade['post_addr']['encrypt_detail'] ?? $trade['post_addr']['encrypt_encrypt_encrypt_detail'],
            'receiver_phone_mask' => $trade['mask_post_tel'],
            'receiver_name_mask' => $trade['mask_post_receiver'],
            'receiver_address_mask' => $trade['mask_post_addr']['detail'],
            'oaid' => $trade['open_address_id'] ?? '',
        ];

        if (!empty($cipher_info['oaid'])) {
            // 如果出现不一致的情况，可以先用 doudian_open_id 判断，然后再调order/merge合单校验接口判断。
            $receiver_address = $cipher_info['receiver_address_mask'];
            $receiver_name = $cipher_info['receiver_name_mask'];
            // 长度太长了，md5 缩短下
            $receiver_phone = md5($trade['doudian_open_id'] . '-' . $receiver_name . '-' . $receiverState . $receiverCity . $receiverDistrict . $receiverTown);
        } else {
            $receiver_phone = '$' . $this->cipherExtractSearch($cipher_info['receiver_phone_ciphertext']) . '$';
            $receiver_name = $this->cipherExtractSearch($cipher_info['receiver_name_ciphertext']);
            $receiver_address = $this->cipherExtractSearch($cipher_info['receiver_address_ciphertext']);
        }
        if (!empty($trade['shop_order_tag_ui'])){
            foreach ($trade['shop_order_tag_ui'] as $item) {
                if ($item['key'] == 'home_delivery'){ // 送货上门
                    $is_home_delivery = 1;
                }elseif ($item['key'] == 'logistics_transit'){// 偏远中转
                    $is_remote_transit = 1;
                }elseif($item['key'] == 'give_gift') { // 赠送礼品单
                    $is_give_gift = 1;
                }
            }
        }

        if (!empty($room_id)){
            $is_live_order = 1;
        }
        // 包裹信息
        $logistics_data = [];
        foreach ($trade['logistics_info'] as $logistics_info) {
            $product_list = [];
            foreach ($logistics_info['product_info'] as $item) {
                $sku_value = '';
                foreach ($item['sku_specs'] as $sku) {
                    $sku_value .= $sku['value'] . ';';
                }
                $logisticsDataProductBo = new LogisticsDataProductBo();
                $logisticsDataProductBo->oid = $item['sku_order_id'];
                $logisticsDataProductBo->sku_id = $item['sku_id'];
                $logisticsDataProductBo->outer_sku_id = $item['outer_sku_id'];
                $logisticsDataProductBo->num_iid = $item['product_id'];
                $logisticsDataProductBo->num = $item['product_count'];
                $logisticsDataProductBo->goods_title = $item['product_name'];
                $logisticsDataProductBo->sku_value = $sku_value;
                $product_list[] = $logisticsDataProductBo;
            }
            $logisticsDataBo = new LogisticsDataBo();
            $logisticsDataBo->waybill_code = $logistics_info['tracking_no']; // 运单号
            $logisticsDataBo->wp_code = $logistics_info['company'];
            $logisticsDataBo->wp_name = $logistics_info['company_name'];
            $logisticsDataBo->delivery_at = !empty($logistics_info['ship_time'])?date('Y-m-d H:i:s', $logistics_info['ship_time']):null;
            $logisticsDataBo->delivery_id = $logistics_info['delivery_id'];// 包裹 id
            $logisticsDataBo->product_list = $product_list;
            $logistics_data[] = $logisticsDataBo;
        }

        $order_is_pre_sale = collect($orderItems)->pluck('is_pre_sale')->filter()->count() > 0 ? 1 : 0;
        $orderData = [
            "tid" => $tid, //主订单
            "type" => $this->platformType, //订单类型
//            "express_code" => array_search($trade['logistics_id'], $this->expressCodeList) ?: null , //快递公司代码
//            "express_no" => array_get($trade, 'logistics_code', null), //快递单号
            "buyer_id" => $trade['doudian_open_id'] ?? '', //买家ID
            "buyer_nick" => '', //买家昵称
            "seller_nick" => $trade['shop_name'], //卖家昵称
            "shop_title" => $trade['shop_name'], //店铺名
            "order_status" => $orderStatus, //订单状态
            "refund_status" => $refund_status, //退款状态
            "receiver_state" => $receiverState, //收货人省份
            "receiver_city" => $receiverCity == "市辖区" ? $receiverState : $receiverCity, //收货人城市
            "receiver_district" => $receiverDistrict, //收货人地区
            "receiver_town" => $receiverTown, //收货人街道
            "receiver_name" => $receiver_name, //收货人名字
            "receiver_phone" => $receiver_phone, //收货人手机
            "receiver_zip" => 0, //收件人邮编
            "receiver_address" => $receiver_address, //收件人详细地址
            "payment" => formatToYuan($trade['pay_amount']), //实付金额
            "total_fee" => formatToYuan($trade['order_amount']), //总金额
            "discount_fee" => formatToYuan($trade['promotion_amount']), //优惠金额
            "post_fee" => formatToYuan($trade['post_amount']), //运费
            //"seller_flag" => Order::FLAG_NONE, //卖家备注旗帜
            "seller_flag" => $this->formatOrderFlag($trade['seller_remark_stars']), //卖家备注旗帜
            "seller_memo" => empty($trade['seller_words']) ? '[]' : json_encode([$trade['seller_words']], 320), //卖家备注
            "buyer_message" => $trade['buyer_words'], //买家留言
            "has_buyer_message" => empty($trade['buyer_words']) ? 0 : 1, //买家留言
            "order_created_at" => date('Y-m-d H:i:s', $trade['create_time']), //订单创建时间
            "order_updated_at" => date('Y-m-d H:i:s', $trade['update_time']), //订单修改时间
            "send_at" => !empty($trade['ship_time']) ? date('Y-m-d H:i:s', $trade['ship_time']) :
                (!empty($shipTime) ? date('Y-m-d H:i:s', $shipTime) : null), //发货时间
            "pay_at" => !empty($trade['pay_time']) ? date('Y-m-d H:i:s', $trade['pay_time']) : null, //支付时间
            'is_comment' => (isset($trade['is_comment']) && $trade['is_comment'] == Order::IS_COMMENT_YES) ? Order::IS_COMMENT_YES : 0, // 是否评价
            'num' => array_sum(array_column($orderItems, 'goods_num')), // 商品数量
            'sku_num' => count($orderItems), // SKU数量
            'is_pre_sale' => $order_is_pre_sale, // 是否预售1是0否
            'promise_ship_at' => !empty($promiseShipAt) ? date('Y-m-d H:i:s', $promiseShipAt[0]) :
                (!empty($trade['exp_ship_time']) ? date('Y-m-d H:i:s', $trade['exp_ship_time']) : null),// 承诺发货时间
//            'district_code' => $trade['post_addr']['town']['id']??0,
            'is_home_delivery' => $is_home_delivery,
            'is_live_order' => $is_live_order,
            'is_gift_order' => $is_gift_order,
            'is_remote_transit' => $is_remote_transit,
            'is_give_gift' => $is_give_gift,
            'urge_shipment_at' => $urge_shipment_at,
            'items' => $orderItems,
            'cipher_info' => $cipher_info,
//            'logisticsDataBoList' => $logistics_data,
            'order_extra' => [
                'order_biz_type' => $trade['biz'] == 8 ? OrderExtra::BIZ_TYPE_QUALITY_INSPECTION : OrderExtra::BIZ_TYPE_GENERAL, // 订单业务类型
                'logistics_data' => jsonEncode($logistics_data),
            ],
        ];
        $orderData['district_code'] = $this->getDistrictCodeByAddress($orderData['receiver_state'], $orderData['receiver_city'], $orderData['receiver_district']);


        //灰度安全改造
//        if (config('app.platform') == 'dy') {
//            $orderData['receiver_name'] = $trade['encrypt_post_receiver'];
//            $orderData['receiver_phone'] = $trade['encrypt_post_tel'];
//            $orderData['receiver_address'] = $trade['post_addr']['encrypt_detail'];
//        }
        //可能为空
        if (empty($orderData['express_code']) || empty($orderData['express_no'])) {
            unset($orderData['express_code']);
            unset($orderData['express_no']);
        }

        return $orderData;
    }

    /**
     * @inheritDoc
     */
    public function formatToOrders(array $orders): array
    {
        $list = [];
        foreach ($orders as $index => $order) {
            $list[] = $this->formatToOrder($order);
        }
        return $list;
    }


    /**
     * @inheritDoc
     * @throws ClientException
     */
    protected function sendGetTradesOrders(int $startTime, int $endTime, bool $isFirstPull = false)
    {
        $this->hasNext = false;
        $client = $this->getClient();
        $params = [
            'create_time_start' => $startTime,
            'create_time_end' => $endTime,
            'size' => $this->pageSize,
            'order_by' => 'create_time',
            'order_asc' => false, //  默认 false 降序（大到小），排序类型，小到大或大到小，
            'page' => $this->page,
            'after_sale_status_desc' => 'all'
        ];
        //第一次仅拉取待发货
        if ($isFirstPull) {
            $params['combine_status']['order_status'] = '2,101';
        }

        $response = $client->execute('order/searchList', $params);
        $shop = $this->getShop();
        $appid = \request('appId', -1);
        \Log::info('sendGetTradesOrders:' . $shop->id . ':' . $appid);
        $this->handleErrorCode($response);

        if (count((array)$response['data']['shop_order_list']) < $this->pageSize) {
            $this->hasNext = false;
        } else {
            $this->hasNext = true;
        }
        $this->setOrderTotalCount($response['data']['total'] ?? -1);

        return (array)$response['data']['shop_order_list'];
    }

    /**
     * 订单详情
     * @param string $tid
     * @return mixed
     * @throws ClientException
     */
    protected function sendGetOrderInfo(string $tid)
    {
        $client = $this->getClient();
        $params = [
            'shop_order_id' => $tid,
        ];
        $response = $client->execute('order/orderDetail', $params);
        Log::info('sendGetOrderInfo response:'.json_encode($response));

        //用户取消授权
//        if (isset($response['code']) && in_array($response['code'], array_keys($this->errorCodeMap))) {
//            $this->errorInfo['code']  = array_get($this->errorCodeMap, $response['code'], 0);
//            $this->errorInfo['info']  = $response;
//            return $this->errorInfo;
//        }
        $this->handleErrorCode($response);

        return $response['data']['shop_order_detail'];
    }

    protected function sendGetAfterSaleOrderInfo(string $tid)
    {
        $client = $this->getClient();
        $params = [
            'order_id' => $tid,
        ];
        $response = $client->execute('afterSale/refundProcessDetail', $params);

        return $response['data'];
    }

    /**
     * 获取商品列表
     * @param int int $pageSize,
     * @param int $currentPage
     * @return array
     * @throws ClientException
     */
    protected function sendGetGoods(int $pageSize, int $currentPage = 1)
    {
        $this->hasNext = false;
        $client = $this->getClient();
        $params = [
            'page' => $currentPage,
            'size' => $pageSize,
        ];
        $goods = $client->execute('product/listV2', $params);
        DyClient::handleErrorCode($goods);
        if (isset($goods['data']['total'])) {
            $this->goodsTotalCount = $goods['data']['total'];
        } else {
            $this->goodsTotalCount = 0;
        }
        if (count($goods['data']['data']) == $goods['data']['size']) {
            $this->hasNext = true;
        }
        $paramsArr = [];
        foreach ((array)$goods['data']['data'] as $index => $datum) {
            $params = [
                'product_id' => $datum['product_id']
            ];
//            $data = $client->execute('product/detail', $params);
            $paramsArr[] = $params;
//            $arr[] = $data['data'];
        }
        $arr = $client->executeAsync('product/detail', $paramsArr);
        return $arr;
    }

    /**
     * 订单发货
     * @param string $tid
     * @param string $expressCode
     * @param string $expressNo
     * @param array $orderItemOId
     * @param bool $silent
     * @return bool|mixed
     * @throws ApiException
     * @throws ClientException
     * @throws OrderException
     */
    protected function deliverySellerOrders(string $tid, string $expressCode, string $expressNo, array $orderItemOId = [],bool  $silent=true)
    {
        $client = $this->getClient();
        $this->filterTid($tid);
        if (empty($orderItemOId)) {
            $params = [
                'order_id' => $tid,
//                'logistics_id' => $this->expressCodeList[$expressCode],
                'company_code' => $this->expressCodeMap[$expressCode] ?? $expressCode,
                'logistics_code' => $expressNo,
            ];
            $result = $client->execute('order/logisticsAdd', $params);
        } else {
            $shippedOrderInfo = [];
            $orderTIdArr = OrderItem::query()->whereIn('oid', $orderItemOId)->get()->toArray();
            $goodsNumArr = array_column($orderTIdArr, 'goods_num', 'oid');
            foreach ($orderItemOId as $key => $item) {
                $shippedOrderInfo[] = [
                    'shipped_order_id' => $item,
                    'shipped_num' => (int)$goodsNumArr[$item],
                ];
            }
            $tidArr = array_unique(array_column($orderTIdArr, 'tid'));
            $this->filterTid($tidArr);
            $params = [
                'order_id_list' => json_encode($tidArr),
                'shipped_order_info' => json_encode($shippedOrderInfo),
//                'logistics_id' => (string)$this->expressCodeList[$expressCode],
                'company_code' => $this->expressCodeMap[$expressCode] ?? $expressCode,
                'logistics_code' => $expressNo,
                'request_id' => (string)time()
            ];

            $result = $client->execute('order/logisticsAddSinglePack', $params);
            if (empty($result['data']['pack_id'])) {
                \Log::info('dy_delivery_seller_orders   params:' . json_encode($params) . ' dy_result:' . json_encode($result));
                return false;
            }
        }
        \Log::info('dy_delivery_seller_orders   params:' . json_encode($params) . ' dy_result:' . json_encode($result));

        if (isset($result['code']) && $result['code'] == 10000) {
            return true;
        }
        $this->handleErrorCode($result);

        return false;
    }

    /**
     * 订单发货
     * @param string $tid
     * @param string $expressCode
     * @param string $expressNo
     * @return bool|mixed
     * @throws ClientException
     */
    protected function deliverySellerOrdersForOpenApi(string $tid, string $expressCode, string $expressNo, array $orderItemOId = [])
    {
        $client = $this->getClient();
        $this->filterTid($tid);
        if (empty($orderItemOId)) {
            $params = [
                'order_id' => $tid,
//                'logistics_id' => $this->expressCodeList[$expressCode],
                'company_code' => $this->expressCodeMap[$expressCode] ?? $expressCode,
                'logistics_code' => $expressNo,
            ];
            $result = $client->execute('order/logisticsAdd', $params);
        } else {
            $shippedOrderInfo = [];
            $orderTIdArr = OrderItem::query()->whereIn('oid', $orderItemOId)->get()->toArray();
            $goodsNumArr = array_column($orderTIdArr, 'goods_num', 'oid');
            foreach ($orderItemOId as $key => $item) {
                $shippedOrderInfo[] = [
                    'shipped_order_id' => $item,
                    'shipped_num' => (int)$goodsNumArr[$item],
                ];
            }

            $tidArr = array_unique(array_column($orderTIdArr, 'tid'));
            $this->filterTid($tidArr);
            $params = [
                'order_id_list' => json_encode($tidArr),
                'shipped_order_info' => json_encode($shippedOrderInfo),
//                'logistics_id' => (string)$this->expressCodeList[$expressCode],
                'company_code' => $this->expressCodeMap[$expressCode] ?? $expressCode,
                'logistics_code' => $expressNo,
                'request_id' => (string)time()
            ];

            $result = $client->execute('order/logisticsAddSinglePack', $params);
            if (empty($result['data']['pack_id'])) {
                \Log::info('dy_delivery_seller_orders_for_open_api   params:' . json_encode($params) . ' dy_result:' . json_encode($result));
                return false;
            }
        }
        \Log::info('dy_delivery_seller_orders_for_open_api   params:' . json_encode($params) . ' dy_result:' . json_encode($result));

        $this->handleErrorCode($result);
        if ($result['code'] != 10000) {
            return $result['sub_msg'] ?? false;
        }

        return true;
    }

    /**
     * @inheritDoc
     * @param int $startTime
     * @param int $endTime
     * @param bool $isFirstPull
     * @return array
     * @throws ApiException
     * @throws ClientException
     * @throws OrderException
     */
    protected function sendGetTradesOrdersByIncr(int $startTime, int $endTime, bool $isFirstPull = false): array
    {
        $this->hasNext = false;
        $client = $this->getClient();
        $params = [
            'update_time_start' => $startTime,
            'update_time_end' => $endTime,
            'size' => $this->pageSize,
            'order_by' => 'update_time',
            'order_asc' => false, // 排序类型，小到大或大到小，默认大到小
            'page' => $this->page,
            'after_sale_status_desc' => 'all'
        ];
        //第一次仅拉取待发货
        if ($isFirstPull) {
            $params['combine_status']['order_status'] = '2,101';
        }

        $response = $client->execute('order/searchList', $params);
        $shop = $this->getShop();
        $appid = \request('appId', -1);
        \Log::info('sendGetTradesOrdersByIncr:' . $shop->id . ':' . $appid);
        $this->handleErrorCode($response);
        if (!isset($response['data'])) {
            return [];
        }

        if (count((array)$response['data']['shop_order_list']) < $this->pageSize) {
            $this->hasNext = false;
        } else {
            $this->hasNext = true;
        }
        $this->setOrderTotalCount($response['data']['total'] ?? -1);

        return (array)$response['data']['shop_order_list'];
    }

    /**
     * @param bool $freshShop
     * @return DyClient
     * @throws ApiException
     * <AUTHOR>
     */
    protected function getClient($freshShop = true): DyClient
    {
        $appKey = config('socialite.dy.client_id');
        $secretKey = config('socialite.dy.client_secret');
        $dyClient = (new DyClient($appKey, $secretKey))->setAccessToken($this->getAccessToken());
        if ($freshShop) {
            $dyClient->setShopIdByShop($this->getShop());
        }
        return $dyClient;
    }

    protected function sendGetOrderTraceList(array $order)
    {
        // TODO: Implement sendGetOrderTraceList() method.
    }

    /**
     * @see https://op.jinritemai.com/docs/api-docs/158/1402
     * @inheritDoc
     */
    public function openSubscribeMsg():bool
    {
        $shop = $this->getShop();
        $client = $this->getClient();
        $push_db_id = config('socialite.dy.push_db_id');
        if (empty($push_db_id)) {
            Log::error('push_db_id empty!');
            return false;
        }
        $params = [
            'shop_id' => $shop->identifier,
            'rds_instance_id' => $push_db_id,
            'history_days' => 7,
        ];
        $response = $client->execute('openCloud/ddpAddShop', $params);
        Log::info('openSubscribeMsg', compact('params', 'response'));
        if (isset($response['code']) && $response['code'] != 10000) {
            return false;
        }
        return true;
    }

    /**
     * @inheritDoc
     */
    public function consumeSubscribeMsg()
    {
        // TODO: Implement consumeSubscribeMsg() method.
    }

    /**
     * @inheritDoc
     */
    public function confirmSubscribeMsg(array $idArr)
    {
        // TODO: Implement confirmSubscribeMsg() method.
    }

    /**
     * @inheritDoc
     */
    public function handleOrderMsg($data)
    {
        // TODO: Implement handleOrderMsg() method.
    }

    /**
     * @inheritDoc
     */
    public function createWebsocketServer()
    {
        // TODO: Implement createWebsocketServer() method.
    }

    /**
     * @inheritDoc
     */
    public function sendMarketOrderDetail($orderId)
    {
        // TODO: Implement createWebsocketServer() method.
    }

    /**
     * @inheritDoc
     */
    public function sendPlatformOrder($startTime, $endTime)
    {
        // TODO: Implement createWebsocketServer() method.
    }

    public function pageTurning()
    {
        $this->page++;
    }


    /**
     * @inheritDoc
     */
    public function sendServiceInfo(): array
    {
        $shop = $this->getShop();
        $client = $this->getClient();
        $params = [];
        $response = $client->execute('rights/info', $params);
        $result = [];

        if (isset($response['code']) && $response['code'] == 10000) {
//            $version = $versionMap[$response['data']['spec_val']] ?? UserExtra::VERSION_FREE;
            $version = UserExtra::getVersionValueByName($response['data']['spec_val']);
//            $version = $response['data']['spec_val'] == "专业版" ? UserExtra::VERSION_PROFESSIONAL  :
//                ($response['data']['spec_val'] == "标准版" ? UserExtra::VERSION_STANDARD : UserExtra::VERSION_FREE);
            $result = [
                'user_id' => $shop->user_id,
                'shop_id' => $shop->id,
                'identifier' => $shop->identifier,
                'platform_type' => $this->platformType,
                'expired_at' => $response['data']['expire_time'], //暂定免费版使用30天
                'version' => $version,
                'version_desc' => array_get(UserExtra::VERSION_MAP_ARR, $version, ''),
                'pay_amount' => 0,
            ];
        }

        return $result;
    }

    /**
     * @see https://op.jinritemai.com/docs/api-docs/17/1295
     * <AUTHOR>
     * @param int $startTime
     * @param int $endTime
     * @return array|mixed
     * @throws ClientException
     */
    protected function sendRefundOrders(int $startTime, int $endTime)
    {
        $this->hasNext = false;
        $client = $this->getClient();
        $params = [
            'start_time' => $startTime,
            'end_time' => $endTime,
//            'type' => 5, // 5全部
            'size' => $this->pageSize,
            'order_by' => ['update_time desc'],
//            'is_desc' => 0, // 按order_by指定的时间方式排序, 设置了此字段即为desc (最近的在前), 不设置默认asc
            'page' => $this->page,
        ];

        $response = $client->execute('afterSale/List', $params);
        //用户取消授权
        if (isset($response['code']) && in_array($response['code'], array_keys($this->errorCodeMap))) {
            $this->errorInfo['code'] = array_get($this->errorCodeMap, $response['code'], 0);
            $this->errorInfo['info'] = $response;
            return $this->errorInfo;
        }

//        if (count((array)$response['data']['aftersale_list']) < $this->pageSize) {
        if ($response['data']['has_more'] != 'true') {
            $this->hasNext = false;
        } else {
            $this->hasNext = true;
        }

        return (array)$response['data']['items'];
    }

    /**
     * @inheritDoc
     */
    protected function formatRefundOrder($order)
    {
        // 0(退货退款),1(已发货仅退款),2(未发货仅退款),3(换货),6(价保),7(补寄)
        $afterSaleStatusMap = [
            0 => Order::AFTERSALE_TYPE_REFUND,
            1 => Order::AFTERSALE_TYPE_ONLY_REFUND,
            2 => Order::AFTERSALE_TYPE_NOT_DELIVERED_ONLY_REFUND,
            3 => Order::AFTERSALE_TYPE_EXCHANGE,
            6 => Order::AFTERSALE_TYPE_OTHER,
            7 => Order::AFTERSALE_TYPE_RESEND,
        ];
        $oid = (string)$order['order_info']['related_order_info'][0]['sku_order_id'];
        return [
            'tid' => (string)$order['order_info']['shop_order_id'] ?? $oid,
            'oid' => $oid,
            'refund_id' => $order['aftersale_info']['aftersale_id'],
//            'express_no' => $order['tracking_number'],
            'refund_created_at' => $order['aftersale_info']['apply_time'],
            //'refund_updated_at' => date('Y-m-d H:i:s', $order['update_time']),
            'refund_status' => $this->formatRefundStatus($order['aftersale_info']['aftersale_status']),
            'aftersale_type' => $afterSaleStatusMap[$order['aftersale_info']['aftersale_type']], // 售后类型
        ];
    }

    public function formatToGoods(array $goods): array
    {
        $list = [];
        foreach ($goods as $index => $good) {
            $list[] = $this->formatToGood($good);
        }

        return $list;
    }

    /**
     * 商品构建
     * @param array $goods
     * @return array
     */
    public function formatToGood(array $goods): array
    {
        $skus = [];
        $specValues = [];
        if (is_array($goods['specs'])) {
            foreach ($goods['specs'] as $index => $item) {
                foreach ($item['values'] as $value) {
                    $specValues[$value['id']] = [
                        'spec_name' => $item['name'], //规格名称：尺寸，颜色
                        'value' => $value,            //规格值：array name 黄 ，XL
                    ];
                }
            }
        }
        foreach ($goods['spec_prices'] as $index => $item) {
            $specIds = array_get($item, 'spec_detail_ids', []);
            $skuValue = '';
            foreach ($specIds as $specId) {
                //带规格名：尺寸：XL;颜色:红色;
//				$skuValue  .= $specValues[$specId]['spec_name'] . ':'. $specValues[$specId]['value']['name'] . ';';
                //不带规格名：XL;红色;
                $skuValue .= $specValues[$specId]['value']['name'] . ';';
            }

            $skus[] = [
                "type" => $this->platformType,
                "sku_id" => $item['sku_id'],
                "sku_value" => $skuValue,
                "outer_id" => $item['out_sku_id'],
                "outer_goods_id" => $goods['out_product_id'] ? $goods['out_product_id'] : '',
                "sku_pic" => $item['sku_pic'] ?? null,
                "is_onsale" => 0,
            ];
        }

        $onsaleArr = [Goods::IS_ONSALE_YES, Goods::IS_ONSALE_NO, Goods::IS_ONSALE_NO];
        return [
            "type" => $this->platformType,
            'num_iid' => $goods['product_id'],
            'outer_goods_id' => $goods['out_product_id'] ? $goods['out_product_id'] : '',
            'goods_title' => $goods['name'],
            'goods_pic' => $goods['img'],
            'is_onsale' => $onsaleArr[$goods['status']],
            'goods_created_at' => $goods['create_time'],
            'goods_updated_at' => $goods['update_time'],
            'skus' => $skus
        ];
    }

    /**
     * 转换订单状态
     * @param $status
     * @return int
     * <AUTHOR>
     */
    public function formatOrderStatus($status): int
    {
        if (!isset($this->orderStatusMap[$status])) {
            return Order::ORDER_STATUS_CLOSE;
        }
        return $this->orderStatusMap[$status];
    }

    /**
     * 转换退款状态
     * 只需要定义没有退款状态
     * @param $status
     * @return int
     * <AUTHOR>
     */
    public function hasRefundStatus($status): int
    {
        if (empty($this->refundStatusMap)) {
            throw new InvalidArgumentException('未定义退款状态！');
        }
        if (isset($this->refundStatusMap[$status])) {
            return $this->refundStatusMap[$status];
        }
        return Order::REFUND_STATUS_NO;
    }

    public function formatSubRefundStatus($itemData)
    {
        // after_sale_status 售后状态；6-售后申请；27-拒绝售后申请；12-售后成功；7-售后退货中；11-售后已发货；29-售后退货拒绝；13-【换货返回：换货售后换货商家发货】，【补寄返回：补寄待用户收货】； 14-【换货返回：（换货）售后换货用户收货】，【补寄返回：（补寄）用户已收货】 ；28-售后失败；51-订单取消成功；53-逆向交易已完成；
        // after_sale_type 售后类型 ；0-退货退款;1-已发货仅退款;2-未发货仅退款;3-换货;4-系统取消;5-用户取消;6-价保;7-补寄;
        // refund_status 退款状态:1-待退款；3-退款成功； 4-退款失败；当买家发起售后后又主动取消售后，此时after_sale_status=28并且refund_status=1的状态不变，不会流转至4状态；
        $after_sale_status = $itemData['after_sale_info']['after_sale_status'];
        $after_sale_type = $itemData['after_sale_info']['after_sale_type'];
        $refund_status = $itemData['after_sale_info']['refund_status'];
        if ($refund_status == 3){ // 退款成功
            return RefundSubStatusConst::REFUND_COMPLETE;
        }
        switch ($after_sale_status){
            case 6: // 售后申请
                return RefundSubStatusConst::MERCHANT_PROCESSING;
                break;
            case 27: // 拒绝售后申请
                if ($after_sale_type == 0){ // 退货退款
                    return RefundSubStatusConst::MERCHANT_REFUSE_RETURN;
                }else if ($after_sale_type == 3){ // 换货
                    return RefundSubStatusConst::MERCHANT_REFUSE_EXCHANGE;
                }else { //else if ($after_sale_type == 1 || $after_sale_type == 2){ // 已发货仅退款 未发货仅退款
                    return RefundSubStatusConst::MERCHANT_REFUSE_REFUND;
                }
                break;
            case 12: // 售后成功
                if ($after_sale_type == 0){ // 退货退款
                    return RefundSubStatusConst::RETURN_REFUND_COMPLETE;
                }else if ($after_sale_type == 3){ // 换货
                    return RefundSubStatusConst::EXCHANGE_COMPLETE;
                }else { //else if ($after_sale_type == 1 || $after_sale_type == 2){ // 已发货仅退款 未发货仅退款
                    return RefundSubStatusConst::REFUND_COMPLETE;
                }
                break;
            case 7: // 售后退货中
                return RefundSubStatusConst::RETURN_WAIT_MERCHANT;
                break;
            case 11: // 售后已发货
                return RefundSubStatusConst::RETURN_WAIT_MERCHANT;
                break;
            case 29: // 售后退货拒绝
                return RefundSubStatusConst::MERCHANT_REFUSE_RETURN;
                break;
            case 13: // 【换货返回：换货售后换货商家发货】，【补寄返回：补寄待用户收货】
                return RefundSubStatusConst::WAIT_MERCHANT_EXCHANGE;
                break;
            case 14: // 【换货返回：（换货）售后换货用户收货】，【补寄返回：（补寄）用户已收货】
                return RefundSubStatusConst::WAIT_BUYER_EXCHANGE_RECEIVE;
                break;
            case 28: // 售后失败
                if ($after_sale_type == 0){ // 退货退款
                    return RefundSubStatusConst::RETURN_REFUND_CLOSE;
                }else if ($after_sale_type == 3){ // 换货
                    return RefundSubStatusConst::EXCHANGE_CLOSE;
                }else { //else if ($after_sale_type == 1 || $after_sale_type == 2){ // 已发货仅退款 未发货仅退款
                    return RefundSubStatusConst::REFUND_CLOSE;
                }
                break;
            case 51: // 订单取消成功
                return RefundSubStatusConst::REFUND_COMPLETE;
                break;
            case 53: // 逆向交易已完成
                return RefundSubStatusConst::REFUND_COMPLETE;
                break;

        }
        return 0;
    }

    /**
     * @return int|string
     */
    public function initPage()
    {
        $this->page = 0;
        $this->page_cursor = '';
    }

    /**
     * @inheritDoc
     */
    public function handleSubscribeMsg($data)
    {
        // TODO: Implement handleOrderMsg() method.
    }

    /**
     * @inheritDoc
     */
    public function createWebsocketConnection()
    {
        // TODO: Implement createWebsocketServer() method.
    }

    /**
     * @throws OrderException
     * @throws ApiException
     * @throws ClientException
     */
    public function sendEditSellerRemark($tid, $sellerFlag, $sellerMemo):bool
    {
        Log::info('dy_add_remark_result start');
        $client = $this->getClient(false);
        Log::info('dy_add_remark_result start client');
        $params = [
            'order_id' => $tid,
            'remark' => $sellerMemo,
        ];
        // 旗标
        if (isset($sellerFlag)) {
            if(array_key_exists($sellerFlag,self::ORDER_FLAG_MAP_REVERT)){
                $star = [
                    'star' => array_flip($this->orderFlagMap)[$sellerFlag],
                    'is_add_star' => 'true'
                ];
                $params = array_merge($params, $star);
            }else{
                throw_error_code_exception(ErrorConst::PARAM_ERROR,null,$this->flagErrorMsg($sellerFlag));
            }


        }
        $response = $client->execute('order/addOrderRemark', $params);
        $this->handleErrorCode($response);
        Log::info('dy_add_remark_result', [$response]);
        if ($response['code'] == 10000) {
            return true;
        }

        return false;
    }

    /**
     * @throws ApiException
     */
    public function sendBatchEditSellerRemark($tidList, $sellerFlag, $sellerMemo): array
    {
        $failTids = [];
        $client = $this->getClient();
        $data = [];
        $url = 'order/addOrderRemark';
        foreach ($tidList as $tid) {
            $params = [
                'order_id' => $tid,
                'remark' => $sellerMemo,
            ];
            // 旗标
            if ($sellerFlag) {
                if (!array_key_exists($sellerFlag, self::ORDER_FLAG_MAP_REVERT)) {
                    $failTids[$tid] = $this->flagErrorMsg($sellerFlag);
                    //如果旗标不支持，返回null，然后从调用接口的时候过滤掉
                    continue;
                }

                $star = [
                    'star' =>  self::ORDER_FLAG_MAP_REVERT[$sellerFlag],
                    'is_add_star' => 'true'
                ];
                $params = array_merge($params, $star);
            }
            $data[] = [
                'tid' => $tid,
                'params' => $this->getRequestData($url, $params),
                'url' => $client->gatewayUrl . '/' . $url
            ];
        }

        $response = $this->poolCurl($data, 'POST');
        foreach ($response as $index => $rt) {
            $tid = $data[$index]['tid'];
            try {
                Log::info('dy_add_remark_result', ["index" => $data[$index], "rt" => $rt]);
                $this->handleErrorCode($rt);
            } catch (\Exception $ex) {
                Log::info("修改失败" . $ex->getMessage());
                $failTids[$tid] =$ex->getMessage();
            }
        }

        return $failTids;

    }

//    public function batchEditSellerRemark($tidList, $sellerFlag, $sellerMemo): array
//    {
//        $client = $this->getClient();
//        $data = $result = [];
//        $url = 'order/addOrderRemark';
//        foreach ($tidList as $tid) {
//            $params = [
//                'order_id' => $tid,
//                'remark' => $sellerMemo,
//            ];
//            // 旗标
//            if ($sellerFlag) {
//                $star = [
//                    'star' => array_flip($this->orderFlagMap)[$sellerFlag],
//                    'is_add_star' => 'true'
//                ];
//                $params = array_merge($params, $star);
//            }
//            $data[] = [
//                'tid' => $tid,
//                'params' => $this->getRequestData($url, $params),
//                'url' => $client->gatewayUrl . '/' . $url
//            ];
//        }
//
//        $response = $this->poolCurl($data, 'POST');
//        foreach ($response as $index => $rt) {
//            $tid = $data[$index]['tid'];
//            try {
//                \Log::info('dy_add_remark_result', ["index" => $data[$index], "rt" => $rt]);
//                $this->handleErrorCode($rt);
//            } catch (\Exception $ex) {
//                \Log::info("修改失败" . $ex->getMessage());
//                $result[] = $tid;
//            }
//        }
//
//        return $result;
//    }

    public function sendRefundOrderInfo($afterSaleId)
    {
        $this->hasNext = false;
        $client = $this->getClient();
        $params = [
            'aftersale_id' => $afterSaleId
        ];

        $response = $client->execute('afterSale/List', $params);
        //用户取消授权
        if (isset($response['code']) && in_array($response['code'], array_keys($this->errorCodeMap))) {
            $this->errorInfo['code'] = array_get($this->errorCodeMap, $response['code'], 0);
            $this->errorInfo['info'] = $response;
            return $this->errorInfo;
        }

        if ($response['data']['has_more'] != 'true') {
            $this->hasNext = false;
        } else {
            $this->hasNext = true;
        }

        return (array)$response['data']['aftersale_list'];
    }
    public function sendRefundOrderList($tid)
    {
        $this->hasNext = false;
        $client = $this->getClient();
        $params = [
            'order_id' => $tid,
            'page' => 0,
            'size' => 50,
        ];
        $response = $client->execute('afterSale/List', $params);
        $this->handleErrorCode($response);
        return (array)$response['data']['items'];
    }

    public function sendBatchGetOrderInfo($orders, $safe = false): array
    {
        $client = $this->getClient();
        //并行异步请求
        $data = $result = [];
        $url = 'order/orderDetail';
        foreach ($orders as $order) {
            $idStr = handleOrderIdStr($order);
//            $this->setAccessToken($order['shop']['access_token']);
            $params = ['shop_order_id' => $order['tid']];
            $data[$idStr] = [
                'params' => $this->getRequestData($url, $params),
                'url' => $client->gatewayUrl . '/' . $url
//                'url' => 'http://proxy-dy.mayiapps.cn/' . $url
            ];
        }

        $response = $this->poolCurl($data, 'POST', 5, $safe);
        foreach ($response as $orderId => $orderInfo) {
            $orderInfo = json_decode(json_encode($orderInfo), true);
            $trade = $orderInfo['data']['shop_order_detail'] ?? [];
            if (empty($trade)) {
                continue;
            }
            $result[$orderId] = $trade;
//            $result[$orderId] = $this->formatToOrder($trade);
        }

        return $result;
    }

    // 关联数组排序，递归
    public function rec_ksort(array &$arr) {
        $kstring = true;
        foreach ($arr as $k => &$v) {
            if (!is_string($k)) {
                $kstring = false;
            }
            if (is_array($v)) {
                $this->rec_ksort($v);
            }
        }
        if ($kstring) {
            ksort($arr);
        }
    }

    /**
     * 生成请求的数据
     * @param $apiMethod
     * @param array $apiParams
     * @return array
     */


    public function getRequestData($apiMethod, array $apiParams): array
    {
        $appKey = config('socialite.dy.client_id');
        $secretKey = config('socialite.dy.client_secret');
//        $apiParams = array_map(function ($item) {
//            return (string)$item;
//        }, $apiParams);

        $this->rec_ksort($apiParams);
        $method = str_replace('/', '.', $apiMethod);
        $timestamp = date('Y-m-d H:i:s', time());


        // 构造请求url
        $sign = $this->generateSign($method, $apiParams, $timestamp, $appKey, $secretKey);
        $request = [
            'app_key' => $appKey,
            'method' => $method,
            'access_token' => $this->getAccessToken(),
            'param_json' => json_encode($apiParams, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE),
            'timestamp' => $timestamp,
            'v' => '2',
            'sign' => $sign,
        ];
        return $request;
    }

    /**
     * 计算签名
     * @param $method
     * @param $params
     * @param $timestamp
     * @param $appKey
     * @param $secretKey
     * @return string
     */
    public function generateSign($method, $params, $timestamp, $appKey, $secretKey): string
    {
        ksort($params);
        $param_json = json_encode($params, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);

        // 计算签名
        $str = "app_key" . $appKey . "method" . $method . "param_json" . $param_json . "timestamp" . $timestamp . "v" . '2';
        $md5_str = $secretKey . $str . $secretKey;
        return md5($md5_str);
    }

    public function sendServiceOrderList($beginAt, $endAt)
    {
        // TODO: Implement sendServiceOrderList() method.
    }

    /**
     * @see https://op.jinritemai.com/docs/guide-docs/10/23
     * <AUTHOR>
     * @param $result
     * @throws ApiException
     * @throws OrderException
     */
    private function handleErrorCode($result)
    {
        DyClient::handleErrorCode($result);
    }

    /*
     * 批量数据解密
     * @see  https://open.pinduoduo.com/application/document/api?id=pdd.open.decrypt.batch
     * <AUTHOR>
     * @return mixed
     */
    public function cipherDecryptBatch()
    {
        // TODO: Implement cipherDecryptBatch() method.
    }

    /**
     * 批量数据解密脱敏
     * @see https://op.jinritemai.com/docs/api-docs/15/508
     * <AUTHOR>
     * @param array $list
     * @return array
     */
    public function cipherDecryptMaskBatch(array $list): array
    {
        $client = $this->getClient();
        $cipher_infos = [];
        foreach ($list as $item) {
            $cipher_infos[] = [
                'auth_id' => $item['tid'],
                'cipher_text' => $item['text'],
            ];
        }
        $response = $client->execute('order/batchSensitive', [
            'cipher_infos' => $cipher_infos,
        ]);
        $this->handleErrorCode($response);
        $returnData = [];
        foreach ($response['data']['decrypt_infos'] as $index => $item) {
            $returnData[] = [
                'tid' => $item['auth_id'],
                'text' => $item['decrypt_text'],
            ];
        }
        return $returnData;
    }

    /**
     * 剥离数据检索字符串
     * @param string $encryptedData
     * @return string
     * <AUTHOR>
     */
    public function cipherExtractSearch(string $encryptedData): string
    {
        if (empty($encryptedData)) {
            return '';
        }
        $separator = $encryptedData[0];
        $explodeArr = explode($separator, $encryptedData);
        return $explodeArr[1];
    }

    /**
     * 批量解密
     * @param $order
     * @return mixed
     */
    protected function sendDecrypt($params)
    {
        $client = $this->getClient();
        $cipher_infos[] = [
            'auth_id' => $params['tid'],
            'cipher_text' => $params['text'],
        ];
        $response = $client->execute('order/batchDecrypt', [
            'cipher_infos' => $cipher_infos,
        ]);
        \Log::info("Decrypt result", [$response]);
        $this->handleErrorCode($response);
        $returnData = [];
        $decryptInfos = $response['data']['decrypt_infos'][0] ?? [];
        if (isset($decryptInfos['err_no']) && $decryptInfos['err_no'] !== 0) {
            throw new OrderException('抖音平台错误：' . $decryptInfos['err_msg']);
        }

        if (!empty($decryptInfos)) {
            $returnData[] = [
                'tid' => $decryptInfos['auth_id'],
                'text' => $decryptInfos['decrypt_text'],
            ];
        }

        return $returnData;
    }

    public function getSearchIndex(string $text, string $type): string
    {
        $type = $type == 'name' ? 2 : ($type == 'phone' ? 3 : 1);
        if (empty($text) || ($type == '3' && !isPhoneNumber($text))) {
            return "";
        }
        $client = $this->getClient();
        $params = [
            'plain_text' => $text,
            'sensitive_type' => $type
        ];

        $response = $client->execute('order/getSearchIndex', $params);

        if (isset($response['code']) && $response['code'] == 10000) {
            return $response['data']['encrypt_index_text'] ?? "";
        } else {
            //智能查询姓名手机号都会查 不写错误日志了
            //\Log::info('抖音获取订单加密索引串 失败 response:',[$response]);
        }

        return "";
    }

    public function sendBatchEncrypt($list)
    {
        if (empty($list)) {
            return [];
        }
        $client = $this->getClient();
        $batch_encrypt_list = [];
        foreach ($list as $item) {
            $batch_encrypt_list[] = [
                'auth_id' => $item['tid'],
                'is_support_index' => true,
                'plain_text' => $item['text'],
                'sensitive_type' => $item['type']
            ];
        }
        $response = $client->execute('order/batchEncrypt', [
            'batch_encrypt_list' => $batch_encrypt_list,
        ]);
        $returnData = [];
        foreach ($response['data']['encrypt_infos'] as $index => $item) {
            $returnData[] = [
                'tid' => $item['auth_id'],
                'cipher_text' => $item['cipher_text'],
                'decrypt_text' => $item['decrypt_text'],
            ];
        }
        return $returnData;
    }

    /**
     * @inheritDoc
     */
    public function checkAuthStatus(): bool
    {
        $client = $this->getClient();


        try {
            $shop = $this->getShop();
            if($shop&&!$shop->isAuthOk()){
                return false;
            }
            if (isFactory()) {
                $result = $client->execute('iop/roleGet', []);
            } else {
                $params = [
                    'page' => 1,
                    'size' => 1,
                    'create_time_start' => time(),
                    'create_time_end' => time()-1,
                ];
                $result = $client->execute('order/searchList', $params);
            }
            $this->handleErrorCode($result);
        } catch (ApiException $e) {
            if (in_array($e->getCode(), ErrorConst::AUTH_ERRORS)) {
                return false;
            }
        } catch (\Exception $e) {

        }
        return true;

    }

    private function getShipTime($skuOrderList)
    {
        $skuShipTime = array_column($skuOrderList, 'ship_time');
        rsort($skuShipTime);

        return $skuShipTime[0];
    }

    /**
     * 风控 用户登录
     * @see https://op.jinritemai.com/docs/api-docs/13/635
     * @param $userId
     * <AUTHOR>
     * @return bool
     * @throws ClientException
     */
    public function checkAntispamUserLogin($userId): bool
    {
        if (isFactory()) {
            return true;
        }
        $client = $this->getClient();
        $data = [];
        $data['params'] = [
            'account_id' => $userId,
        ];
        $data['params'] = json_encode($data['params']);
        $data['event_time'] = time();
        $response = $client->execute('antispam/userLogin', $data);
        \Log::debug('antispamUserLogin:response', $response);
        if (isset($response['data']['decision']['decision']) && $response['data']['decision']['decision'] == 'PASS') {
            return true;
        }
        return false;
    }

    /**
     * 风控 查看订单、下载订单时
     * @see https://op.jinritemai.com/docs/api-docs/15/650
     * @param $userId
     * <AUTHOR>
     * @return bool
     * @throws ClientException
     */
    public function checkAntispamOrderQuery($userId): bool
    {
        if (isFactory()) {
            return true;
        }
        $client = $this->getClient();
        $data = [];
        $data['params'] = [
            'account_id' => $userId,
        ];
        $data['params'] = json_encode($data['params']);
        $data['event_time'] = time();
        $response = $client->execute('antispam/orderQuery', $data);
        \Log::debug('antispamOrderQuery:response', $response);
        if (isset($response['data']['decision']['decision']) && $response['data']['decision']['decision'] == 'PASS') {
            return true;
        }
        return false;
    }

    /**
     * 风控 发送（外部系统或模块，如快递平台）场景
     * @see https://op.jinritemai.com/docs/api-docs/15/649
     * @param $userId
     * <AUTHOR>
     * @return bool
     * @throws ClientException
     */
    public function checkAntispamOrderSend($data): bool
    {
        if (isFactory()) {
            return true;
        }
        $client = $this->getClient();
        $response = $client->execute('antispam/orderSend', $data);
        \Log::debug('antispamOrderSend:response', [$data, $response]);
        if (isset($response['data']['decision']['decision']) && $response['data']['decision']['decision'] == 'PASS') {
            return true;
        }
        return false;
    }

    /**
     * @inheritDoc
     * @see https://op.jinritemai.com/docs/api-docs/59/672
     * <AUTHOR>
     * @return int
     * @throws ClientException
     */
    public function sendFactoryShopRoleType(): int
    {
        $client = $this->getClient();
        $params = [];

        $response = $client->execute('iop/roleGet', $params);
        \Log::info('获取店铺身份 iop/roleGet:' . json_encode($response));
        if (isset($response['data']['role_type'])) {
            // 店铺身份：0，未知身份；1，商户；2，厂家
            return $response['data']['role_type'];
        }
        return Shop::ROLE_TYPE_UNKNOWN;
    }

    /**
     * @inheritDoc
     * @throws OrderException|ClientException|ApiException
     */
    public function getFactoryTradesOrder(int $startTime, int $endTime)
    {
        $this->hasNext = false;
        $client = $this->getClient();
        $params = [
            'start_update_time' => $startTime,
            'end_update_time' => $endTime,
            'page' => $this->page,
            'size' => $this->pageSize,
//            'distr_status' => '', // 分配状态 1-已分配未回传 2-已回传 3-已取消
        ];

        $response = $client->execute('iop/orderList', $params);
        if (isset($response['data']['order_list']) && count((array)$response['data']['order_list']) < $this->pageSize) {
            $this->hasNext = false;
        } else {
            $this->hasNext = true;
        }
        $this->handleErrorCode($response);
        return $this->formatToFactoryOrders($response['data']['order_list']);
    }

    private function formatToFactoryOrders($order_list): array
    {
        $shop = $this->getShop();
        $factoryOrders = [];
        if (empty($order_list)) {
            return $factoryOrders;
        }
        foreach ($order_list as $index => $item) {
            $supplierShop = Shop::query()->where('identifier', $item['user_id'])->first();
            $factoryOrders[] = [
                'shop_id' => $shop->id,
                'distr_tid' => $item['distr_order_id'],
                'distr_shop_id' => $item['user_id'],
                'distr_platform_shop_id' => $supplierShop->id,
                'distr_shop_name' => $item['user_name'],
                'seller_memo' => $item['seller_words'],
                'distr_status' => $this->distStatusMap[$item['distr_status']] ?? 0,
                'distr_cancel_reason' => $item['distr_cancel_reason'],
                'receiver_state' => $item['province'],
                'receiver_city' => $item['city'],
                'receiver_district' => $item['district'],
                'receiver_town' => $item['street'],
                'receiver_id' => $item['province_id'],
                'distr_at' => date('Y-m-d H:i:s', $item['distr_time']),
                'distr_oid' => $item['distr_order_id'],
                'goods_title' => $item['product_name'],
                'goods_num' => $item['product_count'],
                'goods_price' => $item['product_price'] / 100,
                'goods_total_price' => ($item['product_count'] * $item['product_price']) / 100,
                'goods_id' => $item['product_id'],
                'sku_id' => '',
                'sku_value' => $item['sku_spec'],
                'outer_goods_id' => '',
                'outer_sku_id' => $item['out_sku_id'],
                'promise_ship_at' => !empty($item['exp_ship_time']) ? date('Y-m-d H:i:s', $item['exp_ship_time']) : null,
                'aftersale_status' => $item['aftersale_status'] ?? $item['distr_order_aftersale_status'],
                'buyer_message' => $item['buyer_words']
            ];
        }
        return $factoryOrders;
    }

    /**
     * @param array|string $tid
     * @return string
     * <AUTHOR>
     */
    private function filterTid(&$tid)
    {
        if (is_string($tid) || is_numeric($tid)) {
            $tid = str_replace('A', '', $tid);
            return $tid;
        }
        $tid = array_map(function ($item) {
            return str_replace('A', '', $item);
        }, $tid);
    }

    /**
     * 重新订单发货
     * @param OrderDeliverAgainRequest $orderDeliverAgainRequest
     * @return bool
     * @throws ApiException
     * @throws ClientException
     * @throws OrderException
     */
    protected function deliverySellerOrdersAgain(OrderDeliverAgainRequest $orderDeliverAgainRequest): bool
    {
        $client = $this->getClient();
        $tid = $this->filterTid($orderDeliverAgainRequest->tid);
        $expressCode = $orderDeliverAgainRequest->wpCode;
        $expressNo = $orderDeliverAgainRequest->waybillCode;
        // 是否拆单和是否合单
        if ($orderDeliverAgainRequest->isSplit || $orderDeliverAgainRequest->isMerge || !empty($orderDeliverAgainRequest->deliveryId)) {
            $pack_id = $orderDeliverAgainRequest->deliveryId;
            $params = [
                'order_id' => $tid,
                'company_code' => $this->expressCodeMap[$expressCode] ?? $expressCode,
                'logistics_code' => $expressNo,
                'pack_id' => $pack_id,
            ];
            $result = $client->execute('order/logisticsEditByPack', $params);
        }else{ //整单发货修改
            $params = [
                'order_id' => $tid,
                'company_code' => $this->expressCodeMap[$expressCode] ?? $expressCode,
                'logistics_code' => $expressNo,
            ];
            $result = $client->execute('order/logisticsEdit', $params);
        }

        Log::info('dy_delivery_seller_orders_again   params:' . json_encode($params) . ' dy_result:' . json_encode($result));
        if (isset($result['code']) && $result['code'] == 10000) {
            return true;
        }
        $this->handleErrorCode($result);

        return false;
    }

    /**
     * @inheritDoc
     */
    public function batchGetFactoryOrderInfo($orders)
    {
        $client = $this->getClient();
        //并行异步请求
        $data = $result = [];
        $url = 'iop/orderInfo';
        foreach ($orders as $index => $order) {
//            $idStr     = handleOrderIdStr($order);
            $tid = $order['distr_oid'];
            $this->filterTid($tid);
            $params = [
                'user_id' => $order['distr_shop_id'],
                'distr_order_id' => $tid
            ];
            $data[$index] = [
                'params' => $this->getRequestData($url, $params),
                'url' => $client->gatewayUrl . '/' . $url
            ];
        }

        $response = $this->poolCurl($data, 'POST');
        foreach ($response as $orderId => $orderInfo) {
            $orderInfo = json_decode(json_encode($orderInfo), true);
            $trade = $orderInfo['data'] ?? [];
            if (empty($trade)) {
                continue;
            }
            $formatOrders = $this->formatToFactoryOrders([$trade]);
            $result[$orderId] = $formatOrders[0];
        }

        return $result;
    }

    /**
     * @inheritDoc
     */
    public function batchReturnFactoryOrder($orders)
    {
        $list = [];
        $msg_type = "iop/waybillReturn";
        foreach ($orders as $index => $order) {
            $shop = Shop::query()->where('id', $order['shop_id'])->first();
            $this->setShop($shop);
            $client = $this->getClient();
            $info = [
                'user_id' => $order['distr_shop_id'],
                'distr_order_id' => $order['distr_tid'],
                'company_code' => $order['wp_code'],
                'track_no' => $order['waybill_code'],
            ];
            $list[$order['waybill_code']] = [
                'params' => $client->buildRequestData($info, $msg_type),
                'url' => $client->getBaseUrlByApimethod($msg_type)
            ];
        }

        $order = array_pluck($orders, null, 'waybill_code');
        $responses = $this->poolCurl($list, 'POST');
        \Log::info('waybillReturn responses:' . json_encode($responses));
        $orderResponseBoList = [];
        foreach ($responses as $waybill_code => $response) {
            $response = json_decode(json_encode($response), true);
            $data = $response['data'] ?? [];
            $orderResponseBo = new OrderResponseBo();
            $orderResponseBo->order_info = $order[$waybill_code];
            if (is_array($response) && isset($response['code']) && $response['code'] == 10000) {
                if ($data['return_result'] != 'true') {
                    $orderResponseBo->setError(ErrorConst::ORDER_FACTORY_RETURN_ERROR);
                }
            } else {
                $error = [0, '抖音错误：' . $response];
                $orderResponseBo->setError($error);
            }
            $orderResponseBoList[] = $orderResponseBo;
        }
        return $orderResponseBoList;
    }

    /**
     * @inheritDoc
     */
    public function batchWaybillRecoveryFactoryOrder($waybills)
    {
        $list = [];
        $msg_type = "iop/waybillCancel";
        foreach ($waybills as $index => $waybill) {
            $shop = Shop::query()->where('id', $waybill['shop_id'])->first();
            $this->setShop($shop);
            $client = $this->getClient();
            $info = [
                'user_id' => $waybill['distr_shop_id'],
                'company_code' => $waybill['wp_code'],
                'track_no' => $waybill['waybill_code'],
            ];
            $list[$waybill['waybill_code']] = [
                'params' => $client->buildRequestData($info, $msg_type),
                'url' => $client->getBaseUrlByApimethod($msg_type)
            ];
        }

        $waybill = array_pluck($waybills, null, 'waybill_code');
        $responses = $this->poolCurl($list, 'POST');
        $waybillRecoveryResponseBoList = [];
        foreach ($responses as $waybill_code => $response) {
            $response = json_decode(json_encode($response), true);
            $data = $response['data'] ?? [];
            $waybillRecoveryResponseBo = new WaybillRecoveryResponseBo();
            $waybillRecoveryResponseBo->waybill_code = $waybill_code;
            $waybillRecoveryResponseBo->waybill_info = $waybill[$waybill_code];
            if (is_string($response)) {
                $error = [0, '抖音错误：' . $response];
                $waybillRecoveryResponseBo->setError($error);
            } else if ($response['err_no'] == 0) {
                if ($data['return_result'] != 'true') {
                    $waybillRecoveryResponseBo->setError(ErrorConst::ORDER_FACTORY_RECOVERY_ERROR);
                }
            } else {
                $error = [$response['err_no'], '抖音错误：' . $response['message']];
                $waybillRecoveryResponseBo->setError($error);
            }
            $waybillRecoveryResponseBoList[] = $waybillRecoveryResponseBo;
        }
        return $waybillRecoveryResponseBoList;
    }

    public function sendGetSellerList()
    {
        $this->hasNext = false;
        $client = $this->getClient();
        $params = [
            'page' => $this->page,
            'page_size' => $this->pageSize,
            'bind_status' => 2,//未知-0、申请绑定待审核-1、已通过-2、已拒绝-3、已解绑-4、申请解绑待审核-5
        ];
        $result = $client->execute('iop/getSellerList', $params);

        if ($result['err_no'] == 0 && isset($result['data']['seller_shop_list']) && !empty($result['data']['seller_shop_list'])) {
            $this->hasNext = true;
            return $result['data']['seller_shop_list'];
        }

        return [];
    }

    public function sendQueryTradeTid(array $query_list)
    {
        // TODO: Implement sendQueryTradeTid() method.
    }

    /**
     * @inheritDoc
     */
    public function sendAddress(): array
    {
        $client = $this->getClient();
        $params = [];
        $result = $client->execute('address/getProvince', $params);
        $this->handleErrorCode($result);
        $addressList = [];
        foreach ($result['data'] as $province) {
            $params = [
                'province_id' => $province['province_id'],
            ];
            $result2 = $client->execute('address/getAreasByProvince', $params);

            $provinceList = $this->formatAddress($result2['data']);
            $addressList[] = $provinceList[0];
        }
        return $addressList;
    }

    protected function formatAddress(array $list, $level = 1)
    {
        $resArr = [];
        foreach ($list as $item) {
//            if ($level == 4) {
//                continue;
//            }
            $parent_code = $item['father_code'];
            if ($level == 1) {
                $parent_code = 1;
            }
            $arr = [
                'name' => $item['name'],
                'code' => $item['code'],
                'parent_code' => $parent_code,
                'level' => $level,
            ];
            if (!empty($item['sub_districts']) && is_array($item['sub_districts'])) {
                $arr['sub'] = $this->formatAddress($item['sub_districts'], $level + 1);
            }
            $resArr[] = $arr;
        }
        return $resArr;
    }

    /**
     * @throws ApiException
     * @throws ClientException
     */
    public function getQueryTradeOrderId(string $type, string $search):array
    {
        // 查询类型：0-收货人手机号，1-收货人姓名
        $map = [
            'receiver_name' => 1,
            'receiver_phone' => 0,
        ];
        $searchType = $map[$type];
        if (empty($search)
            || ($type == 'receiver_phone' && !isPhoneNumber($search)) // 手机号，匹配不上手机号规则
            || ($type == 'receiver_name' && is_numeric($search))) { // 名字但是是数字
            return [];
        }
        $beginAt = \request()->input('begin_at', date('Y-m-d H:i:s',strtotime('-30 day')));
        $endAt = \request()->input('end_at', date('Y-m-d H:i:s'));
        $client = $this->getClient();
        $params = [
            'post_receiver' => $search,
            'post_type' => $searchType,
            'create_time_start' =>  strtotime($beginAt),
            'create_time_end' =>  strtotime($endAt),
            'size' => 100,
            'page' => 0,
        ];

        $response = $client->execute('order/searchByReceiver', $params);

//        $this->handleErrorCode($response);
        if (!empty($response['data']['shop_order_list'])){

            $shop = $this->getShop();

            $tidArr = collect($response['data']['shop_order_list'])->pluck('order_id')->map(function ($item){
                return $item;
            })->toArray();
            return Order::query()
                ->select('id')
                ->where('shop_id', $shop->id)
                ->whereIn('tid',$tidArr)
                ->get()
                ->pluck('id')
                ->toArray();
        }

        return [];
    }

    /**
     * @inheritDoc
     */
    public function batchDeliveryOrders(array $orderDeliveryRequests): array
    {
        $client = $this->getClient();
        $requestData = [];
        foreach ($orderDeliveryRequests as $index => $orderDeliveryRequest) {
            $tid = $orderDeliveryRequest->tid;
            $this->filterTid($tid);
            $expressCode = $orderDeliveryRequest->expressCode;
            $expressNo = $orderDeliveryRequest->expressNo;
            $orderItemOId = $orderDeliveryRequest->orderItemOId;
            if (empty($orderItemOId)) {
                $params = [
                    'order_id' => $tid,
                    //                'logistics_id' => $this->expressCodeList[$expressCode],
                    'company_code' => $this->expressCodeMap[$expressCode] ?? $expressCode,
                    'logistics_code' => $expressNo,
                ];
                $url = 'order/logisticsAdd';
                $requestData[$index] = [
                    'params' => $this->getRequestData($url, $params),
                    'url' => $client->gatewayUrl . '/' . $url
                ];
            } else {
                $shippedOrderInfo = [];
                $orderTIdArr = OrderItem::query()->whereIn('oid', $orderItemOId)->get()->toArray();
                $goodsNumArr = array_column($orderTIdArr, 'goods_num', 'oid');
                foreach ($orderItemOId as $key => $item2) {
                    $shippedOrderInfo[] = [
                        'shipped_order_id' => $item2,
                        'shipped_num' => (int)$goodsNumArr[$item2],
                    ];
                }
                $tidArr = array_unique(array_column($orderTIdArr, 'tid'));
                $this->filterTid($tidArr);
                $params = [
                    'order_id_list' => json_encode($tidArr),
                    'shipped_order_info' => json_encode($shippedOrderInfo),
//                'logistics_id' => (string)$this->expressCodeList[$expressCode],
                    'company_code' => $this->expressCodeMap[$expressCode] ?? $expressCode,
                    'logistics_code' => $expressNo,
                    'request_id' => (string)time()
                ];
                $url = 'order/logisticsAddSinglePack';
                $requestData[$index] = [
                    'params' => $this->getRequestData($url, $params),
                    'url' => $client->gatewayUrl . '/' . $url
                ];
            }
        }
        Log::info('poolCurl');
        $this->poolCurlAbnormalOutputOriginalData = true;
        $responses = $this->poolCurl($requestData, 'POST');
//        Log::info('batchDeliveryOrders poolCurl',[$requestData,$responses]);
        Log::info('poolCurl done');
        $results = [];
        foreach ($responses as $index => $result) {
            $commonResponse = new CommonResponse();
            try {
                $orderDeliveryRequest = $orderDeliveryRequests[$index];
                $commonResponse->setRequest($orderDeliveryRequest);
                $commonResponse->setRequestId($orderDeliveryRequest->getRequestId());
                $result = json_decode(json_encode($result), true);
                $this->handleErrorCode($result);
                $commonResponse->setSuccess(true);
            } catch (\Throwable $e) {
                $commonResponse->setSuccess(false);
                $commonResponse->setCode($e->getCode());
                $commonResponse->setMessage($e->getMessage());
            } finally {
                $results[] = $commonResponse;
            }
        }
        return $results;
    }

    public function setPage($page): void
    {
        parent::setPage($page - 1);
    }

    /**
     * 检查订单是否要质检
     * @return array
     * <AUTHOR>
     */
    public function checkOrderQualityInspection($orders)
    {
        $client = $this->getClient();
        $url = 'btas/getInspectionOrder';
        $requestData = [];
        foreach ($orders as $index => $order) {
            $tid = $order->tid;
            $this->filterTid($tid);
            $params = [
                'order_id' => $tid,
            ];
            $requestData[] = [
                'params' => $this->getRequestData($url, $params),
                'url' => $client->gatewayUrl . '/' . $url
            ];
        }
        $ordersGroup = array_pluck($orders, null, 'tid');
        $this->poolCurlAbnormalOutputOriginalData = true;
        $responses = $this->poolCurl($requestData, 'POST');
        $results = [];

        foreach ($responses as $index => $response) {
            $commonResponse = new CommonResponse();
            try {
                $response = json_decode(json_encode($response), true);
                $requestOrder = $orders[$index];
                $commonResponse->setRequestId($requestOrder['tid']);
                $this->handleErrorCode($response);
                if (empty($response['data']['order_id']) || empty($ordersGroup[$response['data']['order_id']])) {
                    throw new ApiException(ErrorConst::PLATFORM_RETURN_EMPTY);
                }
                $order = $ordersGroup[$response['data']['order_id']];
                $orderItems = [];
                foreach ($response['data']['product_orders'] as $item) {
                    $orderItems[] = [
                        'oid' => $item['product_order_id'],
                        'order_item_extras' => [
                            'quality_order_code' => jsonEncode($item['order_code']),
                            'quality_status' => OrderItemExtra::QUALITY_STATUS_NOT_CHECKED
                        ],
                    ];
                }
                $data = [
                    'tid' => $order['tid'],
                    'items' => $orderItems
                ];
                $commonResponse->setData($data);
                $commonResponse->setSuccess(true);
            } catch (\Exception $e) {
                $commonResponse->setSuccess(false);
                $commonResponse->setCode($e->getCode());
                $commonResponse->setMessage($e->getMessage());
            } finally {
                $results[] = $commonResponse;
            }
        }
        return $results;
    }

    /**
     * 获取质检结果
     */
    public function getQualityInspectionResult($orders)
    {
        $client = $this->getClient();
        $url = 'btas/getOrderInspectionResult';
        $requestData = [];
        $originalDataArr = [];
        foreach ($orders as $index => $order) {
            $tid = $order->tid;
            $this->filterTid($tid);
            foreach ($order['orderItem'] as $item) {
                $qualityOrderCodeArr = json_decode($item['orderItemExtra']['quality_order_code'], true);
                if (is_array($qualityOrderCodeArr) && !empty($qualityOrderCodeArr)) {
                    foreach ($qualityOrderCodeArr as $quality_order_code_item) {
                        $params = [
                            'order_code' => $quality_order_code_item,
                        ];
                        $requestData[] = [
                            'params' => $this->getRequestData($url, $params),
                            'url' => $client->gatewayUrl . '/' . $url
                        ];
                        $originalDataArr[$item['oid']][$quality_order_code_item] = [
                            'tid' => $order->tid,
                            'oid' => $item['oid'],
                            'order_code' => $quality_order_code_item,
                        ];
                    }
                }

            }
        }

//        $originalDataGroup = collect($originalData)->groupBy('oid')->toArray();
        $this->poolCurlAbnormalOutputOriginalData = true;
        $responses = $this->poolCurl($requestData, 'POST');
        \Log::debug('getQualityInspectionResult', [$responses]);
        $responseByOrderCodeArr = collect($responses)->keyBy('data.order_code')->toArray();
        $results = [];
        foreach ($originalDataArr as $index => $originalData) {
            $commonResponse = new CommonResponse();
            try {
                $successCounter = 0;
                $failureCounter = 0;
                $oid = null;
                foreach ($originalData as $originalDatum) {
                    $oid = $originalDatum['oid'];
                    $commonResponse->setRequestId($oid);
                    $response = $responseByOrderCodeArr[$originalDatum['order_code']] ?? [];
                    $response = json_decode(json_encode($response), true);
                    if (empty($response)) {
                        continue;
                    }
                    // 鉴定结果 1成功
                    if ($response['data']['inspection_result'] == 1) {
                        $successCounter++;
                    } else {
                        $failureCounter++;
                    }
                }

                if ($successCounter == count($originalData)) {
                    $qualityStatus = OrderItemExtra::QUALITY_STATUS_CHECKED_PASS;
                } elseif ($failureCounter > 0 && $successCounter > 0) {
                    $qualityStatus = OrderItemExtra::QUALITY_STATUS_CHECKED_PARTIAL_FAIL;
                } elseif ($failureCounter > 0 && $successCounter == 0) {
                    $qualityStatus = OrderItemExtra::QUALITY_STATUS_CHECKED_FAIL;
                } elseif ($failureCounter == 0 && $successCounter == 0) {
                    $qualityStatus = OrderItemExtra::QUALITY_STATUS_NOT_CHECKED;
                } else {
                    $qualityStatus = OrderItemExtra::QUALITY_STATUS_CHECKED_PARTIAL_PASS;
                }

                $data = [
                    'oid' => $oid,
                    'order_item_extras' => [
                        'quality_status' => $qualityStatus,
                    ],
                ];
                $commonResponse->setData($data);
                $commonResponse->setSuccess(true);
            } catch (\Exception $e) {
                $commonResponse->setSuccess(false);
                $commonResponse->setCode($e->getCode());
                $commonResponse->setMessage($e->getMessage());
            } finally {
                $results[] = $commonResponse;
            }
        }
        return $results;
    }

    /**
     * @inheritDoc
     * @see https://op.jinritemai.com/docs/api-docs/49/489
     */
    public function deliveryQualityInspection(array $orderDeliveryRequests)
    {
        $client = $this->getClient();
        $url = 'btas/shipping';
        $requestData = [];
        $originalData = [];
        $counter = 0;
        foreach ($orderDeliveryRequests as $index => $orderDeliveryRequest) {
            $tid = $orderDeliveryRequest->tid;
            $this->filterTid($tid);
            $expressCode = $orderDeliveryRequest->expressCode;
            $expressNo = $orderDeliveryRequest->expressNo;
            $orderInfo = $orderDeliveryRequest->order;
            $orderItemOIdArr = $orderDeliveryRequest->orderItemOId;
            foreach ($orderInfo['orderItem'] as $item) {
                if (!empty($orderItemOIdArr) && !in_array($item['oid'], $orderItemOIdArr)) {
                    continue;
                }
                $quality_order_code = json_decode($item['orderItemExtra']['quality_order_code'], true);
                if (is_array($quality_order_code)) {
                    foreach ($quality_order_code as $quality_order_code_item) {
                        $params = [
                            'order_id' => $tid,
                            'product_order_id' => $item['oid'],
                            'order_code' => $quality_order_code_item,
                            'service_status' => 1, // 枚举值： 1: 新增发货 2: 修改发货
                            'shipping_logistics_id' => $this->expressCodeToIdMap[$this->expressCodeMap[$expressCode]],
                            'shipping_logistics_code' => $expressNo,
//                            'shipping_logistics_company_code' => $this->expressCodeMap[$expressCode],
                        ];
                        $requestData[] = [
                            'params' => $this->getRequestData($url, $params),
                            'url' => $client->gatewayUrl . '/' . $url
                        ];
                        $originalData[$counter++] = $orderDeliveryRequest;
                    }
                }
            }
        }
        $this->poolCurlAbnormalOutputOriginalData = true;
        $responses = $this->poolCurl($requestData, 'POST');
        $results = [];
        foreach ($responses as $index => $result) {
            $commonResponse = new CommonResponse();
            try {
                $orderDeliveryRequest = $originalData[$index];
                $commonResponse->setRequest($orderDeliveryRequest);
                $this->handleErrorCode($result);
                $commonResponse->setSuccess(true);
            } catch (\Exception $e) {
                $commonResponse->setSuccess(false);
                $commonResponse->setCode($e->getCode());
                $commonResponse->setMessage($e->getMessage());
            } finally {
                $results[] = $commonResponse;
            }
        }
        \Log::debug('deliveryQualityInspection', [$requestData, $responses, $results]);
        return $results;
    }

    /**
     * 拉取平台退款审核单列表
     * @param $data
     * @return array
     */
    public function batchGetRefundApplyList($data)
    {
        // TODO: Implement batchGetRefundApplyList() method.
    }

    /**
     * @param ReportOrderSecurityEventRequest[] $list
     * <AUTHOR>
     */
    public function reportOrderSecurityEvent(array $list)
    {
        $params = [
            'event_type' => 1, // 订单事件类型 1:订单访问事件, 2:订单流出事件
            'events' => [],
        ];
        foreach ($list as $index => $request) {
            $orderIdChunk = array_chunk($request->orderIds, 50);
            foreach ($orderIdChunk as $orderIds) {
                $params['events'][] = [
                    'account_id' => $request->operationShopId,
                    'order_ids' => $orderIds,
                    'operation_type' => $request->operationType,
                    'operate_time' => $request->operationTime,
                    'url' => $request->url,
                    'ip' => $request->ip,
                    'identify_info_list' => $request->identifyInfoList,
                    'referer' => $request->referer,
                    'order_related_shop_id' => $request->orderRelatedShopId,
                    'account_type' => 'main_account',
                    'event_id' => $request->eventId,
                ];
            }

        }
        if (empty($params['events'])) {
            return true;
        }
        if (count($params['events']) > 50) {
            Log::info('reportOrderSecurityEvent:events > 50', [$params]);
            return false;
        }
//        Log::info('reportOrderSecurityEvent', [$params]);
        $client = $this->getClient();
        $result = $client->execute('security/batchReportOrderSecurityEvent', $params, 'post');
        Log::info('reportOrderSecurityEvent', [$result]);

        $this->handleErrorCode($result);
        return true;
    }

    public function sendByCustom($requestMethod, $apiMethod, $apiParams,$order)
    {
        $client = $this->getClient();
        if ($apiMethod == 'logistics.getShopKey') {
            $requestData = $client->buildRequestData($apiParams, $apiMethod);
            $paramsStr = urldecode(http_build_query($requestData));
            return ['paramsBase64Str' => base64_encode($paramsStr)];
        }
        //如果是发货的api，订单信息不为空
        /**
         * @var  Order $order
         */
        if($apiMethod == 'order.logisticsAdd'){
            if(!isset($order)){
                $orderSn= $apiParams['order_id'];
                if(!empty($orderSn)){
                    $order = Order::query()->where('tid',$orderSn)->first();
//                    Log::info('订单信息为空，通过订单号查询订单信息',[$orderSn,$order]);
                }
            }
            if(isset($order)&&$order->refund_status!=Order::REFUND_STATUS_NO){
                Log::info('订单有售后，不能发货',[$order->tid]);
                throw new ApiException(StatusCode::ORDER_HAS_REFUND_NOT_DELIVER);
            }
            if(isset($order)&&$order->order_status==Order::ORDER_STATUS_CLOSE){
                Log::info('订单已取消，不能发货',[$order->tid]);
                throw new ApiException(StatusCode::ORDER_CANCELLED_NOT_DELIVER);
            }


        }
        $result = $client->executeByCustom($apiMethod, $apiParams, $requestMethod);
        return $result;
    }

    /**
     * @inheritDoc
     */
    public function fillApiParamByOrder($apiMethod, $apiParams, $order, $orderShop = null): array
    {
        switch ($apiMethod) {
            case 'logistics.newCreateOrder';
                $receiver_info = [
                    'address' => [
                        'city_name' => $order['receiver_city'],
                        'country_code' => 'CHN',
                        'detail_address' => $order['order_cipher_info']['receiver_address_ciphertext'],
                        'district_name' => $order['receiver_district'],
                        'province_name' => $order['receiver_state'] ?? $order['receiver_province'],
                        'street_name' => $order['receiver_town']
                    ],
                    'contact' => [
                        'mobile' => $order['order_cipher_info']['receiver_phone_ciphertext'],
                        'name' => $order['order_cipher_info']['receiver_name_ciphertext'],
                        'phone' => $order['receiver_tel'] ?? '',
                    ]
                ];
                $apiParams['order_infos'][0]['receiver_info'] = $receiver_info;
                break;
            default:
                break;
        }
        return $apiParams;
    }

    /**
     *
     * 订单的拆包发货
     * $orderDeliveryRequests的格式
     * [
     *  [
     *  "tid"=>"6919675406348523072A",
     *  "shopId"=>1111,
     *  "packs" =>[
     *          [              "waybillCode"=>"6919675406348588608",
     *                  "expressCode"=>"xxx",
     *                  "goods"=>["oid"=>111,
     *                  "shippedNum"=>1],
     *                   ]
     *  ]
     * ]
     * @param int $shopId
     * @param array $orderDeliveryRequests
     * @return array{"successes":array,"failures":array}
     * @throws ApiException
     */

    public function orderMultiPackagesDelivery(int $shopId,array $orderDeliveryRequests): array
    {
        $client = $this->getClient();
        $requestData = [];
        $url = 'order/logisticsAddMultiPack';
        $this->poolCurlAbnormalOutputOriginalData = true;
        foreach ($orderDeliveryRequests as  $orderDeliveryRequest) {
            $requestItem = [];
            $requestItem['request_id']=RandomUtil::uuid();
            $tid = $orderDeliveryRequest['tid'];
            $requestItem['order_id'] = $this->filterTid($tid);
            $requestItem['pack_list'] =[] ;
            /**
             * 数组的格式是
             * [
             * [
             * "waybillCode"=>"6919675406348588608",
             * "expressCode"=>"xxx",
             *  "packs"=>[
             *      ["oid"=>111,"shippedNum"=>1],
             *  ]
             * ]
             */
            $waybillCodes = [];
            foreach ($orderDeliveryRequest['packs'] as $pack) {
                $packItem = [];
                $waybillCode = $pack['waybillCode'];
                $packItem['logistics_code'] = $waybillCode;
                $expressCode = $pack['expressCode'];
                $packItem['company_code'] = $this->expressCodeMap[$expressCode] ?? $expressCode;
                $packItem['shipped_order_info'] = [];
                $packs = [];
                foreach ($pack['goods'] as $goods) {
                    $oid = $goods['oid'];
                    $packItem['shipped_order_info'][] = [
                        'shipped_order_id' => $oid,
                        'shipped_num' => $goods['shippedNum'],
                    ];
                    $packs[] = ["oid"=>$oid,"shippedNum"=>$goods['shippedNum']];
                }
                $requestItem['pack_list'][] = $packItem;
                $waybillCodes[] = ["waybillCode"=>$waybillCode,"expressCode"=>$expressCode,"packs"=>$packs];
            }
//            $requestItem['company_code'] = $orderDeliveryRequest['expressCode'];
//
//            $shippedOrderInfos = [];
//            foreach ($orderDeliveryRequest['goodsInfos'] as $goodsInfo) {
//                $shippedOrderInfos[] = [
//                    'shipped_order_id' => $goodsInfo['oid'],
//                    'shipped_num' => $goodsInfo['shippedNum'],
//                ];
//            }
//            $requestItem['shipped_order_info'] = $shippedOrderInfos;
            $requestData[$tid] = [
                'waybillCodes' => $waybillCodes,
                'shopId'=>$shopId,
                'params' => $this->getRequestData($url, $requestItem),
                'url' => $client->gatewayUrl . '/' . $url
            ];
        }
        Log::info('$requestData',[$requestData]);
        $responses = $this->poolCurl($requestData, 'post');
        $successes = [];
        $failures = [];
        foreach ($responses as $tid=> $response) {
            $request=$requestData[$tid];
            Log::info('拆单发货返回', [$tid,$request, $response]);
            //这个地方如果是正常会返回一个stdClass，反之会返回一个数组
//            if($response instanceof  \stdClass) {
//                $code = $response->code;
//            }else {
//                $code = $response['code'] ?? null;
//            }

            try {
                $this->handleErrorCode($response);
                Log::info('拆单发货成功', [$tid,$request, $response]);
                $successes[] = ['tid' => $tid, "shopId"=>$shopId,   "waybillCodes" => $request['waybillCodes']];
            } catch (\Exception $e) {
                //进入了异常情况，返回的是一个数组
                $subMsg = $e->getMessage();
                Log::info('拆单发货失败', [$tid,$request, $response]);
                $failures[] = ['tid' => $tid,"shopId"=>$shopId, "waybillCodes" => $request['waybillCodes'], 'msg' => $subMsg];
            }
//            if (isset($code)&& $code == 10000) {
//                \Log::info('拆单发货成功', [$tid,$request, $response]);
//                $successes[] = ['tid' => $tid, "shopId"=>$shopId,   "waybillCodes" => $request['waybillCodes']];
//            }else {
//                //进入了异常情况，返回的是一个数组
//                $subMsg = $response['sub_msg']??'未知';
//                \Log::info('拆单发货失败', [$tid,$request, $response]);
//                $failures[] = ['tid' => $tid,"shopId"=>$shopId, "waybillCodes" => $request['waybillCodes'], 'msg' =>
//                    $subMsg];
//            }
        }
        return ["successes" => $successes, "failures" => $failures];


    }


//
//        $packDeliverResponse->waybillCode= $waybillCode;
//        try {
//            $client = $this->getClient();
//            $wpCode = $request->wpCode;
//
//                $shippedOrderInfo = [];
//                foreach ($request->items as $packItem ) {
//                    $shippedOrderInfo[] = [
//                        'shipped_order_id' => $packItem->oid,
//                        'shipped_num' => $packItem->shippedNum,
//                    ];
//                }
//                $pack_list=[
//                    "shipped_order_info"=> $shippedOrderInfo,
//                    "logistics_code"=>$waybillCode,
//                    'company_code' => $wpCode,
//                ];
//                $params = [
//                    'order_id' => $tid,
//                    'pack_list'=>json_encode($pack_list),
////                'logistics_id' => (string)$this->expressCodeList[$expressCode],
//                    'request_id' => $request->requestId
//                ];
//
//                $result = $client->execute('order/logisticsAddMultiPack', $params);
//                if (empty($result['data']['pack_list'])) {
//                    \App\Utils\Log::info('dy_delivery_seller_orders   params:' . json_encode($params) . ' dy_result:' . json_encode($result));
//                    $packDeliverResponse->success=false;
//                    $packDeliverResponse->errMsg="失败";
//                    return $packDeliverResponse;
//                }
//            }
//            Log::info('dy_delivery_seller_orders   params:' . json_encode($params) . ' dy_result:' . json_encode($result));
//            $packDeliverResponse->success=true;
//        }catch (\Throwable $throwable){
//            $packDeliverResponse->success=false;
//            $packDeliverResponse->errMsg=$throwable->getMessage();
//            Log::errorException("发货异常",$throwable,[$request]);
//        }
//        return $packDeliverResponse;

    public function buildAddressMd5($order, $cipherInfo, $isCrossShopMergeOrder,$oldOrder):string
    {
        if (empty($cipherInfo['oaid'])){
            return parent::buildAddressMd5($order, $cipherInfo, $isCrossShopMergeOrder,$oldOrder);
        }
        $addressArr = [
            $cipherInfo['oaid'],
        ];
//        Log::info('buildAddressMd5',[implode(',', $addressArr)]);
        return md5(implode(',', $addressArr));
    }
    public function sendCheckMergeOrder(array $orderList): array
    {

        $merge_list = [];
        foreach ($orderList as $order) {
            $tid = $order['tid'];
            // 超过15天的订单不允许合单
            if (time() - strtotime($order['pay_at']) > 15 * 86400) {
                continue;
            }
            $merge_list[] = [
                'order_id' => $this->filterTid($tid),
                'open_address_id' => $order['order_cipher_info']['oaid'],
            ];
        }
        $mergeListChunk = array_chunk($merge_list, 100); // 取100个

        if (empty($mergeListChunk[0])){
            return [];
        }
        $client = $this->getClient();
        $params = [
            'merge_list' => $mergeListChunk[0],
        ];
        // 合单只能是2-100
        if (count($params['merge_list']) < 2 || count($params['merge_list']) > 100) {
            Log::info('sendCheckMergeOrderError',$params);
            return [];
        }
        $result = $client->execute('order/merge', $params, 'post');
        try {
            $this->handleErrorCode($result);
        } catch (\Exception $e) {
            Log::info('sendCheckMergeOrder:error', [$e->getMessage()]);
            return [];
        }
        $order_list = $result['data']['order_list'];
        $orderList = array_map(function ($item) {
            $arr = explode(',', $item);
            $newArr = [];
            foreach ($arr as $index => $item) {
                $newArr[] = $item;
            }
            return implode(',', $newArr);
        }, $order_list);
        return $orderList;
    }


    /**
     * @doc https://op.jinritemai.com/docs/guide-docs/1480/6504#%E9%92%88%E5%AF%B9%E3%80%90%E8%AE%A2%E5%8D%95%E7%AE%A1%E7%90%86%E5%BA%94%E7%94%A8%E3%80%91
     * @param Event $event
     * @return bool
     * @throws ApiException
     * @throws ClientException
     */
    public function reportBatchLogByEvent(Event $event): bool
    {
//        Log::debug('reportBatchLogByEvent', [$event]);
        $list = [];
        if ($event instanceof OrderCreateEvent) {
            foreach ($event->orderIds as $index => $orderId) {
                $tid = $this->filterTid($orderId);
                $list[] = [
                    'order_id' => $tid,
                    'status' => 'ERP_TRANSFER',
                    'record_time' => Carbon::createFromTimestamp($event->time)->toDateTimeString(),
                ];
            }
        }  elseif ($event instanceof OrderPrintEvent) {
            foreach ($event->allOrderIds as $index => $orderId) {
                $tid = $this->filterTid($orderId);
                $list[] = [
                    'order_id' => $tid,
                    'status' => 'PRINT',
                    'record_time' => Carbon::createFromTimestamp($event->time)->toDateTimeString(),
                ];
            }
        } else {
            return true;
        }
        $client = $this->getClient();
        $trace_list_chunk = array_chunk($list,20);
        foreach ($trace_list_chunk as $index => $trace_list_item) {
            $params = [
                'trace_list' => $trace_list_item,
            ];
            $result = $client->execute('trace/batchRecord', $params);
//            Log::debug('reportBatchLogByEvent',[$params,$result]);
        }
        return true;
    }

    /**
     * @throws ClientException
     * @throws ApiException
     * @throws OrderException
     */
    public function orderAppendPackages(string $tid, string $wpCode, string $waybillCode, array $goodsList = []): bool
    {
        $client = $this->getClient();
        $product_orders = [];
        foreach ($goodsList as $item) {
            $product_orders[] = [
                'product_order_id' => $item['oid'],
                'product_count' => $item['num'],
            ];
        }
        $params = [
            'shop_order_id' => $tid,
            'tracking_no' => $waybillCode,
            'company_code' => $wpCode,
            'product_orders' => $product_orders
        ];

        $result = $client->execute('order/uploadextrapackage', $params, 'post');
        $this->handleErrorCode($result);
        return true;
    }
}
