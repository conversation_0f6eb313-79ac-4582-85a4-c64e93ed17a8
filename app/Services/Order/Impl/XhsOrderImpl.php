<?php

namespace App\Services\Order\Impl;

use App\Constants\ErrorConst;
use App\Constants\PlatformConst;
use App\Constants\RefundSubStatusConst;
use App\Events\Subscription\SubscriptionOrderSucceeded;
use App\Exceptions\ApiException;
use App\Exceptions\ClientException;
use App\Exceptions\OrderException;
use App\Jobs\XhsMsg\XhsSkuUpdateJob;
use App\Models\Goods;
use App\Models\GoodsSku;
use App\Models\Order;
use App\Models\OrderExtra;
use App\Models\OrderItem;
use App\Models\Package;
use App\Models\PlatformOrder;
use App\Models\Shop;
use App\Models\UserExtra;
use App\Services\Bo\LogisticsDataBo;
use App\Services\Bo\LogisticsDataProductBo;
use App\Services\BusinessException;
use App\Services\Client\XhsClient;
use App\Services\CommonResponse;
use App\Services\Order\AbstractOrderService;
use App\Services\Order\OrderCipherInterface;
use App\Services\Order\Request\OrderDeliverAgainRequest;
use App\Utils\DateTimeUtil;
use App\Utils\RandomUtil;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class XhsOrderImpl extends AbstractOrderService implements OrderCipherInterface
{
    /**
     * 每次拉取订单间隔的分钟
     * @var int
     */
    public $orderTimeInterval = 720;

    /**
     * 每次拉取增量订单间隔的分钟
     * @var int
     */
    public $orderIncrTimeInterval = 30;

    /**
     * 每次拉取退款订单间隔的分钟
     * @var int
     */
    public $refundOrderTimeInterval = 30;

    /**
     * 每次拉取厂家订单间隔的分钟
     * @var int
     */
    public $factoryOrderTimeInterval = 30;

    protected $platformType = PlatformConst::PLATFORM_TYPE_XHS;

    protected $orderStatusMap = [
        0 => Order::ORDER_STATUS_UNKNOWN,
        1 => Order::ORDER_STATUS_PADDING,
        2 => Order::ORDER_STATUS_PADDING,
        3 => Order::ORDER_STATUS_PADDING,
        4 => Order::ORDER_STATUS_PAYMENT,
        5 => Order::ORDER_STATUS_PART_DELIVERED,
        6 => Order::ORDER_STATUS_DELIVERED,
        7 => Order::ORDER_STATUS_SUCCESS,
        8 => Order::ORDER_STATUS_CLOSE,
        9 => Order::ORDER_STATUS_CLOSE,
        10 => Order::ORDER_STATUS_RECEIVED,
    ];
    protected $refundStatusMap = [
        // 1无售后 2售后处理中 3售后完成(含取消) 4售后拒绝 5售后关闭 6平台介入中 7售后取消
        1 => Order::REFUND_STATUS_NO, // 无售后或售后关闭
        2 => Order::REFUND_STATUS_YES,
        3 => Order::REFUND_STATUS_YES,
        4 => Order::REFUND_STATUS_YES,
        5 => Order::REFUND_STATUS_NO,
        6 => Order::REFUND_STATUS_YES,
        7 => Order::REFUND_STATUS_NO,
    ];
    protected $orderFlagMap = [
        // 旗子颜色 1灰旗 2红旗 3黄旗 4绿旗 5蓝旗 6紫旗
        0 => Order::FLAG_NONE,
        1 => Order::FLAG_GRAY,
        2 => Order::FLAG_RED,
        3 => Order::FLAG_YELLOW,
        4 => Order::FLAG_GREEN,
        5 => Order::FLAG_BLUE,
        6 => Order::FLAG_PURPLE,
    ];

    protected function getClient(): XhsClient
    {
        $client = XhsClient::newInstance($this->getAccessToken());
        return $client;
    }

    public function formatOrderFlag(?int $flag): string
    {
        return $this->orderFlagMap[$flag] ?? Order::FLAG_NONE;
    }

    /**
     * @inheritDoc
     */
    public function formatToOrder(array $trade): array
    {
        Log::debug('formatToOrder', [$trade['orderId']]);
        $orderItems = [];
        $skuIdArr = array_column($trade['skuList'], 'skuId');
        $goodsSkuList = GoodsSku::query()->with('goods')->whereIn('sku_id', $skuIdArr)->get();
        // 未发货的订单
        $unshippedOrderList = collect($trade['simpleDeliveryOrderList'])->where('status', 4)->first();// 4:待发货
        foreach ($trade['skuList'] as $index => $item) {
            $goodsType = $item['skuTag'] == 1 ? OrderItem::GOODS_TYPE_GIFT : OrderItem::GOODS_TYPE_NORMAL;
            $skuId = $item['skuId'];
            $goodsSku = $goodsSkuList->firstWhere('sku_id', $skuId);
            if (!empty($goodsSku->goods->num_iid)) {
                $num_iid = $goodsSku->goods->num_iid;
            } else {
                // 缓存 60分钟
                $num_iid = \Cache::remember('goodsIdBySkuId:' . $skuId, 60, function () use ($skuId) {
                    // 从接口取
                    $apiGoods = $this->sendGetGoodsBySkuId($skuId);
                    $itemId = $apiGoods[0]['sku']['itemId'] ?? null;
                    if (empty($itemId)) {
                        return null; // 不缓存
                    }
                    if ($this->getShop()) {
                        $shop = $this->getShop();
                        $xhsSkuUpdateJob = new XhsSkuUpdateJob([$skuId], $shop->identifier);
                        dispatch($xhsSkuUpdateJob);
                    }
                    return $itemId;
                });
            }
            // 不在未发货里，就是已发货
            if (!empty($unshippedOrderList['skuIdList']) && in_array($skuId, $unshippedOrderList['skuIdList'])) {
                $orderStatus = Order::ORDER_STATUS_PAYMENT;
            } else {
                $orderStatus = Order::ORDER_STATUS_DELIVERED;
            }
            $skuValue = $item['skuSpec'] ?? '';
            $skuValueArr = explode(' ', $skuValue); // 文艺小镇 均码 上衣90-160斤
            $skuValue = str_replace(' ', ';', $skuValue);
            $skuValue1 = $skuValueArr[0] ?? '';
            $skuValue2 = $skuValueArr[1] ?? '';

            $orderItems[] = [
                "tid" => (string)$trade['orderId'], //主订单
                "oid" => $trade['orderId'] . '-' . $skuId, //子订单号
                "type" => $this->platformType, //订单类型
                "status" => $orderStatus, //订单状态
                "payment" => formatToYuan($item['totalPaidAmount']), //实付金额
                "total_fee" => formatToYuan($item['totalPaidAmount']), //总金额
                "discount_fee" => formatToYuan($item['totalMerchantDiscount'] + $item['totalRedDiscount']), //优惠金额
                "goods_title" => $item['skuName'], //商品标题
                "goods_pic" => $item['skuImage'], //商品图片
                "goods_price" => formatToYuan($item['totalPaidAmount'] / $item['skuQuantity']), //商品单价
                "goods_num" => $item['skuQuantity'], //商品数量
                "num_iid" => $num_iid, //商品id
                "sku_id" => $skuId, //sku id
                "sku_value" => $skuValue,
                "sku_value1" => $skuValue1,
                "sku_value2" => $skuValue2,
//                "sku_num" => $item['skuQuantity'], //商品数量
                "sku_price" => formatToYuan($item['totalPaidAmount'] / $item['skuQuantity']), //商品价格
                "outer_iid" => $item['erpcode'] ?? '', //商家外部商品编码
                "outer_sku_iid" => '', //商家外部sku编码
                "order_created_at" => DateTimeUtil::toDateTimeTimeString($trade['createdTime']), //订单创建时间
                "order_updated_at" => DateTimeUtil::toDateTimeTimeString($trade['updateTime']), //订单修改时间
//                "refund_id" => 0, //
                "refund_status" => $this->formatRefundStatus($item['skuAfterSaleStatus']), //退款状态
                "refund_sub_status" => $this->formatSubRefundStatus($item), //
                "goods_type" => $goodsType,
                "author_id" => $item['kolId'] ?? '',
                "author_name" => $item['kolName'] ?? '',
            ];
        }
        $allRefundStatus = array_column($orderItems, 'refund_status');
        // 判断子订单状态
        if (empty($allRefundStatus)) {
            $order_refund_status = Order::REFUND_STATUS_NO; // 如果没有订单项，默认设置为 0
        } elseif (in_array(1, $allRefundStatus) && in_array(0, $allRefundStatus)) {
            $order_refund_status = Order::REFUND_STATUS_PART; // 既有 1 也有 0
        } elseif (array_sum($allRefundStatus) == count($allRefundStatus)) {
            $order_refund_status = Order::REFUND_STATUS_YES; // 全部是 1
        } else {
            $order_refund_status = Order::REFUND_STATUS_NO; // 全部是 0
        }

        $sellerMemo = empty($trade['sellerRemark']) ? [] : [$trade['sellerRemark']];
        $cipher_info = [
            'receiver_phone_ciphertext' => $trade['receiverPhone'] ?? '',
            'receiver_name_ciphertext' => $trade['receiverName'] ?? '',
            'receiver_address_ciphertext' => $trade['receiverAddress'] ?? '',
            'receiver_phone_mask' => $trade['receiverPhoneMask'] ?? '',
            'receiver_name_mask' => $trade['receiverNameMask'] ?? '',
            'receiver_address_mask' => $trade['receiverAddressMask'] ?? '',
            'oaid' => $trade['openAddressId'],
        ];
        if (empty($cipher_info['receiver_phone_ciphertext'])) {
            unset($cipher_info['receiver_phone_ciphertext'], $cipher_info['receiver_name_ciphertext'], $cipher_info['receiver_address_ciphertext']);
        }
        $receiverNameIdx = $this->extractSearchIndex($trade['receiverName'] ?? '');
        $receiverPhoneIdx = $this->extractSearchIndex($trade['receiverPhone'] ?? '');
        $receiverAddressIdx = $this->extractSearchIndex($trade['receiverAddress'] ?? '');

//        /** @var CommonTemplateService $commonTemplateService */
//        $commonTemplateService = Container::get(CommonTemplateService::class);
//        $union_wp_code = $commonTemplateService->getUnionWpCodeByTemplateCode($trade['logistics_id']);
        // 承诺发货时间
        $promise_ship_at = null;
        if (!empty($trade['promiseLastDeliveryTime'])) {
            $promise_ship_at = DateTimeUtil::toDateTimeTimeString($trade['promiseLastDeliveryTime']);
        }
        if (empty($promise_ship_at) && !empty($trade['presaleDeliveryEndTime'])) {
            $promise_ship_at = DateTimeUtil::toDateTimeTimeString($trade['presaleDeliveryEndTime']);
        }
        // 包裹信息
        $logistics_data = [];
        foreach ($trade['simpleDeliveryOrderList'] as $logistics_info) {
            if (empty($logistics_info['expressTrackingNo'])) {
                continue;
            }
            $product_list = [];
            foreach ($logistics_info['skuIdList'] as $skuId) {
                $oid = $trade['orderId'] . '-' . $skuId;
                $orderItem = collect($orderItems)->firstWhere('oid', $oid);
                $logisticsDataProductBo = new LogisticsDataProductBo();
                $logisticsDataProductBo->oid = $oid;
                $logisticsDataProductBo->sku_id = $skuId;
                $logisticsDataProductBo->outer_sku_id = $orderItem['outer_sku_iid'];
                $logisticsDataProductBo->num_iid = $orderItem['num_iid'];
                $logisticsDataProductBo->num = $orderItem['goods_num'];
                $logisticsDataProductBo->goods_title = $orderItem['goods_title'];
                $logisticsDataProductBo->sku_value = $orderItem['sku_value'];
                $product_list[] = $logisticsDataProductBo;
            }
            $logisticsDataBo = new LogisticsDataBo();
            $logisticsDataBo->waybill_code = $logistics_info['expressTrackingNo']; // 运单号
            $logisticsDataBo->wp_code = $logistics_info['expressCompanyCode'];
            $logisticsDataBo->wp_name = '';
            $logisticsDataBo->delivery_at = null;
            $logisticsDataBo->delivery_id = $logistics_info['deliveryOrderIndex'];// 包裹 id
            $logisticsDataBo->product_list = $product_list;
            $logistics_data[] = $logisticsDataBo;
        }

        $is_pre_sale = in_array($trade['orderType'], [2, 3, 4]) ? 1 : 0;
        $orderData = [
            "tid" => (string)$trade['orderId'], //主订单
            "type" => $this->platformType, //订单类型
//                "user_id" => $trade[''], //用户ID
//            "express_no" => $trade[''], //快递单号
//            "waybill_code" => $trade['tracking_number'], //运单号
//            "union_wp_code" => $union_wp_code, //物流公司编码
            "buyer_id" => $trade['userId'], //买家ID
            "buyer_nick" => '', //买家昵称
            "seller_nick" => $trade['shopName'], //卖家昵称
            "order_status" => self::formatOrderStatus($trade['orderStatus']), //订单状态
            "refund_status" => $order_refund_status, //退款状态
//                "print_status" => $trade[''], //打印状态
            "shop_title" => $trade['shopName'], //店铺名
            "receiver_state" => $trade['receiverProvinceName'], //收货人省份
            "receiver_city" => $trade['receiverCityName'], //收货人城市
            "receiver_district" => $trade['receiverDistrictName'], //收货人地区
//            "receiver_town" => '', //收货人街道
            "receiver_name" => $receiverNameIdx, //收货人名字
            "receiver_phone" => $receiverPhoneIdx, //收货人手机
            "receiver_zip" => 0, //收件人邮编
            "receiver_address" => $receiverAddressIdx, //收件人详细地址
            "payment" => formatToYuan($trade['totalPayAmount']), //实付金额
            "total_fee" => formatToYuan($trade['totalPayAmount']), //总金额
            "discount_fee" => formatToYuan($trade['totalMerchantDiscount'] + $trade['totalRedDiscount']), //优惠金额
            "post_fee" => formatToYuan($trade['totalShippingFree']), //运费
            "seller_flag" => $this->formatOrderFlag($trade['sellerRemarkFlag'] ?? 0), //卖家备注旗帜
            "seller_memo" => json_encode($sellerMemo, 320), //卖家备注
            "buyer_message" => $trade['customerRemark'] ?? null, //买家留言
//                "express_code" => $trade[''], //快递公司代码
            "order_created_at" => DateTimeUtil::toDateTimeTimeString($trade['createdTime']), //订单创建时间
            "order_updated_at" => DateTimeUtil::toDateTimeTimeString($trade['updateTime']), //订单修改时间
            "send_at" => DateTimeUtil::toDateTimeTimeString($trade['deliveryTime']), //发货时间
            "finished_at" => DateTimeUtil::toDateTimeTimeString($trade['updateTime']), //订单完成时间
            "groupon_at" => DateTimeUtil::toDateTimeTimeString($trade['finishTime']), //订单完成时间
            "pay_at" => DateTimeUtil::toDateTimeTimeString($trade['paidTime']), //支付时间
//                "refund_id" => $trade[''], //退款id
            'goods_total_num' => array_sum(array_column($orderItems, 'sku_num')), // 商品数量
            'sku_num' => count($orderItems), // SKU数量
            'num' => array_sum(array_column($orderItems, 'goods_num')), // 商品数量
            'is_pre_sale' => $is_pre_sale, // 是否预售1是0否
            'promise_ship_at' => $promise_ship_at, // 承诺发货时间
            'items' => $orderItems,
            'cipher_info' => $cipher_info,
            'order_extra' => [
                'logistics_data' => jsonEncode($logistics_data),
            ],
        ];

        return $orderData;
    }

    public function formatToAfterSale(array $trade)
    {
        // TODO: Implement formatToAfterSale() method.
    }

    /**
     * @inheritDoc
     */
    public function formatToOrders(array $orders): array
    {
        $list = [];
        foreach ($orders as $index => $order) {
            $list[] = $this->formatToOrder($order);
        }
        return $list;
    }

    /**
     * @inheritDoc
     */
    public function formatToGoods(array $goodsList): array
    {
        $list = [];
        foreach ($goodsList as $index => $goods) {
            $list[] = $this->formatToGood($goods);
        }
        return $list;
    }

    /**
     * @inheritDoc
     */
    protected function sendGetTradesOrders(int $startTime, int $endTime, bool $isFirstPull = false)
    {
        $this->hasNext = false;
        $client = $this->getClient();
        $params = [
            'startTime' => $startTime,
            'endTime' => $endTime,
            'timeType' => 1, // startTime/endTime对应的时间类型，1 创建时间 限制 end-start<=24h、2 更新时间 限制 end-start<=30min 倒序拉取 最后一页到第一页
            'orderType' => 0, // 订单类型，0/null 全部 1 普通 normal 2 定金预售 3 全款预售(废弃) 4 全款预售(新) 5 换货补发
            'orderStatus' => 0, // 订单状态，0全部 1已下单待付款 2已支付处理中 3清关中 4待发货 5部分发货 6待收货 7已完成 8已关闭 9已取消 10换货申请中
            'pageNo' => $this->page,
            'pageSize' => $this->pageSize,
        ];
        //第一次仅拉取待发货
        if ($isFirstPull) {
            $params['orderStatus'] = 4;
        }

        $result = $client->execute('order.getOrderList', $params);
        Log::debug('getTradesOrders', [$params]);
        $client->handleErrorCode($result);

        $list = (array)$result['data']['orderList'];
        if ($this->page <= $result['data']['maxPageNo']) {
            $this->hasNext = true;
        }
//        $this->total = $result['data']['total'] ?? -1;
        $tidOrders = collect($list)->map(function ($item) {
            return ['tid' => $item['orderId']];
        })->toArray();

        $orderInfoList = $this->sendBatchGetOrderInfo($tidOrders);
        return $orderInfoList;
    }

    public function sendBatchGetOrderInfo($orders, $safe = false): array
    {
        if (empty($orders)) {
            return [];
        }
        $client = $this->getClient();
        $paramsArr = [];
        foreach ($orders as $order) {
            $params = [
                'orderId' => $order['tid'],
            ];
            $paramsArr[$order['tid']] = [
                "url" => $client->gatewayUrl,
                "params" => $client->buildRequestData($params, "order.getOrderDetail")
            ];
        }
        $orderInfoArr = [];
        $response = $this->poolCurl($paramsArr, 'post', 5, $safe);
        foreach ($response as $orderId => $orderInfo) {
            $orderInfo = json_decode(json_encode($orderInfo), true);
            $trade = $orderInfo['data'] ?? [];
            if (empty($trade)) {
                continue;
            }
            $orderInfoArr[$orderId] = $trade;
        }

        // 去拉收件人信息
        $receiverList = [];
        foreach ($orderInfoArr as $item) {
            // 4 待发货 5 部分发货
            if (!in_array($item['orderStatus'], [4, 5])) {
                continue;
            }
            $receiverList[] = [
                'orderId' => $item['orderId'],
                'openAddressId' => $item['openAddressId'],
            ];
        }
        if (!empty($receiverList)) {
            $receiverListChunk = array_chunk($receiverList, 20); // 每次限制 20 个
            foreach ($receiverListChunk as $receiverListItem) {
                $params = [
                    'receiverQueries' => array_values($receiverListItem)
                ];
                $receiverResult = $client->execute('order.getOrderReceiverInfo', $params);
                $receiverInfos = collect($receiverResult['data']['receiverInfos'])->keyBy('orderId')->toArray();
                foreach ($receiverInfos as $indexOrderId => $receiverInfo) {
                    $orderInfoArr[$indexOrderId] = array_merge($orderInfoArr[$indexOrderId], $receiverInfo);
                }
            }
        }

        // 去拉脱敏信息
        $baseInfoList = [];
        $receiverFieldArr = ['receiverName', 'receiverPhone', 'receiverAddress'];
        foreach ($orderInfoArr as $item) {
            // 4 待发货
            if ($item['orderStatus'] != 4) {
                continue;
            }
            foreach ($receiverFieldArr as $receiverField) {
                $baseInfoList[] = [
                    'dataTag' => $item['orderId'],
                    'encryptedData' => $item[$receiverField],
                ];
            }
        }
        if (!empty($baseInfoList)) {
            $baseInfoListChunk = array_chunk($baseInfoList, 100);
            foreach ($baseInfoListChunk as $index => $baseInfoListItems) {
                $params = [
                    'baseInfos' => array_values($baseInfoListItems),
                    'actionType' => 4, // 操作类型1 - 单个查看订单明文，2 - 批量解密打单，3 - 批量解密后面向三方的数据下发，4 - 其他场景,解密接口必填
                    'appUserId' => REQ_ID . '_' . $index,
                ];
                $receiverResult = $client->execute('data.batchDesensitise', $params);
                $desensitiseInfoListGroup = collect($receiverResult['data']['desensitiseInfoList'])->groupBy('dataTag')->toArray();
                $resultArr3 = [];
                foreach ($desensitiseInfoListGroup as $idxTid => $desensitiseInfoList) {
                    $desensitiseInfoList = collect($desensitiseInfoList)->keyBy('encryptedData')->toArray();
                    $arr3 = [];
                    // 通过密文来索引取到掩码后的数据，赋值给拼接上 Mask 的字段，比如 receiverNameMask
                    foreach ($receiverFieldArr as $receiverField) {
                        $ciphertext = $orderInfoArr[$idxTid][$receiverField] ?? '';
                        if (empty($ciphertext)) {
                            continue;
                        }
                        if (empty($desensitiseInfoList[$ciphertext]['desensitisedData'])) {
                            continue;
                        }
                        $str = $desensitiseInfoList[$ciphertext]['desensitisedData'];
                        if ($receiverField == 'receiverAddress') {
                            // 替换 $str 字符串里，最前面是否存在 **,省市区 这 3 种，如果存在，则替换成空。循环替换；
                            // 原文：**上海市徐汇区宛平南路**弄**号** , 替换后：徐汇区宛平南路
                            $arr = [
                                '\*\*',
                                '省',
//                                $resultArr[$dataTag]['receiverProvinceName'],
                                $orderInfoArr[$idxTid]['receiverCityName'],
                                $orderInfoArr[$idxTid]['receiverDistrictName']
                            ];
                            for ($i = 0; $i < count($arr); $i++) {
                                $str = preg_replace('/^' . $arr[$i] . '/', '', $str);
                            }
                        }
                        $arr3[$receiverField . 'Mask'] = $str;
                    }
                    $orderInfoArr[$idxTid] = array_merge($orderInfoArr[$idxTid], $arr3);
                }
            }
        }

        return $orderInfoArr;

    }

    /**
     * @inheritDoc
     */
    protected function sendGetOrderInfo(string $tid)
    {
        $client = $this->getClient();

        $data = [
            'orderId' => $tid,
        ];
        $result = $client->execute('order.getOrderDetail', $data);
        $client->handleErrorCode($result);
        return $result['data'];
    }

    protected function sendGetAfterSaleOrderInfo(string $tid)
    {
        // TODO: Implement sendGetAfterSaleOrderInfo() method.
    }

    /**
     * @inheritDoc
     */
    protected function sendGetGoods(int $pageSize, int $currentPage)
    {
        $client = $this->getClient();
        $this->hasNext = false;

        $data = [
            'pageNo' => $currentPage,
            'pageSize' => $pageSize,
        ];
        $result = $client->execute('product.getDetailSkuList', $data);
        $client->handleErrorCode($result);
        if (isset($result['data']['total'])) {
            $this->goodsTotalCount = $result['data']['total'];
        } else {
            $this->goodsTotalCount = 0;
        }
        if (count($result['data']['data']) == $result['data']['pageSize']) {
            $this->hasNext = true;
        }
        return $result['data']['data'];
    }

    /**
     * @inheritDoc
     */
    protected function deliverySellerOrders(string $tid, string $expressCode, string $expressNo, array $orderItemOId, bool $silent = true)
    {
        // TODO: Implement deliverySellerOrders() method.
    }

    /**
     * @inheritDoc
     * @param OrderDeliverAgainRequest $orderDeliverAgainRequest
     * @return bool
     * @throws ApiException
     * @throws ClientException
     * @throws OrderException
     */
    protected function deliverySellerOrdersAgain(OrderDeliverAgainRequest $orderDeliverAgainRequest): bool
    {
        $tid = $orderDeliverAgainRequest->tid;
        $expressCode = $orderDeliverAgainRequest->wpCode;
        $expressNo = $orderDeliverAgainRequest->waybillCode;
        $client = $this->getClient();

        $data = [
            'orderId' => $tid,
            'expressNo' => $expressNo,
            'expressCompanyCode' => $expressCode,
            'deliveryOrderIndex' => $orderDeliverAgainRequest->deliveryId,
        ];
        $result = $client->execute('order.modifyOrderExpressInfo', $data);
        $client->handleErrorCode($result);
        return true;
    }

    /**
     * @inheritDoc
     */
    protected function deliverySellerOrdersForOpenApi(string $tid, string $expressCode, string $expressNo, array $orderItemOId)
    {
        // TODO: Implement deliverySellerOrdersForOpenApi() method.
    }

    /**
     * @inheritDoc
     */
    protected function sendGetTradesOrdersByIncr(int $startTime, int $endTime, bool $isFirstPull = false): array
    {
        $this->hasNext = false;
        $client = $this->getClient();

        $data = [
            'startTime' => $startTime,
            'endTime' => $endTime,
            'timeType' => 2, // startTime/endTime对应的时间类型，1 创建时间 限制 end-start<=24h、2 更新时间 限制 end-start<=30min 倒序拉取 最后一页到第一页
            'orderType' => 0, // 订单类型，0/null 全部 1 普通 normal 2 定金预售 3 全款预售(废弃) 4 全款预售(新) 5 换货补发
            'orderStatus' => 0, // 订单状态，0全部 1已下单待付款 2已支付处理中 3清关中 4待发货 5部分发货 6待收货 7已完成 8已关闭 9已取消 10换货申请中
            'pageNo' => $this->page,
            'pageSize' => $this->pageSize,
        ];
        $result = $client->execute('order.getOrderList', $data);
        $client->handleErrorCode($result);

        $list = (array)$result['data']['orderList'];
        if (count($list) == $this->pageSize) {
            $this->hasNext = true;
        }
//        $response->total = $result['data']['total'] ?? -1;
        $tidOrders = collect($list)->map(function ($item) {
            return ['tid' => $item['orderId']];
        })->toArray();

        $orderInfoList = $this->sendBatchGetOrderInfo($tidOrders);
        return $orderInfoList;
    }

    /**
     * @inheritDoc
     */
    public function sendServiceInfo():array
    {
        $shop = $this->getShop();
        $shopId = $shop->id;
        $userId = $shop->user_id;
        $subscriptionServiceInfo = PlatformOrder::getServiceInfo($shop);
        Log::info('获取订阅服务信息', ['shop_id' => $shopId, 'subscription' => $subscriptionServiceInfo]);
        if (!$subscriptionServiceInfo) {
            Log::info('首次授权，初始化一个免费版本', ['shop_id' => $shopId, 'user_id' => $userId]);
            $platformOrder = PlatformOrder::initFreeVersion($shopId, $userId);
            event(new SubscriptionOrderSucceeded($platformOrder));
            $subscriptionServiceInfo = PlatformOrder::getServiceInfo($shop);
        }

        return $subscriptionServiceInfo->toArray();


//        $version = UserExtra::getVersionValueByName('试用版');
//        $result = [
//            'user_id' => $shop->user_id,
//            'shop_id' => $shop->id,
//            'identifier' => $shop->identifier,
//            'platform_type' => $this->platformType,
////            'expired_at' => Carbon::parse($shop->created_at)->addDay(30)->toDateTimeString(), //暂定免费版使用30天
//            'expired_at' => Carbon::now()->addDay(30)->toDateTimeString(), //暂定免费版使用30天
//            'version' => $version,
//            'version_desc' => array_get(UserExtra::VERSION_MAP_ARR, $version, ''),
//            'pay_amount' => 0,
//        ];
//        return $result;

        //XHS是自己做的订购，从订购记录里面找

    }

    /**
     * @inheritDoc
     */
    protected function sendDecrypt($params)
    {
        $client = $this->getClient();
        $cipher_infos[] = [
            'dataTag' => $params['tid'],
            'encryptedData' => $params['text'],
        ];
        $response = $client->execute('data.batchDecrypt', [
            'baseInfos' => $cipher_infos,
            'actionType' => 1, // 操作类型1 - 单个查看订单明文，2 - 批量解密打单，3 - 批量解密后面向三方的数据下发，4 - 其他场景,解密接口必填
            'appUserId' => REQ_ID
        ]);
        \Log::info("Decrypt result", [$response]);
        $client->handleErrorCode($response);
        $returnData = [];
        $decryptInfoList = $response['data']['dataInfoList'] ?? [];

        foreach ($decryptInfoList as $index => $decryptInfo) {
            if ($decryptInfo['errorCode'] > 0) {
                Log::error('sendDecrypt error', [$decryptInfo]);
                throw new OrderException('小红书平台错误：' . $decryptInfo['errorMsg']);
            }
            $returnData[] = [
                'tid' => $decryptInfo['dataTag'],
                'text' => $decryptInfo['decryptedData'],
            ];
        }

        return $returnData;
    }

    /**
     * @inheritDoc
     */
    protected function sendBatchEncrypt($order)
    {
        // TODO: Implement sendBatchEncrypt() method.
    }

    /**
     * @inheritDoc
     */
    public function checkAuthStatus(): bool
    {
        $client = $this->getClient();
        try {
            $params = [
                'startTime' => time() - 1,
                'endTime' => time(),
                'timeType' => 2, // startTime/endTime对应的时间类型，1 创建时间 限制 end-start<=24h、2 更新时间 限制 end-start<=30min 倒序拉取 最后一页到第一页
                'orderType' => 0, // 订单类型，0/null 全部 1 普通 normal 2 定金预售 3 全款预售(废弃) 4 全款预售(新) 5 换货补发
                'orderStatus' => 0, // 订单状态，0全部 1已下单待付款 2已支付处理中 3清关中 4待发货 5部分发货 6待收货 7已完成 8已关闭 9已取消 10换货申请中
                'pageNo' => 1,
                'pageSize' => 1,
            ];
            $result = $client->execute('order.getOrderList', $params);
            $client->handleErrorCode($result);
        } catch (ApiException $e) {
            if (in_array($e->getCode(), ErrorConst::AUTH_ERRORS)) {
                return false;
            }
        } catch (\Exception $e) {

        }
        return true;
    }

    /**
     * @inheritDoc
     */
    public function batchDeliveryOrders(array $orderDeliveryRequests): array
    {
        $client = $this->getClient();
        $requestData = [];
        $url = 'order.orderDeliver';
        $this->poolCurlAbnormalOutputOriginalData = true;
        foreach ($orderDeliveryRequests as $orderDeliveryRequest) {
            $requestItem = [];
            $tid = $orderDeliveryRequest->tid;
            $requestItem['orderId'] = $tid;
            $waybillCodes = [];
            $requestItem['expressNo'] = $orderDeliveryRequest->expressNo;
            $requestItem['expressCompanyCode'] = $orderDeliveryRequest->expressCode;
//            $requestItem['unpack'] = true;
            $requestData[] = [
                'orderId' => $tid,
//                'waybillCodes' => $waybillCodes,
                'params' => $client->buildRequestData($requestItem, $url),
                'url' => $client->getBaseUrlByApimethod(),
            ];
        }
        $responseData = $this->poolCurl($requestData, 'post');
        $results = [];

        foreach ($responseData as $index => $result) {
            $commonResponse = new CommonResponse();
            try {
                $orderDeliveryRequest = $orderDeliveryRequests[$index];
                $commonResponse->setRequest($orderDeliveryRequest);
                $commonResponse->setRequestId($orderDeliveryRequest->getRequestId());
                $result = json_decode(json_encode($result), true);
                $client->handleErrorCode($result);

                $commonResponse->setSuccess(true);
            } catch (\Exception $e) {
                $commonResponse->setSuccess(false);
                $commonResponse->code = $e->getCode();
                $commonResponse->message = $e->getMessage();
            } finally {
                $results[] = $commonResponse;
            }
        }

        return $results;
    }


    /**
     * @inheritDoc
     */
    public function openSubscribeMsg(): bool
    {
        return false;
    }

    /**
     * @inheritDoc
     */
    public function consumeSubscribeMsg()
    {
        // TODO: Implement consumeSubscribeMsg() method.
    }

    /**
     * @inheritDoc
     */
    public function confirmSubscribeMsg(array $idArr)
    {
        // TODO: Implement confirmSubscribeMsg() method.
    }

    /**
     * @inheritDoc
     */
    public function handleSubscribeMsg($data)
    {
        // TODO: Implement handleSubscribeMsg() method.
    }

    /**
     * @inheritDoc
     */
    public function createWebsocketConnection()
    {
        // TODO: Implement createWebsocketConnection() method.
    }

    /**
     * @inheritDoc
     */
    public function batchGetRefundApplyList($data)
    {
        // TODO: Implement batchGetRefundApplyList() method.
    }

    /**
     * @inheritDoc
     */
    protected function sendRefundOrders(int $startTime, int $endTime)
    {
        // TODO: Implement sendRefundOrders() method.
    }

    /**
     * @inheritDoc
     */
    protected function formatRefundOrder($order)
    {
        // TODO: Implement formatRefundOrder() method.
    }

    public function sendEditSellerRemark($tid, $sellerFlag, $sellerMemo): bool
    {
        $client = $this->getClient();

        $data = [
            'orderId' => $tid,
            'sellerMarkNote' => $sellerMemo,
            'operator' => $this->getShop()->shop_name,
        ];
        $orderFlagMap = $this->orderFlagMap;
        unset($orderFlagMap[0]);
        $orderFlagMap = array_flip($orderFlagMap);
        $data['sellerMarkPriority'] = $sellerFlag ? $orderFlagMap[$sellerFlag] : 1;

        $result = $client->execute('order.modifySellerMarkInfo', $data);
        $client->handleErrorCode($result);

        return true;
    }

    public function sendServiceOrderList($beginAt, $endAt)
    {
        // TODO: Implement sendServiceOrderList() method.
    }

    /**
     * @inheritDoc
     */
    public function sendFactoryShopRoleType(): int
    {
        // TODO: Implement sendFactoryShopRoleType() method.
    }

    /**
     * @inheritDoc
     */
    public function getFactoryTradesOrder(int $startTime, int $endTime)
    {
        // TODO: Implement getFactoryTradesOrder() method.
    }

    /**
     * @inheritDoc
     */
    public function batchGetFactoryOrderInfo($orders)
    {
        // TODO: Implement batchGetFactoryOrderInfo() method.
    }

    /**
     * @inheritDoc
     */
    public function batchReturnFactoryOrder($orders)
    {
        // TODO: Implement batchReturnFactoryOrder() method.
    }

    public function batchWaybillRecoveryFactoryOrder($waybills)
    {
        // TODO: Implement batchWaybillRecoveryFactoryOrder() method.
    }

    public function sendGetSellerList()
    {
        // TODO: Implement sendGetSellerList() method.
    }

    /**
     * @inheritDoc
     */
    public function sendQueryTradeTid(array $query_list)
    {
        // TODO: Implement sendQueryTradeTid() method.
    }

    /**
     * @inheritDoc
     */
    public function sendAddress(): array
    {
        $client = $this->getClient();
        $params = [
        ];
        $result = $client->execute('common.getNestZone', $params);
        if (!isset($result['data']['provinceZoneList'])) {
            return [];
        }

        $list = $this->formatAddress($result['data']['provinceZoneList']);
        return $list;
    }

    protected function formatAddress(array $list, $parent_code = 1, $level = 1)
    {
        $resArr = [];
        foreach ($list as $item) {
            $arr = [
                'name' => $item['name'],
                'code' => $item['code'],
                'parent_code' => $parent_code,
                'level' => $level,
            ];
            if (!empty($item['zones']) && is_array($item['zones'])) {
                $arr['sub'] = $this->formatAddress($item['zones'], $item['code'], $level + 1);
            }
            $resArr[] = $arr;
        }
        return $resArr;
    }

    public function getQueryTradeOrderId(string $type, string $search):array
    {
        return [];
    }

    /**
     * @inheritDoc
     */
    public function fillApiParamByOrder($apiMethod, $apiParams, $order, $orderShop = null): array
    {
        switch ($apiMethod) {
            case 'express.createEbillOrders':
//                \Log::info('fillApiParamByOrder', [$order]);
                $recipient = [
                    'openAddressId' => $order['order_cipher_info']['oaid'],
                ];
                $apiParams['tradeOrderInfoList'][0]['recipient'] = $recipient;
                break;
        }
        Log::info('fillApiParamByOrder', [$apiMethod, $apiParams]);
        return $apiParams;
    }

    /**
     * @inheritDoc
     */
    public function sendByCustom($requestMethod, $apiMethod, $apiParams, $order)
    {
        $client = $this->getClient();
        $result = $client->execute($apiMethod, $apiParams, $requestMethod);
        return $result;
    }

    /**
     * @inheritDoc
     */
    public function cipherDecryptBatch()
    {
        // TODO: Implement cipherDecryptBatch() method.
    }

    /**
     * @inheritDoc
     */
    public function cipherDecryptMaskBatch(array $list): array
    {
        // TODO: Implement cipherDecryptMaskBatch() method.
    }

    /**
     * @inheritDoc
     */
    public function cipherExtractSearch(string $encryptedData): string
    {
        // TODO: Implement cipherExtractSearch() method.
    }

    public function getSearchIndex(string $text, string $type): string
    {
        // TODO: Implement getSearchIndex() method.
    }

    public function orderMultiPackagesDelivery(int $shopId, array $orderDeliveryRequests): array
    {
        $client = $this->getClient();
        $requestData = [];
        $url = 'order.orderDeliver';
        $this->poolCurlAbnormalOutputOriginalData = true;
        $requestItemArr = [];
        foreach ($orderDeliveryRequests as $orderDeliveryRequest) {
            $requestItem = [];
            $tid = $orderDeliveryRequest['tid'];
            $requestItem['orderId'] = $tid;
            $requestItem['skuIdList'] = [];
            /**
             * 数组的格式是
             * [
             * [
             * "waybillCode"=>"6919675406348588608",
             * "expressCode"=>"xxx",
             *  "packs"=>[
             *      ["oid"=>111,"shippedNum"=>1],
             *  ]
             * ]
             */
            $waybillCodes = [];
            foreach ($orderDeliveryRequest['packs'] as $pack) {
                $waybillCode = $pack['waybillCode'];
                $expressCode = $pack['expressCode'];
                $requestItem['expressNo'] = $waybillCode;
                $requestItem['expressCompanyCode'] = $expressCode;
                $requestItem['unpack'] = true;
                $skuIdList = [];
                $packs = [];
                foreach ($pack['goods'] as $goods) {
                    $skuIdList[] = $goods['sku_id'];
                    $packs[] = ["oid" => $goods['oid'], "shippedNum" => $goods['shippedNum']];
                }
                $requestItem['skuIdList'] = $skuIdList;
                $waybillCodes[] = ["waybillCode" => $waybillCode, "expressCode" => $expressCode, "packs" => $packs];

                $requestData[] = [
                    'orderId' => $tid,
                    'waybillCodes' => $waybillCodes,
                    'params' => $client->buildRequestData($requestItem, $url),
                    'url' => $client->getBaseUrlByApimethod(),
                ];
//                $requestItemArr[]= $requestItem;
            }
        }
//        foreach ($requestItemArr as $index => $requestItem) {
//             $requestData[$index] = [
//                'params' => $client->buildRequestData($requestItem, $url),
//                'url' => $client->getBaseUrlByApimethod(),
//            ];
//        }
        $responses = $this->poolCurl($requestData, 'post');
        $successes = [];
        $failures = [];
        foreach ($responses as $index => $response) {
            $request = $requestData[$index];
            \Log::info('拆单发货', [$index, $request, $response]);
            try {
                $client->handleErrorCode($response);
                \Log::info('拆单发货成功', [$request['orderId'], $request, $response]);
                $successes[] = ['tid' => $request['orderId'], "shopId" => $shopId, "waybillCodes" => $request['waybillCodes']];
            } catch (\Exception $e) {
                //进入了异常情况，返回的是一个数组
                $subMsg = $e->getMessage();
                \Log::info('拆单发货失败', [$request['orderId'], $request, $response]);
                $failures[] = ['tid' => $request['orderId'], "shopId" => $shopId, "waybillCodes" => $request['waybillCodes'], 'msg' => $subMsg];
            }
        }
        return ["successes" => $successes, "failures" => $failures];


    }

    /**
     * 发送获取商品列表请求
     * @param array $goodsId
     * @return mixed
     * @throws ApiException
     * @throws OrderException
     */
    protected function sendGetGoodsByGoodsId(array $goodsId)
    {
        // 订单不返回商品 id，所以不实现
        return [];
        $paramsArr = [];
        foreach ($goodsId as $datum) {
            $params = [
                'itemId' => $datum
            ];
            $paramsArr[] = $params;
        }
        $client = $this->getClient();
        $goodsList = $client->executeAsync('product.getItemInfo', $paramsArr);
        $client->handleErrorCode($goodsList);
        \Log::info("获取商品详情", $goodsList);

        return $goodsList;
    }

    /**
     * 发送获取商品列表请求
     * @param $skuId
     * @return mixed
     * @throws ApiException
     * @throws OrderException
     */
    protected function sendGetGoodsBySkuId($skuId)
    {
        $params = [
            'id' => $skuId
        ];
        $client = $this->getClient();
        $result = $client->execute('product.getDetailSkuList', $params);
        $client->handleErrorCode($result);
        Log::info("sendGetGoodsBySkuId", $result);
        if (empty($result['data']['data'])) {
            return [];
        }

        return $result['data']['data'];
    }

    /**
     * 提取搜索串
     * @param string $string
     * @param string $sep
     * @return string
     */
    private function extractSearchIndex(string $string, string $sep = '#'): string
    {
        if (empty($string)) {
            return '';
        }
        $str = $string;
        if (str_starts_with($string, $sep)) {
            $endPos = strpos($string, $sep, 1);
            $str = substr($string, 1, $endPos - 1);
        }
        return $str;
    }

    /**
     * @doc https://open.xiaohongshu.com/document/api?apiNavigationId=221&id=63&gatewayId=103&gatewayVersionId=1661&apiId=27228&apiParentNavigationId=17
     * @param $goods
     * @return array
     */
    private function formatToGood($goods)
    {
        $skus = [];
        $skuValue = '';
        foreach ($goods['sku']['variants'] as $value) {
            $skuValue .= $value['value'] . ';';
        }
        $skus[] = [
            "type" => $this->platformType,
            "sku_id" => $goods['sku']['id'],
            "sku_value" => $skuValue,
            "outer_id" => $goods['sku']['erpCode'],
            "outer_goods_id" => '',
            "sku_pic" => $goods['item']['images'][0],
            "is_onsale" => $goods['sku']['buyable'] ? Goods::IS_ONSALE_YES : Goods::IS_ONSALE_NO,
        ];

        return [
            "type" => $this->platformType,
            'num_iid' => $goods['item']['id'],
            'outer_goods_id' => '',
            'goods_title' => $goods['item']['name'],
            'goods_pic' => $goods['item']['images'][0],
            'is_onsale' => Goods::IS_ONSALE_YES,
            'goods_created_at' => date('Y-m-d H:i:s', $goods['item']['createTime'] / 1000),
            'goods_updated_at' => date('Y-m-d H:i:s', $goods['item']['updateTime'] / 1000),
            'skus' => $skus
        ];
    }

    private function formatSubRefundStatus($item)
    {
        switch ($item['skuAfterSaleStatus']) { //  1无售后 2售后处理中 3售后完成 4售后拒绝 5售后关闭 6平台介入中 7售后取消
            case 1: // 无售后
                return RefundSubStatusConst::NONE;
            case 2: // 售后处理中
                return RefundSubStatusConst::MERCHANT_PROCESSING;
            case 3: // 售后完成
                return RefundSubStatusConst::REFUND_COMPLETE;
            case 4: // 售后拒绝
                return RefundSubStatusConst::MERCHANT_REFUSE_REFUND;
            case 5: // 售后关闭
                return RefundSubStatusConst::REFUND_CLOSE;
            case 6: // 平台介入中
                return RefundSubStatusConst::PLATFORM_PROCESSING;
            case 7: // 售后取消
                return RefundSubStatusConst::REFUND_CLOSE;

        }
        return RefundSubStatusConst::NONE;
    }

}
