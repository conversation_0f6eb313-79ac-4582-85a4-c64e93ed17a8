<?php

namespace App\Services\Order\Impl;

use App\Exceptions\ErrorCodeException;
use App\Services\Client\CnClient;
use App\Services\Order\AbstractOrderService;
use TopClient\request\CustomRequest;

class CnOrderImpl extends AbstractOrderService
{

    /**
     * @inheritDoc
     */
    public function formatToOrder(array $trade): array
    {
        // TODO: Implement formatToOrder() method.
    }

    public function formatToAfterSale(array $trade)
    {
        // TODO: Implement formatToAfterSale() method.
    }

    /**
     * @inheritDoc
     */
    public function formatToOrders(array $orders): array
    {
        // TODO: Implement formatToOrders() method.
    }

    /**
     * @inheritDoc
     */
    public function formatToGoods(array $goods): array
    {
        // TODO: Implement formatToGoods() method.
    }

    /**
     * @inheritDoc
     */
    protected function sendGetTradesOrders(int $startTime, int $endTime, bool $isFirstPull = false)
    {
        // TODO: Implement sendGetTradesOrders() method.
    }

    /**
     * @inheritDoc
     */
    protected function sendGetOrderInfo(string $tid)
    {
        // TODO: Implement sendGetOrderInfo() method.
    }

    protected function sendGetAfterSaleOrderInfo(string $tid)
    {
        // TODO: Implement sendGetAfterSaleOrderInfo() method.
    }

    /**
     * @inheritDoc
     */
    protected function sendGetGoods(int $pageSize, int $currentPage)
    {
        // TODO: Implement sendGetGoods() method.
    }

    /**
     * @inheritDoc
     */
    protected function deliverySellerOrders(string $tid, string $expressCode, string $expressNo, array $orderItemOId, bool $silent = true)
    {
        // TODO: Implement deliverySellerOrders() method.
    }



    /**
     * @inheritDoc
     */
    protected function deliverySellerOrdersForOpenApi(string $tid, string $expressCode, string $expressNo, array $orderItemOId)
    {
        // TODO: Implement deliverySellerOrdersForOpenApi() method.
    }

    /**
     * @inheritDoc
     */
    protected function sendGetTradesOrdersByIncr(int $startTime, int $endTime, bool $isFirstPull = false): array
    {
        // TODO: Implement sendGetTradesOrdersByIncr() method.
    }

    /**
     * @inheritDoc
     * @throws ErrorCodeException
     */
    public function sendServiceInfo():array
    {
        throwUnsupportedException();
        return [];
    }

    /**
     * @inheritDoc
     */
    protected function sendDecrypt($order)
    {
        // TODO: Implement sendDecrypt() method.
    }

    /**
     * @inheritDoc
     */
    protected function sendBatchEncrypt($order)
    {
        // TODO: Implement sendBatchEncrypt() method.
    }

    /**
     * @inheritDoc
     */
    public function checkAuthStatus(): bool
    {
        // TODO: Implement checkAuthStatus() method.
    }

    /**
     * @inheritDoc
     */
    public function batchDeliveryOrders(array $orderDeliveryRequests): array
    {
        // TODO: Implement batchDeliveryOrders() method.
    }

    /**
     :b* @inheritDoc
     */
    protected function getClient()
    {
        // TODO: Implement getClient() method.
    }

    /**
     * @inheritDoc
     */
    public function openSubscribeMsg():bool
    {
        return false;
    }

    /**
     * @inheritDoc
     */
    public function consumeSubscribeMsg()
    {
        // TODO: Implement consumeSubscribeMsg() method.
    }

    /**
     * @inheritDoc
     */
    public function confirmSubscribeMsg(array $idArr)
    {
        // TODO: Implement confirmSubscribeMsg() method.
    }

    /**
     * @inheritDoc
     */
    public function handleSubscribeMsg($data)
    {
        // TODO: Implement handleSubscribeMsg() method.
    }

    /**
     * @inheritDoc
     */
    public function createWebsocketConnection()
    {
        // TODO: Implement createWebsocketConnection() method.
    }

    /**
     * @inheritDoc
     */
    public function batchGetRefundApplyList($data)
    {
        // TODO: Implement batchGetRefundApplyList() method.
    }

    /**
     * @inheritDoc
     */
    protected function sendRefundOrders(int $startTime, int $endTime)
    {
        // TODO: Implement sendRefundOrders() method.
    }

    /**
     * @inheritDoc
     */
    protected function formatRefundOrder($order)
    {
        // TODO: Implement formatRefundOrder() method.
    }

    public function sendEditSellerRemark($tid, $sellerFlag, $sellerMemo):bool
    {
        // TODO: Implement sendEditSellerRemark() method.
    }

    public function sendServiceOrderList($beginAt, $endAt)
    {
        // TODO: Implement sendServiceOrderList() method.
    }

    /**
     * @inheritDoc
     */
    public function sendFactoryShopRoleType(): int
    {
        // TODO: Implement sendFactoryShopRoleType() method.
    }

    /**
     * @inheritDoc
     */
    public function getFactoryTradesOrder(int $startTime, int $endTime)
    {
        // TODO: Implement getFactoryTradesOrder() method.
    }

    /**
     * @inheritDoc
     */
    public function batchGetFactoryOrderInfo($orders)
    {
        // TODO: Implement batchGetFactoryOrderInfo() method.
    }

    /**
     * @inheritDoc
     */
    public function batchReturnFactoryOrder($orders)
    {
        // TODO: Implement batchReturnFactoryOrder() method.
    }

    public function batchWaybillRecoveryFactoryOrder($waybills)
    {
        // TODO: Implement batchWaybillRecoveryFactoryOrder() method.
    }

    public function sendGetSellerList()
    {
        // TODO: Implement sendGetSellerList() method.
    }

    /**
     * @inheritDoc
     */
    public function sendQueryTradeTid(array $query_list)
    {
        // TODO: Implement sendQueryTradeTid() method.
    }

    /**
     * @inheritDoc
     */
    public function sendAddress(): array
    {
        // TODO: Implement sendAddress() method.
    }

    /**
     * @inheritDoc
     */
    public function getQueryTradeOrderId(string $type, string $search):array
    {
        // TODO: Implement getQueryTradeOrderId() method.
    }

    /**
     * @inheritDoc
     */
    public function fillApiParamByOrder($apiMethod, $apiParams, $order, $orderShop = null): array
    {
        return $apiParams;
    }

    /**
     * @inheritDoc
     */
    public function sendByCustom($requestMethod, $apiMethod, $apiParams, $order)
    {
        $client = CnClient::newInstance($this->accessToken);
        $req = new CustomRequest();
        $apiMethod = str_replace('/', '.', $apiMethod);
        $req->setApiMethodName($apiMethod);
        foreach ($apiParams as $key => $apiParam) {
            if (!is_array($apiParam) && !is_object($apiParam)){
                $apiParam = (string)$apiParam;
            }
            $req->putOtherTextParam($key, $apiParam);
        }
        list($requestUrl, $apiParams) = $client->getApiParamsAndUrl($req);
        return $client->executeByCustom($requestUrl, $apiParams);    }
}
