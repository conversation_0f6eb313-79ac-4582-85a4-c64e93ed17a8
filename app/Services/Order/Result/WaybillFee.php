<?php

namespace App\Services\Order\Result;

/**
 * 运单的费用
 */
class WaybillFee
{
    /**
     * @var int  网点ID
     */
    public $companyId;

    /**
     * @var string  网点描述
     */
    public $companyDesc;

    /**
     * @var string  面单来源
     */
    public $authSource;
    /**
     * @var string  区域名称
     */
    public $districtName;

    /**
     * @var string  区域编码
     */

    public $districtCode;

    /**
     * @var string  快递公司code
     */

    public $wpCode;

    /**
     * @var string  运单号
     */

    public $waybillCode;

    /**
     * @var string  运单费用
     */

    public $fee;


}
