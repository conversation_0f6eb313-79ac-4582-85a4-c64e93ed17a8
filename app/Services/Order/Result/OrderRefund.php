<?php

namespace App\Services\Order\Result;

class OrderRefund
{
    public $tid;
    public $reason;
    public $refundId;
    public $status;
    public $refundSubStatus;
    /**
     * @var OrderRefundItem[];
     */
    public $items;

    public  function addItem(OrderRefundItem $item):void
    {
        $this->items[] = $item;
    }

    /**
     * @var bool 是否部分退款
     */
    public $isPartRefund=false;

    /**
     * 看是不是有某个skuUuid的退款信息
     * @param  string  $skuUuid
     * @return bool
     */
    public function containsSkuUuid(string $skuUuid):bool
    {
        //如果不是部分退款，那肯定包含
        if(!$this->isPartRefund){
            return true;
        }
        foreach ($this->items as $item) {
            if ($item->skuUuid == $skuUuid) {
                return true;
            }
        }
        return false;
    }
}
