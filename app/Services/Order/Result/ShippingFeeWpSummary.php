<?php

namespace App\Services\Order\Result;

use Illuminate\Support\Collection;

class ShippingFeeWpSummary
{

    /**
     * @var int $companyId
     */
    public $companyId;

    public $wpCode;
    public $wpName;

    public $authSource;
    public $companyDesc;

    /**
     * @var Collection<ShippingFeeWpItem> $shippingFeeWpItems
     */
    public  $shippingFeeWpItems;

    public function addShippingFeeWpItem(ShippingFeeWpItem $shippingFeeWpItem)
    {
        if(!$this->shippingFeeWpItems){
            $this->shippingFeeWpItems = new Collection();
        }
        $this->shippingFeeWpItems->push($shippingFeeWpItem);
    }



}
