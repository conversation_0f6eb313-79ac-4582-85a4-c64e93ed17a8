<?php

namespace App\Services\Auth\Impl;

use App\Constants\PlatformConst;
use App\Exceptions\ClientException;
use App\Http\StatusCode\StatusCode;
use App\Models\Shop;
use App\Services\Auth\AbstractAuthService;
use App\Services\Client\AlbbClient;
use App\Services\Client\Alc2mClient;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;

class Alc2mAuthImpl  extends AlbbAuthImpl
{

    protected $platformType = PlatformConst::PLATFORM_TYPE_ALC2M;

    /**
     * @inheritDoc
     */
    public function formatToShop($socialiteUser): array
    {
        $token = $socialiteUser->getToken();
        $original = $socialiteUser->getOriginal();

        Log::debug('formatToShop', [$original]);
        return [
            'type' => $this->platformType,
            'identifier' => $socialiteUser->getId(),
            'shop_identifier' => $socialiteUser->getId(),
            'access_token' => $token->getToken(),
            'refresh_token' => $original['refresh_token'],
            'expire_at' => date('Y-m-d H:i:s', time() + $original['expires_in']),
            'auth_at' => date('Y-m-d H:i:s'),
            'shop_name' => $socialiteUser->getName(),
            // 'name' => $socialiteUser->getName(),
            'name' => $socialiteUser->getName(),
            'auth_user_id' => 0,
        ];
    }



    protected function getClient()
    {
        return Alc2mClient::newInstance();
    }
}
