<?php

namespace App\Services\PlatformOrder;

use App\Constants\PlatformConst;
use App\Services\PlatformOrder\Impl\DyPlatformOrderImpl;
use App\Services\PlatformOrder\Impl\JdPlatformOrderImpl;
use App\Services\PlatformOrder\Impl\KsPlatformOrderImpl;
use App\Services\PlatformOrder\Impl\PddPlatformOrderImpl;
use App\Services\PlatformOrder\Impl\TaobaoPlatformOrderImpl;
use App\Services\PlatformOrder\Impl\WxPlatformOrderImpl;
use App\Services\PlatformOrder\Impl\WxspPlatformOrderImpl;
use InvalidArgumentException;

class PlatformOrderServiceManager
{
    protected static $initMap = [
        PlatformConst::TAOBAO => TaobaoPlatformOrderImpl::class,
        PlatformConst::PDD => PddPlatformOrderImpl::class,
        PlatformConst::KS => KsPlatformOrderImpl::class,
        PlatformConst::WX => WxPlatformOrderImpl::class,
        PlatformConst::DY => DyPlatformOrderImpl::class,
        PlatformConst::JD => JdPlatformOrderImpl::class,
        PlatformConst::WXSP => WxspPlatformOrderImpl::class,
//        PlatformConst::XHS => XhsPlatformOrderImpl::class,
    ];

    /**
     * 创建一个Platform订单server
     * @param $name
     * @return AbstractPlatformOrderService
     * <AUTHOR>
     */
    public static function create($name)
    {
        if (isset(self::$initMap[$name])) {
            return new self::$initMap[$name]();
        }
        throw new InvalidArgumentException('不存在的 PlatformOrderServiceManager:' . $name);
    }
}
