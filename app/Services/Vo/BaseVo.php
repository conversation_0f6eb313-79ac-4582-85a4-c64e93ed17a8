<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2022/1/6
 * Time: 22:32
 */

namespace App\Services\Vo;

use App\Services\Traits\CopyAttributeTrait;
use ArrayAccess;

class BaseVo implements ArrayAccess
{
    use CopyAttributeTrait;

    public function __construct($data = [])
    {
        $this->copyByArr($data);
    }


    /**
     * @inheritDoc
     */
    public function offsetExists($offset): bool
    {
        if (property_exists($this, $offset)) {
            return true;
        }
        return false;
    }

    /**
     * @inheritDoc
     */
    public function offsetGet($offset)
    {
        if ($this->offsetExists($offset)) {
            return $this->$offset;
        }
        return null;
    }

    /**
     * @inheritDoc
     */
    public function offsetSet($offset, $value)
    {
        if ($this->offsetExists($offset)) {
            $this->$offset = $value;
        }
    }

    /**
     * @inheritDoc
     */
    public function offsetUnset($offset)
    {
        if ($this->offsetExists($offset)) {
            $this->$offset = null;
        }
    }
}
