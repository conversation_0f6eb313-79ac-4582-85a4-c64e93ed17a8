<?php

namespace App\Services\User;

use App\Constants\PlatformConst;
use App\Exceptions\ApiException;
use App\Models\Shop;
use App\Models\ShopBind;
use App\Models\User;
use App\Models\UserShopBind;
use App\Services\ShengYiWang\ShengYiWangClient;
use Firebase\JWT\JWT;

class UserService
{

    /**
     * @throws \Exception
     */
    public function loginByOpenUser(array $requestData): array
    {
        $open_user_id = $requestData['open_user_id'];
        $open_token = $requestData['open_token'];
        $open_user_source = $requestData['open_user_source'] ?? '';
        if (empty($open_user_id) || in_array($open_user_id,['null','undefined']) || empty($open_token)){
            throw new \Exception('open_user_id open_token 不能为空');
        }
        $user = User::firstOrCreate(['open_user_id' => $open_user_id]);
        $user->update(['open_token' => $open_token, 'open_user_source' => $open_user_source]);
        $shop = Shop::firstOrCreate(['user_id' => $user->id, 'type' => PlatformConst::PLATFORM_TYPE_USER], [
            'shop_name' => '默认店铺',
            'name' => '默认店铺',
            'identifier' => uniqid('def_'),
        ]);


        $payload = [
            'iss' => 'lumen-jwt',
            'sub' => $user->id,
            'iat' => time(),
            'shop_id' => $shop->id,
            'identifier' => $shop->identifier,
            'plaftorm_type' => $shop->type,
            'shop_name' => $shop->name,
            'exp' => time() + env('JWT_EXPIRATION_TIME')
        ];
        $token = JWT::encode($payload, env('JWT_SECRET'));
        redis('cache')->setex('jwt_token:' . $payload['sub'], intval($payload['exp'] - $payload['iat']), $token);
        $arr = [
            'token' => $token,
            'token_type' => 'bearer',
        ];
        return $arr;
    }

    /**
     * @throws \Exception
     */
    public function updateShopListBySyw($user, $shop)
    {
        $shengYiWangClient = new ShengYiWangClient();
        $userShop=$this->getPlatformUserShop($user->id);
        $params = [];
        $shengYiWangClient->setUser($user);
        $response = $shengYiWangClient->execute('GET', '/third/print/shopInfo/getInfoList', $params);
        $shengYiWangClient->handleResponse($response);
        $responseData = $response['data'];

        if (empty($responseData)){
            throw new \Exception('店铺信息获取失败');
        }

        $shopIdArr = [$shop->id];
        foreach ($responseData as $responseDatum) {
            if ($responseDatum['ifExpire']){
                $auth_status = Shop::AUTH_STATUS_EXPIRE;
            }else{
                $auth_status = Shop::AUTH_STATUS_SUCCESS;
            }
            $type = PlatformConst::SYW_TYPE_MAP[$responseDatum['type']];
            $thisShop = Shop::where(['identifier' => $responseDatum['shopUid'], 'type' => $type])->first();
            $attributes = [
                'user_id' => 0,
                'type' => $type,
                'identifier' => $responseDatum['shopUid'],
                'name' => $responseDatum['name'],
//                'name' => $responseDatum['nickName'],
                'shop_name' => $responseDatum['name'],
                'shop_logo' => $responseDatum['image'],
                'access_token' => $responseDatum['accessToken'],
                'refresh_token' => $responseDatum['refreshToken'],
                'expire_at' => $responseDatum['expireTime'],
                'service_id' => $responseDatum['venderId'],
                'auth_status' => $auth_status,
            ];
            if (empty($thisShop)){
                $thisShop = Shop::create($attributes);
            }
            $thisShop->update($attributes);
            $shopIdArr[] = $thisShop->id;
//            UserShopBind::firstOrCreate([
//                'user_id' => $user->id,
//                'shop_id' => $thisShop->id,
//            ]);
            //迁移到ShopBind
            ShopBind::firstOrCreate([
                'f_shop_id'=>$userShop->id,
                'o_shop_id'=>$thisShop->id,
                'type'=>ShopBind::TYPE_ME_BIND,

//                'user_id' => $user->id,
//                'shop_id' => $thisShop->id,
            ]);

        }
        //删掉没有的店铺
//        $userShopBinds = UserShopBind::where('user_id', $user->id)->whereNotIn('shop_id', $shopIdArr)->get();
        // 只删掉关系
//        $userShopBinds->each(function ($query){
//            $query->delete();
//        });
        //删掉没用的店铺关系
        ShopBind::where('f_shop_id',$userShop->id)->where('type',ShopBind::TYPE_ME_BIND)->whereNotIn('o_shop_id', $shopIdArr)->delete();
    }

    /**
     * 这个就是获取用户的平台店铺
     * @param  int  $userId
     * @return ?Shop
     */
    public function getPlatformUserShop(int $userId): ?Shop
    {
        return Shop::query()->where('user_id', $userId)->where('type', PlatformConst::PLATFORM_TYPE_USER)->first();
    }
}
