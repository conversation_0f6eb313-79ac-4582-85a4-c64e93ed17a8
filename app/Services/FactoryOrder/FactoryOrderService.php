<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2021/12/22
 * Time: 17:04
 */

namespace App\Services\FactoryOrder;

use App\Constants\ErrorConst;
use App\Constants\PlatformConst;
use App\Constants\PrintConst;
use App\Events\Orders\OrderPrintEvent;
use App\Events\Orders\OrderWaybillEvent;
use App\Exceptions\ApiException;
use App\Exceptions\PrintException;
use App\Jobs\Orders\SaveFactoryOrdersJob;
use App\Jobs\Orders\SyncSaveOrders;
use App\Models\Company;
use App\Models\FactoryOrder;
use App\Models\Order;
use App\Models\Package;
use App\Models\PackageOrder;
use App\Models\PrintRecord;
use App\Models\QueryArea;
use App\Models\Shop;
use App\Models\ShopBind;
use App\Models\ShopExtra;
use App\Models\Template;
use App\Models\User;
use App\Models\Waybill;
use App\Models\WaybillHistory;
use App\Services\Bo\PrintWaybillBo;
use App\Services\Bo\SenderAddressBo;
use App\Services\Bo\PrintDataPackBo;
use App\Services\Bo\PrintPackBo;
use App\Services\Bo\WaybillBo;
use App\Services\Bo\WaybillsPrintDataBo;
use App\Services\Order\AbstractOrderService;
use App\Services\Order\OderLogicService;
use App\Services\Order\OrderServiceManager;
use App\Services\PrintDataService;
use App\Services\Printing\OrderPrintService;
use App\Services\Vo\PrintPackVo;
use App\Services\Waybill\DY\DYApi;
use App\Services\Waybill\PDDWB\PDDWBApi;
use App\Services\Waybill\Taobao\NewTBApi;
use App\Services\Waybill\WaybillServiceManager;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class FactoryOrderService
{
    private $orderService;


    /**
     * 智能查询
     * @param Builder $query
     * @param $requestData
     * @param $search
     * @param $selectItem
     * @param $includeOrNot
     * @return mixed
     * <AUTHOR>
     */
    public function handleSmartSearch(Builder $query, $requestData, $search, $selectItem)
    {
        if ($selectItem > 0 && $search) {
            switch ($selectItem) {
                case '1':
                        $query->where('distr_tid', $search);
                    break;
                case '2':
                    $query->where('waybill_code', $search);
                    break;
                case '5':
                    //商品包含
                    $goodsInclude = json_decode($search, true);
                    $query->where('goods_title', 'like', "%$goodsInclude[0]%");
                    $query->where('outer_goods_id', 'like', "%$goodsInclude[0]%");
                    $query->where('sku_id', 'like', "%$goodsInclude[1]%");
                    $query->where('outer_sku_id', 'like', "%$goodsInclude[1]%");
                    break;
                case '6':
                    //商品不含
                    $goodsInclude = json_decode($search, true);
                    $query->where('goods_title', 'not like', "%$goodsInclude[0]%");
                    $query->where('outer_goods_id', 'not like', "%$goodsInclude[0]%");
                    $query->where('sku_id', 'not like', "%$goodsInclude[1]%");
                    $query->where('outer_sku_id', 'not like', "%$goodsInclude[1]%");
                    break;
                case '9':
                    $query->where('seller_memo', 'like', "%{$search}%");
                    break;
                case '13':
                    $query->where('buyer_message', 'like', "%{$search}%");
                    break;
                case '16':
                    // 单个商品规格数量
                    if (strpos($search, '-')) {
                        list($left, $right) = explode('-', $search);
                        $query->where('goods_num', '>=', $left);
                        $query->where('goods_num', '<=', $right);
                    } else {
                        $query->where('goods_num', '=', $search);
                    }
                    break;
                default:
                    break;
            }
        } else {
            //关键词查询
            if ($search) {
                $packageOrderIdArr = Package::query()
                    ->leftJoin('package_orders', 'package_id', '=', 'packages.id')
                    ->where('waybill_code', $search)
                    ->get(['order_id'])
                    ->pluck('order_id')
                    ->toArray();
                $query->where(function ($query) use ($search, $packageOrderIdArr) {
                    $query->where('distr_tid', $search)
                        ->orWhere('waybill_code', $search)
                        ->orWhere('seller_memo', 'like', "%{$search}%");// 卖家备注
                    if (!empty($packageOrderIdArr)) {
                        $query->orWhereIn('id', $packageOrderIdArr);
                    }
                });
            }
        }

        return $query;
    }

    /**
     * 排序处理
     * @param        $query
     * @param string $sort
     * @return Builder
     */
    public function handleOrderBy($query, string &$sort)
    {
        if (empty($sort)) {
            $sort = ['dist_at', 'desc'];
            return $query;
        }
        $sort = explode(' ', $sort);


        $orderBy = '';
        switch ($sort[0]) {
            case 'last_ship_time':
            case 'confirm_time':
            case 'pay_time':
                $orderBy = 'distr_at';
                break;
            case 'shipping_time':
                $orderBy = 'return_at';
                break;
            case 'printed_time':
                $orderBy = 'print_at';
                break;
            case 'goods_count':
                $orderBy = 'goods_num';
                break;
            case 'goods_price':
                $orderBy = 'goods_price';
                break;
            case 'goods_title':
                if ($sort[1] == 'desc') {
                    $orderBy = 'goods_title';
                } else {
                    $orderBy = 'goods_title_last';
                }
                $query->orderByRaw('CONVERT(orders.' . $orderBy . ' USING gbk) COLLATE gbk_chinese_ci ' . $sort[1]);
                break;
            case 'sku_value':
                if ($sort[1] == 'desc') {
                    $orderBy = 'sku_value_last';
                } else {
                    $orderBy = 'sku_value';
                }
                $query->orderByRaw('CONVERT(' . $orderBy . ' USING gbk) COLLATE gbk_chinese_ci ' . $sort[1]);
                break;
            case 'promise_ship_at':
                $orderBy = 'promise_ship_at';
                break;
            case 'area':
                //根据省份城市排序
                $orderSort = $sortArea[1] ?? 'desc';
                $query->orderByRaw('CONVERT(receiver_state USING gbk) COLLATE gbk_chinese_ci ' . $orderSort)
                    ->orderByRaw('CONVERT(receiver_city USING gbk) COLLATE gbk_chinese_ci ' . $orderSort)
                    ->orderByRaw('CONVERT(receiver_district USING gbk) COLLATE gbk_chinese_ci ' . $orderSort);
                break;
            default:
                $orderBy = 'distr_at';
                break;
        }
        $sort = [$orderBy, $sort[1]];

        return $query;
    }

    /**
     * @param $data
     * @throws PrintException
     */
    public function checkAuthStatusAndWaybillBalance($data)
    {
        $total = 0;
        foreach ($data['packages'] as $package) {
            if (!empty($package['waybill_code'])) {
                $total++;
            }
        }
        $templateInfo = Template::query()->where('id', $data['template_id'])->first();
        if (in_array($templateInfo->auth_source, [ Waybill::AUTH_SOURCE_DY,Waybill::AUTH_SOURCE_KS,Waybill::AUTH_SOURCE_TAOBAO,Waybill::AUTH_SOURCE_JD])) {
            $waybillAuth = Shop::query()->where('id', $templateInfo->shop_id)->first();
            if ($waybillAuth->auth_status != Shop::AUTH_STATUS_SUCCESS || strtotime($waybillAuth->expire_at) < time()) {
                $codeUrl = socialite()->driver(config('app.platform'))->redirect()->getTargetUrl();
                $templateInfo->codeUrl = $codeUrl;
                //return $this->success([$templateInfo], '电子面单账户过期, 请重新授权', 1007);
                throw new PrintException([$templateInfo], 1007, '电子面单账户过期, 请重新授权');
            }
        } else {
            // 快手平台已切换的店铺 取新号不支持第三方电子面单
            if (config('app.platform') == PlatformConst::KS && ksEncryptSwitch($templateInfo->shop_id)) {
                //return $this->success([], '平台订单现不支持第三方电子面单', 1006);
                throw new PrintException([], 1006, '平台订单现不支持第三方电子面单');
            }
            // 抖音不支持第三方电子面单
            if (config('app.platform') == PlatformConst::DY ) {
                throw new PrintException([], 1006, '平台订单现不支持第三方电子面单');
            }
            $waybillAuth = Waybill::where([
                'owner_id' => $templateInfo->owner_id,
                'auth_source' => $templateInfo->auth_source
            ])->orderBy('expires_at', 'desc')->first();

            if (empty($waybillAuth) || strtotime($waybillAuth->expires_at) < time()) {
                throw new PrintException([$templateInfo], 1000, '电子面单过期, 请重新授权');
            }
        }

        $company = $templateInfo->company;
        $authSource = $templateInfo->auth_source;
        //虚拟网点直接使用余额
        if ($company->source == Company::SOURCE_COMPANY_STATUS_YES) {
            $quantity = $company->quantity;
            if ($quantity != -1 && $total > $quantity) {
                //return $this->success(['need_num' => $total, 'balance' => $quantity], '电子面单余额不足，请联系分享者', 1001);
                throw new PrintException(['need_num' => $total, 'balance' => $quantity], 1001, '电子面单余额不足，请联系分享者');
            }
        } else {
            \Log::info("waybillAuth",[$waybillAuth]);
            try {
                $waybillService = WaybillServiceManager::init($authSource, $waybillAuth->access_token);
                $waybill = $waybillService->waybillSubscriptionQuery($company->wp_code, $waybillAuth->service_id);
                $quantityInfo = collect($waybill[0]['branch_account_cols'])->where('branch_code', $company->branch_code)->first();
            } catch (\Exception $e) {
                throw new PrintException([$templateInfo], 1007, '电子面单账户过期, 请重新授权');
            }

            if ($company->branch_code) {
                $quantity = $quantityInfo['quantity'];
            } else {
                $quantity = ($waybill[0]['branch_account_cols'])[0]['quantity'];
            }
            if (in_array($authSource, [Waybill::AUTH_SOURCE_PDD_WB, Waybill::AUTH_SOURCE_PDD]) && $total > $quantity && $waybill[0]['wp_type'] == 1 && $this->isNewPrint) {
                throw new PrintException(['need_num' => $total, 'balance' => $quantity], 1001, '电子面单余额不足，请充值');
            }
            if (in_array($authSource, [Waybill::AUTH_SOURCE_TWC, Waybill::AUTH_SOURCE_LINK, Waybill::AUTH_SOURCE_DY]) && $total > $quantity && $waybill[0]['wp_type'] != 1 && $this->isNewPrint) {
                throw new PrintException(['need_num' => $total, 'balance' => $quantity], 1001, '电子面单余额不足，请充值');
            }
        }
    }

    public function checkPrintStatusAndOrderStatus($userId, $shopId, $data)
    {
        $hasWaybillCodeList = $hasDeliveryList = [];
        $orderIdArr = array_merge(...array_pluck($data['packages'], 'orders.*.id'));
        $orderList = FactoryOrder::query()->with(['packages' => function ($query) {
            $query->whereNull('recycled_at');
        }])->whereIn('id', $orderIdArr)->get()->toArray();

        \Log::info('查询数据库订单结束');
        //拉取一把最新状态
        $orderService = OrderServiceManager::create();
        $orderService->setUserId($userId);
        $orderService->setShop(Shop::find($shopId));
        $dbOrders = array_pluck($orderList, null, 'id');
        $tradesOrder = $orderService->batchGetFactoryOrderInfo($dbOrders);
        \Log::info('拉取平台订单结束');

        $platformOrders = array_column($tradesOrder, null, 'distr_tid');
        //是否有已打印/已发货订单
        foreach ($orderList as $item) {
            if (in_array($item['print_status'], [Order::PRINT_STATUS_YES, Order::PRINT_STATUS_PART])) {
                $hasWaybillCodeList[] = $item;
            }

            $distrStatus = isset($platformOrders[$item['distr_tid']]) ? $platformOrders[$item['distr_tid']]['distr_status'] : $item['distr_status'];
            if (in_array($distrStatus, [FactoryOrder::DIST_STATUS_RETURNED])) {
                $hasDeliveryList[] = $item;
                // 异步保存订单
                dispatch(new SaveFactoryOrdersJob($item['shop_id'], [$platformOrders[$item['distr_tid']]]));
            }
        }

        if (!empty($hasDeliveryList)) {
            throw new PrintException($hasDeliveryList, 1004, '打印列表中有已发货');
        }

        if (!empty($hasWaybillCodeList)) {
            throw new PrintException($hasWaybillCodeList, 1003, '打印列表中有已打印订单');
        }
    }

    public function getPrintData($userId, $shopId, $data)
    {
        $templateObj = Template::query()->findOrFail($data['template_id']);
        $company = $templateObj->company;
        $template = $templateObj->toArray();
        $user = User::query()->find($userId);
        $shop = Shop::query()->find($shopId);

        // 获取网点地址和发货地址
        $sender = OrderPrintService::getBranchAndSendAddress($userId, $shopId, $company, $template);
        $senderAddressBo = new SenderAddressBo($sender['branch_address']);

        // 电子面单账号
        $waybillAuth = OderLogicService::getWaybillAuth($template, $company);

        // 提取老订单
        $oldPacks = array_filter($data['print_params'], function ($item) {
            return !empty($item['waybill_code']);
        });
        // 提取新订单
        $newPacks = array_filter($data['print_params'], function ($item) {
            return empty($item['waybill_code']);
        });


        $printOldList = $printNewList = $failPrintOrderVoList = [];
        // 取老单号
        if (!empty($oldPacks)) {
            $printOldList = $this->getPrintOld($user, $shop, $data['batch_no'], $oldPacks, $template, $waybillAuth, $senderAddressBo,$failPrintOrderVoList);
        }

        // 取新单号
        if (!empty($newPacks)) {
            $printNewList = $this->getPrintNew($user, $shop, $data['batch_no'], $newPacks, $company, $template, $waybillAuth, $senderAddressBo,$failPrintOrderVoList);
        }

        return array_merge($printOldList, $printNewList, $failPrintOrderVoList);
    }

    /**
     * @return AbstractOrderService
     */
    public function getOrderService(): AbstractOrderService
    {
        return $this->orderService;
    }

    /**
     * @param $userId
     * @param $shopId
     */
    public function setOrderService($userId, $shopId): void
    {
        $orderService = OrderServiceManager::create(config('app.platform'));
        $orderService->setUserId($userId);
        $shop = Shop::find($shopId);
        $orderService->setShop($shop);
        $this->orderService = $orderService;
    }

    /**
     * 判断订单是否有打印订单
     * @param array $orderIds
     * <AUTHOR>
     */
    public function existPrintedOrder(array $orderIds): bool
    {
        return WaybillHistory::query()
            ->where('waybill_status', WaybillHistory::WAYBILL_RECOVERY_NO)
            ->whereIn('order_id', $orderIds)
            ->where('order_type', WaybillHistory::ORDER_TYPE_FACTORY)
            ->exists();
    }

    /**
     * 检查网点状态
     * @param $company
     * @param $getWaybillNum
     * @throws ApiException
     * <AUTHOR>
     */
    public function checkCompany($company, $getWaybillNum): void
    {
        //判断是否是虚拟分享网点，且余额不为0，状态是正常
        if ($company->source == Company::SOURCE_COMPANY_STATUS_YES) {
            if ($company->source_status == Company::SOURCE_COMPANY_STATUS_CLOSED) {
                throw new ApiException(ErrorConst::WAYBILL_SHARE_FREEZE);
            }
            if ($company->source_status == Company::SOURCE_COMPANY_STATUS_OPEN &&
                $company->quantity == Company::INIT_QUANTITY) {
                throw new ApiException(ErrorConst::WAYBILL_SHARE_INSUFFICIENT_BALANCE);
            }
            if ($getWaybillNum > $company['quantity'] && $company['quantity'] !== Company::INIT_UNLIMITE_QUANTITY) {
                throw new ApiException(ErrorConst::WAYBILL_INSUFFICIENT_BALANCE);
            }
        }
    }

    /**
     * @param int $userId
     * @param int $shopId
     * @param array $dbOrders
     * @param array $packs
     * @param bool $isPrintCancel
     * @return array
     * <AUTHOR>
     */
    public function handleOrderFiller(int $userId, int $shopId, array $dbOrders, array $packs, bool $isPrintCancel): array
    {
        //过滤订单
        $orderService = OrderServiceManager::create();
        $orderService->setUserId($userId);
        $orderService->setShop(Shop::find($shopId));
        $platformOrders = $orderService->batchGetFactoryOrderInfo($dbOrders);
        $platformOrders = array_column($platformOrders, null, 'distr_tid');
        $canPrintPacks = [];
        $printPackVoList = [];

        foreach ($packs as $pack) {
            try {
                foreach ($pack['orders'] as $order) {
                    // 排除取消的订单
                    if (!$isPrintCancel) {
                        $id = $order['id'];
                        if (isset($platformOrders[$dbOrders[$id]['distr_tid']]) && $platformOrders[$dbOrders[$id]['distr_tid']]['distr_status'] == FactoryOrder::DIST_STATUS_CANCELLED) {
                            dispatch(new SaveFactoryOrdersJob($shopId, [$platformOrders[$id]]));
                            throw new ApiException(ErrorConst::ORDER_DIST_CANCEL);
                        }
                        if (!isset($dbOrders[$id])) {
                            throw new ApiException(ErrorConst::ORDER_NOT_FOUND);
                        }
                        if ($dbOrders[$id]['distr_status'] == FactoryOrder::DIST_STATUS_CANCELLED) {
                            throw new ApiException(ErrorConst::ORDER_DIST_CANCEL);
                        }
                    }
                }
                $canPrintPacks[] = $pack;
            } catch (\Exception $e) {
                $printPackVoList[] = (new PrintPackVo())->setByPack($pack)->setError([$e->getCode(), $e->getMessage()]);
            }
        }
        return array($canPrintPacks, $printPackVoList);
    }

    /**
     * @param array $packs
     * @param array $dbOrders
     * @param $mode
     * <AUTHOR>
     */
    public function appendRequestId(array $packs, array $dbOrders)
    {
        foreach ($packs as $index => $pack) {
            $tidArr = [];
            $idArr = array_pluck($pack['orders'], 'id');
            foreach ($idArr as $id) {
                $tidArr[] = $dbOrders[$id]['distr_tid'];
            }
            $tids = implode(',', $tidArr);
            $firstOid = $pack['orders'][0]['id'];
            $userId = 0;
            $shopId = $dbOrders[$firstOid]['shop_id'];
            //$ids = implode(',', $idArr);
            //查询是否有取号中的记录
//                $idStr = handleOrderIdStr($order);
//                $oidArr  = collect(explode('_', $idStr))->sort()->all();
            $new_num = 1;
            $packageInfos = Package::query()
                ->where(['user_id' => $userId, 'shop_id' => $shopId, 'tids' => $tids, 'waybill_status' => Package::WAYBILL_STATUS_DOING])
                ->orderBy('id', 'desc')
                ->limit($new_num)
                ->get();
            $counter = $packageInfos->count();
            $requestIdArr = $packageInfos->pluck('id')->toArray();
            // 补齐数量
            while ($counter < $new_num) {
                //没有则创建
                $package = Package::create([
                    'tids' => $tids,
                    'waybill_status' => Package::WAYBILL_STATUS_DOING,
                    'user_id' => $userId,
                    'shop_id' => $shopId,
                ]);
                foreach ($idArr as $id) {
                    PackageOrder::create([
                        'order_id' => $id,
                        'package_id' => $package->id,
                    ]);
                }
                $counter++;
                $requestIdArr[] = $package->id;
            }
            $packs[$index]['request_ids'] = $requestIdArr;
        }
        return $packs;
    }

    /**
     * 获取请求打印的 printPacksBo
     * @param array $canPrintPacks
     * @param string $mode
     * @return PrintPackBo[]
     * <AUTHOR>
     */
    public function getPrintPacksBoList(array $canPrintPacks, $orderInfos): array
    {
        //先创建 package and request_id
        $canPrintPacks = $this->appendRequestId($canPrintPacks, $orderInfos);
        // 填装订单数据
        foreach ($canPrintPacks as $index => $pack) {
            foreach ($pack['orders'] as $oIndex => $order) {
                $canPrintPacks[$index]['order_infos'][$oIndex] = $orderInfos[$order['id']];
            }
        }
        $printPacks = [];
        foreach ($canPrintPacks as $index => $pack) {
            foreach ($pack['orders'] as $oIndex => $order) {
                $canPrintPacks[$index]['order_infos'][$oIndex] = $orderInfos[$order['id']];
            }
            $new_num = 1;
            for ($i = 0; $i < $new_num; $i++) {
                $tempPack = $pack;
                if (!empty($tempPack['request_ids'])) {
                    $tempPack['request_id'] = $tempPack['request_ids'][$i];
                    $tempPack['package_id'] = $tempPack['request_ids'][$i];
                }
                $printPackBo = new PrintPackBo($tempPack);
                $printPackBo->copyByArr($tempPack);
                $printPacks[] = $printPackBo;
            }
        }
        return $printPacks;
    }

    /**
     * 获取请求打印的 PrintWaybillBo
     * @param array $canPrintPacks
     * @param $orderInfos
     * @param $template
     * @return PrintWaybillBo[]
     * <AUTHOR>
     */
    public function getPrintWaybillBoListByOld(array $canPrintPacks, $orderInfos, $template): array
    {
        $list = [];
        foreach ($canPrintPacks as $index => $pack) {
            $printWaybillBo = new PrintWaybillBo();
            // 填装订单数据
            foreach ($pack['orders'] as $order) {
                $printWaybillBo->order_infos[] = $orderInfos[$order['id']]??null;
            }
            $printWaybillBo->origin_orders = $pack['orders'];
            $printWaybillBo->index = $pack['index'];

//            $printPackBo = new PrintPackBo($canPrintPacks[$index]);
//            foreach ($pack['waybills'] as $waybill) {
//                $printPackBo->appendToWaybills(new WaybillBo($waybill));
                $waybill['wp_code'] = $template['wp_code'];
                $waybill['waybill_code'] = $pack['waybill_code'];
                $printWaybillBo->setWaybillBo(new WaybillBo($waybill));
                $list[] = clone $printWaybillBo;
//            }

        }
        return $list;
    }

    /**
     * 获取打印数据 并扣余额
     * @param $company
     * @param array $template
     * @param $accessToken
     * @param SenderAddressBo $branchAddressBo
     * @param array $printPacks
     * @param $getWaybillNum
     * @return PrintDataPackBo[]
     * <AUTHOR>
     */
    public function getPrintDataPackBoList($company, array $template, $accessToken, SenderAddressBo $branchAddressBo,
                                           array $printPacks, $getWaybillNum)
    {
        if ($company['source'] == Company::SOURCE_COMPANY_STATUS_YES) {
            $waybillService = WaybillServiceManager::init($template['auth_source'], $accessToken);
            $printDataPackBoList = $waybillService->assemFactoryWaybillPackages($branchAddressBo, $printPacks, $template);
            // todo 扣余额要改成新的
            //单号余额非无限量，取号后，电子面单余额数量减少，已用面单数量增加
            if ($company['quantity'] !== Company::INIT_UNLIMITE_QUANTITY) {
                $query = Company::where('id', $company['id']);
                $query->decrement('quantity', $getWaybillNum);
                $query->increment('allocated_quantity', $getWaybillNum);
            } else {
                //无限量的不用减少电子面单余额数量，已用面单数量增加
                $query = Company::where('id', $company['id']);
                $query->increment('allocated_quantity', $getWaybillNum);
            }
        } else {
            //加盟快递公司余额减少，实际余额以command更新为准
            $quanData = ['allocated_quantity' => DB::raw("allocated_quantity + " . $getWaybillNum)];
            if (!in_array($company['wp_code'], Company::ZHI_YING_COMPANY_LIST)) {
                $quanData = array_merge($quanData, ['quantity' => DB::raw("quantity - " . $getWaybillNum)]);
            }
            Company::query()->where('source', Company::SOURCE_COMPANY_STATUS_NO)
                ->where('auth_source', $company['auth_source'])
                ->where('owner_id', $company['owner_id'])
                ->where('branch_code', $company['branch_code'])
                ->where('wp_code', $company['wp_code'])
                ->update($quanData);

            //非虚拟网点，正常取号
            $waybillService = WaybillServiceManager::init($template['auth_source'], $accessToken);
            $printDataPackBoList = $waybillService->assemFactoryWaybillPackages($branchAddressBo, $printPacks, $template);
        }
        return $printDataPackBoList;
    }

    /**
     * @param $user
     * @param $shop
     * @param $batchNo
     * @param $newPacks
     * @param $company
     * @param $template
     * @param $waybillAuth
     * @param SenderAddressBo $senderAddressBo
     * @param $failPrintPackVoList
     * @return array
     * @throws ApiException
     */
    public function getPrintNew($user, $shop, $batchNo, $newPacks, $company, $template, $waybillAuth, SenderAddressBo $senderAddressBo, &$failPrintPackVoList): array
    {
        $userId = $user->id;
        $shopId = $shop->id;
        if (empty($newPacks)) {
            throw new ApiException(ErrorConst::ORDER_DATA_EMPTY);
        }
        $newOrderIds = array_merge(...array_pluck($newPacks, 'orders.*.id'));
        $orderInfos = FactoryOrder::query()
            ->whereIn('id', $newOrderIds)
            ->get()
            ->toArray();
        if (empty($orderInfos)) {
            throw new ApiException(ErrorConst::ORDER_NOT_FOUND);
        }
        $orderInfos = array_pluck($orderInfos, null, 'id');

        // 过滤订单
        list($canPrintPacks, $printPackVoList) = $this->handleOrderFiller($userId, $shopId, $orderInfos, $newPacks, false);
        if (empty($canPrintPacks)) {
            throw new ApiException(ErrorConst::ORDER_PRINT_DATA_EMPTY);
        }
        // 计算单号数量
        $packageNum = array_sum(array_pluck($newPacks, 'new_num'));
        // 检查网点状态
        $this->checkCompany($company, $packageNum);

        // 获取请求打印的 printPacksBo
        $printPacksBoList = $this->getPrintPacksBoList($canPrintPacks, $orderInfos);
        // 获取打印数据(取号) 并扣余额
        $printDataPackBoList = $this->getPrintDataPackBoList($company, $template, $waybillAuth->access_token,
            $senderAddressBo, $printPacksBoList, $packageNum);
        // 循环组装打印数据，并写入打印记录
        foreach ($printDataPackBoList as $index => $printDataPackBo) {
            if ($printDataPackBo->hasError()) {
//                    $printPackVo = $this->getPrintOrderVoByaWybillsPrintDataBo($waybillBo, $waybillsPrintDataBo, $printWaybillBo);
                $printPackVo = new PrintPackVo();
                $printPackVo->setByPrintDataPackBo($printDataPackBo);
                $printPackVo->setError($printDataPackBo->getError());
                $failPrintPackVoList[] = $printPackVo;
                continue;
            }
            $index = $this->getBatchNoIndex($shopId, $batchNo);
            $tempBatchNo = $batchNo . '-' . $index;
            $waybillsPrintDataBo = $printDataPackBo->getWaybillsPrintData();
            $tid_oids = [];
            foreach ($printDataPackBo->order_infos as $order_info) {
                $tid_oids[] = [
                    'id' => $order_info['id'],
                    'subIds' => [],
                ];
            }
            // 拼接打印数据
            $tempPrintData = PrintDataService::factoryTemplateData($shopId, $senderAddressBo, $printDataPackBo, $template,
                $index + 1, $packageNum, []);
            list($printData, $print_data_items) = $this->getPrintDataAndPrintDataItems($tempPrintData);
            DB::transaction(function () use (
                $waybillsPrintDataBo, $printDataPackBo, $template, $tempBatchNo, $tid_oids,
                $print_data_items, $tempPrintData, $user, $shop, &$printPackVoList
            ) {
                $p = Package::query()
                    ->where(['id' => $printDataPackBo->package_id])
                    ->update([
                        'user_id' => 0,
                        'shop_id' => $printDataPackBo->getFirstOrderShopId(),
                        'waybill_code' => $printDataPackBo->waybill_code,
                        //'tids'         => $tids,
                        'wp_code' => $template['wp_code'],
                        'template_id' => $template['id'],
                        'auth_source' => $template['auth_source'],
                        'batch_no' => $tempBatchNo,
                        'tid_oids' => json_encode($tid_oids),
                        'waybill_status' => Package::WAYBILL_STATUS_SUCCESS,
                    ]);
                foreach ($printDataPackBo->order_infos as $order_info) {
                    FactoryOrder::query()->where('id', $order_info['id'])->update([
                        // 修改打印中状态
                        'print_status' => Order::PRINT_STATUS_NO,
                        'print_num' => DB::raw('print_num + 1')
                    ]);

                    if ($template['auth_source'] == Waybill::AUTH_SOURCE_DY) {
                        $print_data = json_encode([]);
                    } else {
                        $print_data = $waybillsPrintDataBo->encrypted_data;
                    }
                    $history = WaybillHistory::create([
                        'user_id' => 0,
                        'shop_id' => $order_info['shop_id'],
                        'order_id' => $order_info['id'],
                        'package_id' => $waybillsPrintDataBo->package_id,
                        'order_no' => $order_info['distr_oid'],
                        'template_id' => $template['id'],
                        'auth_source' => $template['auth_source'],
                        'source' => $company['source'] ?? '',
                        'source_userid' => $company['source_userid'] ?? '',
                        'parent_waybill_code' => $waybillsPrintDataBo->parent_waybill_code,
                        'waybill_code' => $waybillsPrintDataBo->waybill_code,
                        'wp_code' => $template['wp_code'],
                        'print_data' => $print_data,
                        'receiver_province' => $order_info['receiver_state'],
                        'receiver_city' => $order_info['receiver_city'],
                        'receiver_district' => $order_info['receiver_district'],
                        'receiver_name' => '',
                        'receiver_phone' => '',
                        'receiver_address' => '',
                        'extra' => json_encode([]),
                        'print_data_items' => $print_data_items,
                    ]);

                    PrintRecord::create([
                        'user_id' => 0,
                        'shop_id' => $order_info['shop_id'],
                        'order_id' => $order_info['id'],
                        'package_id' => $waybillsPrintDataBo->package_id,
                        'history_id' => $history->id,
                        'order_no' => $order_info['distr_oid'],
                        'waybill_code' => $waybillsPrintDataBo->waybill_code,
                        'wp_code' => $template['wp_code'],
                        'receiver_province' => $order_info['receiver_state'],
                        'receiver_city' => $order_info['receiver_city'],
                        'receiver_district' => $order_info['receiver_district'],
                        'receiver_name' => '',
                        'receiver_phone' => '',
                        'receiver_address' => '',
                        'receiver_zip' => 0,
                        'buyer_remark' => '',
                        'print_data' => json_encode($tempPrintData),
                        'batch_no' => $tempBatchNo
                    ]);

                    $tmpInfoArr = [
                        'id' => $order_info['id'],
                        'oid' => $order_info['distr_oid'],
                        'package_id' => $history['package_id'],
                        'waybill_code' => $history['waybill_code'],
                        'wp_code' => $history['wp_code'],
                    ];
                }
            });
            $printPackVo = new PrintPackVo();
            $printPackVo->setByPrintDataPackBo($printDataPackBo);
            $printPackVo->to_print_content = $printData;
            $printPackVoList[] = $printPackVo;

        }
        return $printPackVoList;
    }

    /**
     * 区域筛选
     * @param Builder $query
     * @param $addressGroupId
     * <AUTHOR>
     */
    public function buildByArea(Builder $query, $addressGroupId): void
    {
        //区域筛选
        if ($addressGroupId > 0) {
            $area = QueryArea::query()->findOrFail($addressGroupId);
            $provinceArr = explode(',', $area->province_str);
            $cityArr = explode(',', $area->city_str);
            $districtArr = explode(',', $area->district_str);
            $query->whereIn('receiver_state', $provinceArr);
            $query->whereIn('receiver_city', $cityArr);
            $query->whereIn('receiver_district', $districtArr);
        }
    }

    /**
     * @param Builder $query
     * @param $tab_flag
     * <AUTHOR>
     */
    public function buildByTabFlag(Builder $query, $tab_flag): void
    {
        switch ($tab_flag) {
            case 0: //未打印
                $query->whereNull('locked_at');
                break;
            case 1: //已打印
                $query->whereNull('locked_at');
                break;
            case 2: //已打印有退款
                $query->whereNull('locked_at');
                break;
            case 3: //已发货
                $query->whereNull('locked_at');
                break;
            case 4: //全部
                $query->whereNull('locked_at');
                break;
            case 5: //已锁单
                $query->whereNotNull('locked_at');
                break;
        }
    }

    /**
     * @param Builder $query
     * @param $print_status
     * <AUTHOR>
     */
    public function buildByPrintStatus(Builder $query, $print_status): void
    {
        //打印状态
        switch ($print_status) {
            case FactoryOrder::PRINT_STATUS_YES:
            case FactoryOrder::PRINT_STATUS_NO:
                $query->where('print_status', $print_status);
                break;
            case FactoryOrder::PRINT_STATUS_ONE:
                $query->where('print_num', 1);
                break;
            case FactoryOrder::PRINT_STATUS_MORE:
                $query->where('print_num', '>', 1);
                break;
//            case FactoryOrder::PRINT_STATUS_SHIPPING:
//                $query->whereNotNull('print_shipping_at');
//                break;
            default:
                break;
        }
    }

    /**
     * @param Builder $query
     * @param $quickFilterValue
     * @param bool $onlyShowMergeOrder
     * @return array
     * <AUTHOR>
     */
    public function buildByQuickFilter(Builder $query, $quickFilterValue): array
    {
        $notShowMergeOrder = false;  //非合单
        $onlyShowMergeOrder = false; //合并订单

        //快捷筛选
        if ($quickFilterValue) {
            switch ($quickFilterValue) {
                case '1': //合并订单
                    $onlyShowMergeOrder = true;
                    break;
                case '2': //非合单
                    $notShowMergeOrder = true;
                    break;
                case '12': //有售后
                	$query->where('aftersale_status', FactoryOrder::REFUND_STATUS_YES);
                	break;
                case '13': //无售后
                	$query->where('aftersale_status', FactoryOrder::REFUND_STATUS_NO);
                	break;
                default:
                    break;
            }
        }
        return array($onlyShowMergeOrder, $notShowMergeOrder);
    }

    /**
     * @param Builder $query
     * @param $requestData
     * @return array
     */
    public function buildIndexQuery(Builder $query, $requestData)
    {
        $timeField = array_get($requestData, 'timeField', '');
        $beginAt = array_get($requestData, 'begin_at', '');
        $endAt = array_get($requestData, 'end_at', '');
        $checkedMsg = array_get($requestData, 'checkedMsg', '');
        $goods = array_get($requestData, 'goods', []);
        $goodsId = array_get($requestData, 'goodsId');
        $skuIdList = array_get($requestData, 'skuIdList');
        $goodsInclude = array_get($requestData, 'goodsInclude', '1');
        $selectItem = array_get($requestData, 'selectItem', 0);
        $search = array_get($requestData, 'search', '');
        $print_status = array_get($requestData, 'print_status', -1);   //打印状态
        $distr_status = array_get($requestData, 'distr_status', -1);   //分配状态
        $quickFilterValue = array_get($requestData, 'quickFilterValue', -1);//快捷筛选
        $addressGroupId = array_get($requestData, 'addressGroupId', 0);  //区域筛选， 0是不限
        $shopIdList = array_get($requestData, 'shopIdList', []);  //绑定店铺列表

        $this->handleSmartSearch($query, $requestData, $search, $selectItem);
        $this->buildByArea($query, $addressGroupId);
        $this->buildByPrintStatus($query, $print_status);
        $this->buildByOrderStatus($query, $distr_status, $quickFilterValue);

        if ($timeField && $beginAt && $endAt) {
            $query->where($timeField, '>=', date('Y-m-d H:i:s', strtotime($beginAt)));
            $query->where($timeField, '<=', date('Y-m-d H:i:s', strtotime($endAt)));
        }

        //留言、备注
        if ((int)$checkedMsg > 0) {
            switch ($checkedMsg) {
                case '1':
                    $query->where('buyer_message', '<>', '')
                        ->where('seller_memo', '[]');
                    break;
                case '2':
                    $query->where('buyer_message', '')
                        ->where('seller_memo', '<>', '[]');
                    break;
                case '3':
                    $query->where('buyer_message', '<>', '')
                        ->where('seller_memo', '<>', '[]');
                    break;
                case '4':
                    $query->where('buyer_message', '')
                        ->where('seller_memo', '[]');
                    break;
                default:
                    break;
            }
        }

        //商品筛选
        foreach ($goods as $k => $g) {
            if (!$g) {
                unset($goods[$k]); //去除空值
            }
        }
        $goods_id = collect($goods)->pluck('goods_id')->toArray();
        $goods_sku = collect($goods)->pluck('goods_sku')->toArray();
        if (count($goods) > 0 && (array_filter($goods_id) || array_filter($goods_sku))) {
            if ($goodsInclude == '1') { //包含
                $query->whereIn('goods_title', $goods_id)
                    ->orWhereIn('sku_value', $goods_sku)
                    ->orWhereIn('goods_id', $goods_id);
            } else if ($goodsInclude == '3') { //精确
                $query->whereIn('goods_title', $goods_id)
                    ->orWhereIn('sku_value', $goods_sku)
                    ->orWhereIn('goods_id', $goods_id);
            } else { //不包含
                $query->whereNotIn('goods_title', $goods_id)->orWhereNotIn('goods_id', $goods_id)->orWhereNotIn('sku_value', $goods_sku);
            }
        }

        if ($goodsId) {
            $query->where('goods_id', $goodsId);
        }
        if ($skuIdList) {
            $query->where('sku_id', $skuIdList);
        }

        if ($shopIdList) {
            $query->whereIn('distr_shop_id', $shopIdList);
        }
    }

    /**
     * @param Builder $query
     * @param string $groupColumn
     * @param $sort
     * @param int $limit
     * @param int $offset
     * @return array
     * <AUTHOR>
     */
    public function handleOrderSingle(Builder $query, array $data, string $groupColumn, array $sort, int $limit, int $offset): array
    {
        $queryBase = clone $query;
        list($orderBy, $orderSort) = $sort;

//        $maxOrder = "MAX(orders.$sort)";
//        if ($orderSort == 'asc') {
//            $maxOrder = "MIN(orders.$sort)";
//        }
//        $queryBase->select([$groupColumn])
//            ->orderBy($orderBy, $orderSort)
//            ->limit(10000);

//        $queryGroup = \DB::query()->fromSub($queryBase->getQuery(), 'base');
//        $selectArr = [
//            $groupColumn,
//            'group_concat(id) as ids',
//            'COUNT(distinct id) as merge_orders_num',
//            "$maxOrder as sort_column",
//            'count(distinct num_iid) as unique_goods_num',
//            'count(distinct sku_id) as unique_sku_num',
//            'sum(num) as goods_sum_num',
//            'sum(sku_num) as sku_sum_num',
//            'count(distinct receiver_address) as unique_address_num',
//            'count(address_md5) as address_md5_num',
//            'sum(payment) as merge_orders_payment',
//            'sum(total_fee) as merge_orders_total_fee'
//        ];
//        $queryGroup->select($selectArr)->groupBy([$groupColumn]);
//        $count = \DB::query()->fromSub($queryGroup, 'g')->count();
//        $result = \DB::query()->fromSub($queryGroup, 't')
//            ->orderBy($orderBy, $orderSort)
//            ->limit($limit)->offset($offset)->get(['ids']);
//        $orderIdArr = [];
//        collect($result)->pluck('ids')->map(function ($item) use (&$orderIdArr) {
//            $arr = explode(',', $item);
//            $orderIdArr = array_merge($orderIdArr, $arr);
//        });
        $count = $queryBase->count();
        $query = $queryBase
            ->limit($limit)
            ->offset($offset)
            ->orderBy($orderBy, $orderSort);
        return array($query, $count);
    }

    /**
     * @param Builder $query
     * @param string $groupColumn
     * @param $sort
     * @param int $limit
     * @param int $offset
     * @return array
     * <AUTHOR>
     */
    public function handleOrderGroup(Builder $query, array $requestData, bool $onlyShowMergeOrder, bool $notShowMergeOrder, string $groupColumn, array $sort, int $limit, int $offset): array
    {
        $queryBase = clone $query;
        list($orderBy, $orderSort) = $sort;
        $maxOrder = "MAX($orderBy)";
        if ($orderSort == 'asc') {
            $maxOrder = "MIN($orderBy)";
        }

        $queryBase->select('*')
            ->orderBy($orderBy, $orderSort)
            ->limit(10000);

        $queryGroup = \DB::query()->fromSub($queryBase->getQuery(), 'orders');
        $selectArr = [
            $groupColumn,
            'group_concat(id) as ids',
            'COUNT(distinct id) as merge_orders_num',
            "$maxOrder as sort_column",
            'count(distinct goods_id) as unique_goods_num',
            'count(distinct sku_id) as unique_sku_num',
            'sum(goods_num) as goods_sum_num',
            'sum(goods_num) as sku_sum_num',
            'count(distinct receiver_id) as unique_address_num',
            'sum(goods_total_price) as merge_orders_total_fee'
        ];
        $queryGroup->selectRaw(implode(',', $selectArr))->groupBy([$groupColumn]);
        $count = \DB::query()->fromSub($queryGroup, 'g')->count();
        $query = \DB::query()->fromSub($queryGroup, 't');

        //筛出合并订单
        if ($onlyShowMergeOrder) {
            $query->where('merge_orders_num', '>', 1);
        }
        //筛出非合并订单
        if ($notShowMergeOrder) {
            $query->where('merge_orders_num', '=', 1);
        }

        //商品数量种类
        $goodSkuNumType = array_get($requestData, 'goodSkuNumType', '');
        if ($goodSkuNumType == 1) { //单商品单规格单件
            $query->where([
                ['unique_goods_num', '=', 1],
                ['unique_sku_num', '=', 1],
                ['merge_orders_num', '=', 1],
                ['goods_sum_num', '=', 1]
            ]);
        } else if ($goodSkuNumType == 2) { //单商品单规格多件
            $query->where([
                ['unique_goods_num', '=', 1],
                ['unique_sku_num', '=', 1],
                ['goods_sum_num', '>', 1]
            ]);
        } else if ($goodSkuNumType == 3) {//单商品多规格多件
            $query->where([
                ['unique_goods_num', '=', 1],
                ['unique_sku_num', '>', 1],
                ['goods_sum_num', '>', 1]
            ]);
        } else if ($goodSkuNumType == 4) {//多商品多规格多件
            $query->where([
                ['unique_goods_num', '>', 1],
                ['unique_sku_num', '>', 1]
            ]);
        }

        // 实付金额(商品总价格)
        $search = array_get($requestData, 'search', '');
        $selectItem = array_get($requestData, 'selectItem', '');
        if ($selectItem == 10 && $search) {
            $query->where('goods_total_price', $search);
        }

        //订单金额
        if ($selectItem == 11 && $search) {
            $query->where('goods_total_price', $search);
        }

        //商品数量
        if ($selectItem == 12 && $search) {
            if (is_numeric($search)) {
                $goodsNum = $search;
            } else {
                $searchArr = explode('-', $search);
                $goodsNum = $searchArr;
            }

            if ($goodsNum == 1) {
                $query->where('goods_sum_num', 1);
                $query->where('merge_orders_num', 1);
            } else if (is_Array($goodsNum)) {
                $query->where('goods_sum_num', '>=', $goodsNum[0]);
                $query->where('goods_sum_num', '<=', $goodsNum[0]);
            } else {
                $query->where('goods_sum_num', $goodsNum);
            }
        }

        //订单数量
        if ($selectItem == 17 && $search) {
            // 订单数量
            if (strpos($search, '-')) {
                list($left, $right) = explode('-', $search);
                $ordersNum = [$left, $right];
            } else {
                $ordersNum = $search;
            }
            if (is_Array($ordersNum)) {
                $query->where('merge_orders_num', '>=', $ordersNum[0]);
                $query->where('merge_orders_num', '<=', $ordersNum[0]);
            } else {
                $query->where('merge_orders_num', '>=', $ordersNum);
            }
        }

        $result = $query->orderBy('sort_column', $orderSort)->limit($limit)->offset($offset)->get(['ids']);
        $orderIdArr = [];
        collect($result)->pluck('ids')->map(function ($item) use (&$orderIdArr) {
            $arr = explode(',', $item);
            $orderIdArr = array_merge($orderIdArr, $arr);
        });
        $query = FactoryOrder::query()
            ->orderBy($orderBy, $orderSort)
            ->whereIn('id', $orderIdArr);
        return array($query, $count);
    }

    public function handleMergeOrder($ret, $shop_id, string $groupColumn)
    {
        $list = [];
        if (empty($ret)) {
            return $list;
        }
        $ret = collect($ret)->toArray();
        $shopExtra = ShopExtra::query()->where([
            'shop_id' => $shop_id,
        ])->first();
        $tempPackageList = [];
        $groupArr = collect($ret)->groupBy($groupColumn)->toArray();
        $packageMax = $shopExtra->merge_order_num ?? ShopExtra::MERGE_ORDER_NUM_DEFAULT; // 每个包裹最大数量
        // 根据分组字段拆分
        foreach ($groupArr as $groupItem) {
            // 疑似合单标记
            $last_like_merge_flag = '';
            // 可合单标记
            $last_merge_flag = '';
            // 可合单标记索引
            $last_merge_flag_index = 0;
            if (count($groupItem) == 1) {
                $tempPackageList[] = $groupItem;
                continue;
            }
//            array_multisort(array_column($groupItem,$groupColumn),SORT_ASC, $groupItem);
            // 标记疑似合单和可合单
            foreach ($groupItem as $groupItemIndex => $groupItemValue) {
                $merge_flag_array = [
                    $groupItemValue['shop_id'],
//                    $groupItemValue['user_id'],
                    $groupItemValue['receiver_id'],
                ];
                if (PlatformConst::KS == config('app.platform')) {
                    // ks 可以跨店铺合单
                    $merge_flag_array = [
                        $groupItemValue['receiver_id'],
                    ];
                }
                $like_merge_flag = implode('-', $merge_flag_array);
//                $merge_flag = $like_merge_flag . '-' . $groupItemValue['receiver_address'];
                $merge_flag = $like_merge_flag;
                $merge_flag = md5($merge_flag);
                $groupItem[$groupItemIndex]['is_merge'] = false;
                $groupItem[$groupItemIndex]['is_like'] = false;
                if ($merge_flag == $last_merge_flag) {
                    $groupItem[$groupItemIndex]['is_merge'] = true;
                    $groupItem[$last_merge_flag_index]['is_merge'] = true;
                }
//                elseif ($like_merge_flag == $last_like_merge_flag) {
//                    $groupItem[$groupItemIndex]['is_like'] = true;
//                    $groupItem[$last_merge_flag_index]['is_like'] = true;
//                    $last_merge_flag_index = $groupItemIndex;
//                }
                else {
                    $last_merge_flag_index = $groupItemIndex;
                }
                if (empty($groupItem[$groupItemIndex]['merge_flag'])) {
                    $groupItem[$groupItemIndex]['merge_flag'] = $merge_flag;
                }
                $last_merge_flag = $merge_flag;
                $last_like_merge_flag = $like_merge_flag;
            }


            // 拆分出有退款成单独订单
//            foreach ($groupItem as $index => $item) {
//                if ($item['refund_status'] == 1) {
//                    $tempPackageList[] = [$item];
//                    unset($groupItem[$index]);
//                }
//            }
            // 拆分出可合单和不可合单
            $isMergeArr = collect($groupItem)->groupBy('is_merge')->toArray();
            // 不可合单的 拆分成单个
            if (!empty($isMergeArr[0])) {
                $array_chunk1 = array_chunk($isMergeArr[0], 1);
                array_push($tempPackageList, ...$array_chunk1);
            }
            // 可合单的
            if (!empty($isMergeArr[1])) {
                // 根据手动合单拆分
                $mergeFlagArr = collect($isMergeArr[1])->groupBy('merge_flag')->toArray();
                foreach ($mergeFlagArr as $i => $mergeFlagItem) {
                    // 根据包裹最大数拆分
                    $array_chunk2 = array_chunk($mergeFlagItem, $packageMax);
                    array_push($tempPackageList, ...$array_chunk2);
                }
            }

        }
        // 组装子订单数据
        foreach ($tempPackageList as $index => $groupItemValue) {
//                $tempItem = $item[0];
            $notDeliveredCount = count(array_filter($groupItemValue, function ($item) {
                if (!empty($item['return_at'])) {
                    return true;
                }
                return false;
            }));
            $packageAll = [];
            collect($groupItemValue)->pluck('packages')->each(function ($items) use (&$packageAll) {
                if (empty($items)) {
                    return true;
                }
                foreach ($items as $item) {
                    $tidOidArr = json_decode($item['tid_oids'], true);
                    $item['tid_oids'] = $tidOidArr;
                    $subArr = collect($tidOidArr)->pluck('subIds')->filter()->values()->toArray();
                    $subIdArr = [];
                    if (!empty($subArr) && !empty($subArr[0])) {
                        $subIdArr = array_merge(...$subArr);
                    }
                    $item['sub_id_arr'] = $subIdArr;
                    array_push($packageAll, $item);
                }
            });
            $packageAll = collect($packageAll)->unique('id')->sortBy('id')->values()->toArray();
            $mergeOrderCount = count($groupItemValue);
            $totalNum = array_sum(array_column($groupItemValue, 'num'));
            $totalPayment = array_sum(array_column($groupItemValue, 'payment'));
            $tempItem = array_shift($groupItemValue);
            // 有子订单，把主订单的可合并和疑似去掉
            //if (count($groupItemValue) > 0 || $tempItem['refund_status'] > 0) {
            if (count($groupItemValue) > 0) {
                $tempItem['is_merge'] = false;
                $tempItem['is_like'] = false;
            }
            $tempItem['package_all'] = $packageAll;
            $tempItem['not_delivered_count'] = $notDeliveredCount;
            $tempItem['merge_orders'] = $groupItemValue;
            $tempItem['merge_order_count'] = $mergeOrderCount;
            $tempItem['total_num'] = $totalNum;
            $tempItem['total_payment'] = $totalPayment;
            $list[] = $tempItem;
        }
        return $list;

    }

    public function handleOrderPackages($list)
    {
        $data = [];
        foreach ($list->toArray() as $index => $item) {
            $packageAll = [];
            foreach ($item['packages'] as $pIndex => $package) {
                $tidOidArr = json_decode($package['tid_oids'], true);
                $package['tid_oids'] = $tidOidArr;
                $subArr = collect($tidOidArr)->pluck('subIds')->filter()->values()->toArray();
                $subIdArr = [];
                if (!empty($subArr) && !empty($subArr[0])) {
                    $subIdArr = array_merge(...$subArr);
                }
                $package['sub_id_arr'] = $subIdArr;
                $packageAll[] = $package;
            }
            $item['package_all'] = $packageAll;
            $data[] = $item;
        }
        return $data;
    }

    /**
     * @param array $printData
     * @param array $tempPrintData
     * @return array
     * <AUTHOR>
     */
    public function getPrintDataAndPrintDataItems(array $tempPrintData): array
    {
        if (count($tempPrintData) == 2) {
            $printData[] = $tempPrintData[0];
            $printData[] = $tempPrintData[1];
            $print_data_items = json_encode($tempPrintData[1]['contents'][0]['data']['printNextItemBeans']);
        } else {
            $printData[] = $tempPrintData;
            $print_data_items = json_encode($tempPrintData['contents'][1]['data']['printNextItemBeans']);
        }
        return [$printData, $print_data_items];
    }

    /**
     * @param $user
     * @param $shop
     * @param $batchNo
     * @param $packs
     * @param $template
     * @param $waybillAuth
     * @param $senderAddressBo
     * @param $failPrintPackVoList
     * @return array
     * @throws ApiException
     */
    public function getPrintOld($user, $shop, $batchNo, $packs, $template, $waybillAuth, $senderAddressBo, &$failPrintPackVoList): array
    {
        $printPackVoList = [];
        if (empty($packs)) {
            throw new ApiException(ErrorConst::ORDER_DATA_EMPTY);
        }
        $waybillCodeArr = array_pluck($packs, 'waybill_code');
        $newOrderIdArr = array_merge(...array_pluck($packs, 'orders.*.id'));
        $orderInfos = FactoryOrder::query()
            ->whereIn('id', $newOrderIdArr)
            ->get()
            ->toArray();
        if (empty($orderInfos)) {
            throw new ApiException(ErrorConst::ORDER_DATA_EMPTY);
        }
        $orderInfos = array_pluck($orderInfos, null, 'id');

        $waybillHistoryAll = WaybillHistory::query()->where([
            'waybill_status' => WaybillHistory::WAYBILL_RECOVERY_NO
        ])->where('wp_code', $template['wp_code'])
            ->whereIn('waybill_code', $waybillCodeArr)
            ->get()
            ->pluck(null, 'waybill_code')
            ->toArray();

        $printWaybillBoList = $this->getPrintWaybillBoListByOld($packs, $orderInfos, $template);
        // 计算单号数量
        $totalPackageNum = 0;

        $waybillsPrintDataBoList = [];
        $waybillList = array_pluck($printWaybillBoList,'waybillBo');
        $waybillService = WaybillServiceManager::init($template['auth_source'], $waybillAuth->access_token);
        if ($template['auth_source'] == Waybill::AUTH_SOURCE_DY) {
            $waybillsPrintDataBoList = $waybillService->getPrintDataByWaybillBos($waybillList);
            $waybillsPrintDataBoList = array_pluck($waybillsPrintDataBoList, null, 'waybill_code');
        }

        foreach ($printWaybillBoList as $index => $printWaybillBo) {
            $error = null;
            foreach ($printWaybillBo->order_infos as $order) {
                if (empty($order)) {
                    $error = ErrorConst::ORDER_NOT_FOUND;
                    break;
                }
            }
            $waybillBo = $printWaybillBo->getWaybillBo();
            if (empty($waybillHistoryAll[$waybillBo->waybill_code])) {
                $error = ErrorConst::WAYBILL_CODE_NOT_FOUND;
            }
            if ($error) {
                $failPrintPackVoList[] = (new PrintPackVo())
                    ->setByPrintWaybillBo($printWaybillBo)
                    ->setError($error);
                continue;
            }
            $totalPackageNum ++;
            $printDataPackBo = new PrintDataPackBo();
            $printDataPackBo->copyByPrintWaybillBo($printWaybillBo);
            $history = $waybillHistoryAll[$waybillBo->waybill_code];
            // 抖音平台特殊处理
            if ($template['auth_source'] == Waybill::AUTH_SOURCE_DY) {
                /** @var WaybillsPrintDataBo $waybillsPrintDataBo */
                $waybillsPrintDataBo = $waybillsPrintDataBoList[$waybillBo->waybill_code];
                $print_data = $waybillsPrintDataBo->encrypted_data;
                // 面单有错
                if ($waybillsPrintDataBo->hasError()) {
                    $printPackVo = new PrintPackVo();
                    $printPackVo->setByPrintDataPackBo($printDataPackBo);
                    $printPackVo->setError($waybillsPrintDataBo->getError());
                    $failPrintPackVoList[] = $printPackVo;
                    continue;
                }
            } else {
                $print_data = $history['print_data'];
            }

            $waybillsPrintDataBo = new WaybillsPrintDataBo();
            $waybillsPrintDataBo->waybill_code = $waybillBo->waybill_code;
            $waybillsPrintDataBo->encrypted_data = $print_data;
            $printDataPackBo->setWaybillsPrintData($waybillsPrintDataBo);
            $printDataPackBo->wp_code = $template['wp_code'];
            $printDataPackBo->waybill_code = $waybillBo->waybill_code;

            $tempPrintData = PrintDataService::factoryTemplateData($shop->id, $senderAddressBo, $printDataPackBo,
                $template, $index + 1, $totalPackageNum, []);
            list($printData, $print_data_items) = $this->getPrintDataAndPrintDataItems($tempPrintData);

            DB::transaction(function () use (
                $printDataPackBo, $waybillsPrintDataBo, $history, $template,
                $tempPrintData, $batchNo, $user, $shop, &$printPackVoList
            ) {
                foreach ($printDataPackBo->order_infos as $order_info) {
                    FactoryOrder::query()->where('id', $order_info['id'])->update([
                        'print_num' => DB::raw('print_num + 1')
                    ]);

                    PrintRecord::create([
                        'user_id' => 0,
                        'shop_id' => $order_info['shop_id'],
                        'order_id' => $order_info['id'],
                        'package_id' => $waybillsPrintDataBo->package_id,
                        'history_id' => $history['id'],
                        'order_no' => $order_info['distr_oid'],
                        'waybill_code' => $waybillsPrintDataBo->waybill_code,
                        'wp_code' => $template['wp_code'],
                        'receiver_province' => $order_info['receiver_state'],
                        'receiver_city' => $order_info['receiver_city'],
                        'receiver_district' => $order_info['receiver_district'],
                        'receiver_name' => '',
                        'receiver_phone' => '',
                        'receiver_address' => '',
                        'receiver_zip' => 0,
                        'buyer_remark' => '',
                        'print_data' => json_encode($tempPrintData),
                        'batch_no' => $batchNo
                    ]);

                    $tmpInfoArr = [
                        'id' => $order_info['id'],
                        'oid' => $order_info['distr_oid'],
                        'package_id' => $history['package_id'],
                        'waybill_code' => $history['waybill_code'],
                        'wp_code' => $history['wp_code'],
                    ];

                    //记录操作表
                    //event((new OrderPrintEvent($user, $shop, time(), $tmpInfoArr))->setClientInfoByRequest(request()));
                }

            });

            $printPackVo = new PrintPackVo();
            $printPackVo->to_print_content = $printData;
            $printPackVoList[] = $printPackVo->setByPrintDataPackBo($printDataPackBo);
        }
        return $printPackVoList;
    }

    /**
     * 订单回传并发货
     * @param $delivers
     * <AUTHOR>
     */
    public function waybillReturn($delivers): array
    {
        $orderIds = array_pluck($delivers, 'order_id');
        $delivers = array_pluck($delivers, null, 'order_id');
        $orders = FactoryOrder::query()->whereIn('id', $orderIds)->get()->toArray();
        foreach ($orders as $index => $order) {
            $orders[$index]['waybill_code'] = $delivers[$order['id']]['waybill_code'];
            $orders[$index]['wp_code'] = $delivers[$order['id']]['wp_code'];
        }
        $orderService = OrderServiceManager::create();
        $list = $orderService->batchReturnFactoryOrder($orders);
        return $list;
    }

    public function waybillRecovery($waybillCodeList): array
    {
        $waybills = WaybillHistory::query()->whereIn('waybill_code', $waybillCodeList)->get();
        foreach ($waybills as $index => $waybill) {
            $order = FactoryOrder::query()->where('id', $waybill['order_id'])->first()->toArray();
            $waybills[$index]['distr_shop_id'] = $order['distr_shop_id'];
        }

        $orderService = OrderServiceManager::create();
        $list = $orderService->batchWaybillRecoveryFactoryOrder($waybills);
        //更改状态
        foreach ($list as $item) {
            if ($item['code'] == 200) {
                Package::where('id', $item->waybill_info['package_id'])
                    ->update([
                        'recycled_at' => Carbon::now()
                    ]);
            }
            WaybillHistory::query()
                ->where(['id'=>$item->waybill_info['id']])
                ->update(['waybill_status' => WaybillHistory::WAYBILL_RECOVERY_YES]);
        }
        return $list;
    }

    private function getBatchNoIndex($shopId, string $batchNoKey)
    {
        return Order::getBatchNoIndex($shopId, $batchNoKey);
    }

    /**
     * @param WaybillBo $waybill
     * @param $wp_code
     * @param WaybillsPrintDataBo $waybillsPrintDataBo
     * @param PrintWaybillBo $printWaybillBo
     * @param array $printData
     * @return PrintPackVo
     * <AUTHOR>
     */
    public function getPrintOrderVoByaWybillsPrintDataBo(WaybillBo      $waybill, WaybillsPrintDataBo $waybillsPrintDataBo,
                                                         PrintWaybillBo $printWaybillBo, array $printData = [])
    {
        $tmpInfoArr = [
            'waybill_code' => $waybill->waybill_code,
            'wp_code' => $waybill->wp_code,
        ];
        $printOrderVo = new PrintPackVo($tmpInfoArr);
        if ($waybillsPrintDataBo->hasError()) {
            $printOrderVo->setError($waybillsPrintDataBo->getError());
        }
        $printOrderVo->setOrderInfos($printWaybillBo->order_infos);
        $printOrderVo->setPrintData($printData);
        return $printOrderVo;
    }

    /**
     * @param array $packs
     * @param array $waybillHistoryAll
     * @return array[]
     * <AUTHOR>
     */
    public function handlePackFilterByOld(array $packs, array $waybillHistoryAll, $orderInfos, &$failPrintPackVoList)
    {
        $canPrintPacks = [];
        foreach ($packs as $index => $pack) {
            foreach ($pack['waybills'] as $waybill) {
                foreach ($pack['orders'] as $order) {
                    if (empty($orderInfos[$order['oid']])) {
                        $failPrintPackVo = (new PrintPackVo())->setByPack($pack)
                            ->setError(ErrorConst::ORDER_NOT_FOUND);
                        $failPrintPackVo->waybill_code = $waybill['waybill_code'];
                        break;
                    }
                }
                if (!empty($failPrintPackVo)) {
                    $failPrintPackVoList[] = $failPrintPackVo;
                    break;
                }
                if (empty($waybillHistoryAll[$waybill['waybill_code']])) {
                    $failPrintPackVo = (new PrintPackVo())->setByPack($pack)
                        ->setError(ErrorConst::WAYBILL_CODE_NOT_FOUND);
                    $failPrintPackVo->waybill_code = $waybill['waybill_code'];
                }
                if (!empty($failPrintPackVo)) {
                    $failPrintPackVoList[] = $failPrintPackVo;
                }
            }


            $canPrintPacks[] = $pack;
        }
        return $canPrintPacks;
    }

    public function notifyPrintResult($data)
    {
        foreach ($data as $item) {
            foreach ($item['ids'] as $id) {
                $order = FactoryOrder::findOrFail($id);
                FactoryOrder::query()
                    ->where('id', $id)
                    ->update([
                        'wp_code' => $item['wp_code'],
                        'waybill_code' => $item['waybill_code'],
                        'print_status' => FactoryOrder::PRINT_STATUS_YES,
                        'print_at' => date('Y-m-d H:i:s'),
                    ]);
            }

            $packageOrders = PackageOrder::where('order_id', $id)->get();
            $packageIds = collect($packageOrders)->pluck('package_id')->toArray();
            Package::query()
                ->where('shop_id', $order->shop_id)
                ->where('print_status', Package::PRINT_STATUS_NO)
                ->whereIn('id', $packageIds)
                ->update(['print_status' => Package::PRINT_STATUS_YES]);
            //写入打印序号
            PrintRecord::query()->where(['order_id' => $id, 'batch_no' => $item['batch_no']])->update([
                'print_index' => $item['index'],
                'print_count' => $item['total']
            ]);
        }
    }

    public function lock($data) :bool
    {
        $locked = array_get($data, 'locked', true);
        $orderIds = array_get($data, 'ids', true);
        foreach ($orderIds as $id) {
            $order = FactoryOrder::findOrFail($id);
            if ($locked) {
                $orderModel = FactoryOrder::query()->where([
//                    'user_id' => $order->user_id,
                    'locked_at' => null,
                    'id' => $id,
                ])->first();
                if (!empty($orderModel) && $orderModel->distr_status >= FactoryOrder::DIST_STATUS_RETURNED) {
                    throw new ApiException(ErrorConst::ORDER_LOCK_FAIL_BY_STATUS);
                }
                $orders = $orderModel->update(['locked_at' => Carbon::now()]);
                if (!$orders) {
                    return false;
                }
            } else {
                $orders = FactoryOrder::query()->where([
                    'shop_id' => $order->shop_id,
                ])->where('id', $id)->update(['locked_at' => null]);
                if (!$orders) {
                    return false;
                }
            }

        }
        return true;
    }

    public function getGoodsNameList($params)
    {
//        $shops = Shop::query()->whereIn('identifier', $params['ownerIdList'])->get();
        $shops = Shop::getListByIdentifiers($params['ownerIdList']);
        $shopIds = collect($shops)->pluck('id')->toArray();


        $timeField = $params['timeField'];
        $beginAt = $params['begin_at'];
        $endAt = $params['end_at'];

        $condition = [];
        if ($timeField && $beginAt && $endAt) {
            $condition[] = ['factory_orders.' . $timeField, '>=', date('Y-m-d H:i:s', strtotime($beginAt))];
            $condition[] = ['factory_orders.' . $timeField, '<=', date('Y-m-d H:i:s', strtotime($endAt))];
        }

        //订单状态，未发货，已发货
        $query = FactoryOrder::query()
            ->whereIn('factory_orders.shop_id', $shopIds)
            ->whereIn('factory_orders.distr_status', [FactoryOrder::DIST_STATUS_ASSIGNED])
            //->whereIn('factory_orders.aftersale_status', [FactoryOrder::REFUND_STATUS_NO])
            ->where($condition);
        //是否显示锁定订单
        if (!request()->input('includeLocked')) {
            $query->whereNull('factory_orders.locked_at');
        }

        $maxLimit = 5000; //订单筛出规格最大限制数量
        $list = $query->groupBy(['sku_id'])
            ->orderBy('to_shipped_count', 'desc')
            ->limit($maxLimit)
            ->get([
                'factory_orders.sku_id',
                'factory_orders.outer_goods_id',
                'factory_orders.outer_sku_id',
                DB::raw('max(factory_orders.sku_value) as sku_value'),
                DB::raw('max(factory_orders.goods_id) as goods_id'),
                DB::raw('max(factory_orders.goods_title) as goods_title'),
                DB::raw('count(*) as to_shipped_count')
            ])->toArray();

        //相同商品的合并到一块
        $list = collect($list)->groupBy(['goods_id']);

        $result =[];
        foreach ($list as $goods) {
            $temp = [];
            $toShippedCount = 0;
            foreach ($goods as $item) {
                $tempToShippedCount = $item['to_shipped_count'];
                $temp[] = [
                    'label' => $item['sku_value'],
                    'sku_value' => $item['sku_value'] ?? '',
                    'sku_id' => $item['sku_id'] ?? '',
                    'custom_sku_value' => '',
                    'outer_sku_id' => $item['outer_sku_id'] ?? '',
                    'goods_pic' => '',
                    'to_shipped_count' => $tempToShippedCount
                ];
                $toShippedCount += $tempToShippedCount;
            }

            $result[] = [
                'label' => $goods[0]['goods_title'] ?? '',
                'goods_title' => $goods[0]['goods_title'] ?? '',
                'custom_title' => '',
                'outer_goods_id' => $goods[0]['outer_goods_id'] ?? '',
                'children' => $temp,
                'to_shipped_count' => $toShippedCount,
                'goods_id' => $goods[0]['goods_id'] ?? '',
                'goods_pic' => '',
            ];
        }

        return $result;
    }

    public function getBindShopList($shopId)
    {
        $ret = [];
        $shopBindArr = ShopBind::query()->where(['type'=>ShopBind::TYPE_AGENT_PRINT_FACTORY_SHOP, 'f_shop_id'=>$shopId])->get();
        if (!empty($shopBindArr)) {
            $oShopIdArr = collect($shopBindArr)->pluck('o_shop_id');
            $oshopArr = Shop::query()->whereIn('id', $oShopIdArr)->get();
            foreach ($oshopArr as $item) {
                $ret[] = [
                    'name'       => $item['name'],
                    'shop_name'  => $item['shop_name'],
                    'identifier' => $item['identifier']
                ];
            }
        }

        return $ret;
    }

    public function buildByOrderStatus($query, $distr_status, $quickFilterValue)
    {
        if ($distr_status > 0) {
            switch ($distr_status) {
                case FactoryOrder::DIST_STATUS_ASSIGNED:
                    if ($quickFilterValue == '12') {
                        $query->whereNull('return_at');
                    } else {
                        $query->whereIn('distr_status', [
                            FactoryOrder::DIST_STATUS_ASSIGNED
                        ]);
                    }
                    break;
                case FactoryOrder::DIST_STATUS_RETURNED:
                    $query->whereNotNull('return_at');
                    $query->whereIn('distr_status', [FactoryOrder::DIST_STATUS_RETURNED]);
                    break;
                default:
                    if ($quickFilterValue != '12') {
                        $query->where('distr_status', $distr_status);
                    }
                    break;
            }
        } else {
            //全部订单状态
            $query->whereIn('distr_status', [FactoryOrder::DIST_STATUS_ASSIGNED,FactoryOrder::DIST_STATUS_RETURNED,FactoryOrder::DIST_STATUS_CANCELLED]);
        }
    }
}
