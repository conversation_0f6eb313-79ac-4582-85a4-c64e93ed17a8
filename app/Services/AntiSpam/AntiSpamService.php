<?php

namespace App\Services\AntiSpam;

use App\Constants\ErrorConst;
use App\Constants\PlatformConst;
use App\Exceptions\ApiException;
use App\Models\Shop;
use App\Services\Order\Impl\DyOrderImpl;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

/**
 *  / view_order_list (查看订单列表) /
 * download_order (下载订单) / download_order_list (下载订单列表) /
 * print_order (打印订单) / print_order_list (打印订单列表) /
 * export_order (导出订单) / export_order_list (导出订单列表) /
 * delete_order (删除订单) / "
 */
class OperationType
{
    /**
     * view_order (查看订单)
     */

    const view_order = 'view_order';
    /**
     * 下载订单
     */
    const download_order = 'download_order';

    /**
     * 下载订单列表
     */
    const  download_order_list = 'download_order_list';

    /**
     * 打印订单
     */
    const print_order = 'print_order';
    /**
     * 打印订单列表
     */
    const print_order_list = "print_order_list";

    /**
     * 导出订单
     */
    const export_order = 'export_order';
    /**
     *  导出订单列表
     */
    const export_order_list = 'export_order_list';

    /**
     * 删除订单
     */
    const delete_order = 'delete_order';
}

/**
 * 敏感字段类型
 */
class SensitiveType
{
    /**
     * 手机
     */
    const mobile = 'mobile';
    /**
     * 真是姓名
     */
    const name = 'name';
    /**
     * 身份证
     */
    const id = 'id';
    /**
     * 地址
     */
    const address = 'address';
    /**
     * 快递单号
     */
    const waybillCode = 'waybillCode';
    /**
     * 平台订单ID
     */
    const tid = 'tid';
    /**
     * 支付流水Id
     */
    const paymentId = 'paymentId';
    /**
     * 商品ID
     */
    const numIid = 'numIid';

    static function toPlatformCode($types)
    {
        $platform = config('app.platform');
        $result = [];
        foreach ($types as $type) {
            $result[] = SensitiveTypeMapping[$platform][$type] ?? $type;
        }

        return $result;
    }

}

const SensitiveTypeMapping = array(
    PlatformConst::DY => array(
        SensitiveType::mobile => 1,
        SensitiveType::name => 2,
        SensitiveType::id => 3,
        SensitiveType::address => 4,
        SensitiveType::waybillCode => 5,
        SensitiveType::numIid => 6,
        SensitiveType::tid => 7,
        SensitiveType::paymentId => 8
    )
);

const OrderOperationMapping = array(
    PlatformConst::DY => array()
);

class OrderAntiSpamRequest
{
    public $silent = true;
    public $accountId;
    public $accountType = 1;
    public $shopId;
    public $orderIds;
    public $operation;
    public $operationTime;
    public $url;
    public $ip;
    public $identifyInfoList;
    public $sensitiveDataList;
    public $deviceType;
    public $deviceId;
    public $referer;
    public $userAgent;

    public function __construct($accountId, $accountType = 1, $shopId, $orderIds, $operation, $operationTime, $identifyInfoList, $sensitiveDataList, Request $request = null)
    {
        $this->accountId = $accountId;
        $this->accountType = $accountType;
        $this->shopId = $shopId;
        $this->orderIds = $orderIds;
        $this->operation = OrderOperationMapping[PlatformConst::DY][$operation] ?? $operation;
        $this->operationTime = $operationTime ?? time();
        if ($request) {
            $this->url = $request->path();
            $this->ip = $request->ip();
            $this->deviceType = 'PC';
            $this->referer =$request->header('Referer');
            $this->userAgent = $request->userAgent();
        }

        $this->identifyInfoList = $identifyInfoList;
        $this->sensitiveDataList = $sensitiveDataList;
    }

    function getDyData()
    {
        $data = [];
        $data['params'] = [
            'account_id' => $this->accountId,
            'account_type' => $this->accountType,
            'shop_ids' => [$this->shopId],
            'order_ids' => $this->orderIds,
            'operation' => $this->operation,
            'operation_time' => $this->operationTime,
            'url' => $this->url?urlencode($this->url):'',
            'ip' => $this->ip,
            'identifyInfoList' => $this->identifyInfoList,
            'sensitive_data_list' => $this->sensitiveDataList,
            'device_type' => $this->deviceType,
            'referer' => $this->referer?urlencode($this->referer):'',
            'user_agent' => $this->userAgent?urlencode($this->userAgent):''
        ];
        $data['params'] = json_encode($data['params']);
        $data['event_time'] = time();
        return $data;
    }
}

/**
 * 风控
 */
class AntiSpamService
{

    /**
     * 订单风控
     * @param OrderAntiSpamRequest $request
     * @return void
     * @throws ApiException
     * @throws \App\Exceptions\ClientException
     */
    function checkAntispamOrderSend(OrderAntiSpamRequest $request)
    {
        if (PlatformConst::DY == config('app.platform')) {
            $shop = Shop::query()->where('id', $request->shopId)->first();
            $dyOrderImpl = new DyOrderImpl();
            $dyOrderImpl->setShop($shop);
            $passed = $dyOrderImpl->checkAntispamOrderSend($request->getDyData());
            Log::info('checkAntispamOrderSend', [$passed]);
            if (!$request->silent && !$passed) {
                throw new ApiException(ErrorConst::PLATFORM_ANTISPAM);
            }

            return true;
        }
        return true;
    }

}
