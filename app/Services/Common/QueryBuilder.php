<?php

namespace App\Services\Common;

use App\Models\Shop;
use App\Traits\QueryHelperTrait;

class QueryBuilder
{
    use QueryHelperTrait;
    private $clazz;

    static  function  newInstance($clazz): QueryBuilder
    {
        $queryBuilder = new QueryBuilder();
        $queryBuilder->setModel($clazz);
        return $queryBuilder;
    }

    /**
     * @param mixed $clazz
     */
    public function setModel($clazz): void
    {
        $this->clazz = $clazz;
    }
    function build($search): \Illuminate\Database\Eloquent\Builder{
        $query=$this->clazz::query();
        $this->smartWhereByFront($query, $search['condition'],$search['boolean']??'and');
        return $query;
    }

    function sort($query,$sort){
        $this->orderBy($query,$sort);
    }
}
