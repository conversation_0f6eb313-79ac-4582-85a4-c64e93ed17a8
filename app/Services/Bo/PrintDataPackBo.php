<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2022/1/6
 * Time: 22:28
 */

namespace App\Services\Bo;

use App\Services\Traits\ErrorInfoTrait;

/**
 * 带打印数据的 pack
 */
class PrintDataPackBo extends PrintPackBo
{
    use ErrorInfoTrait;

    public $request_id = '';
    public $package_id = 0;
    public $order_infos = [];
    /**
     * 打印数据
     * @var WaybillsPrintDataBo
     */
    private $waybills_print_data;
    /**
     * 子单号打印数据
     * @var WaybillsPrintDataBo[]
     */
    private $sub_waybills_print_data_arr = [];

    public $wp_code = '';
    /**
     * 电子面单号
     * @var string
     */
    public $waybill_code = '';

    /**
     * 子电子面单号
     * @var string
     */
    public $sub_waybill_code_arr = [];


    /**
     * @return null|WaybillsPrintDataBo
     */
    public function getWaybillsPrintData(): ?WaybillsPrintDataBo
    {
        return $this->waybills_print_data;
    }

    /**
     * @param WaybillsPrintDataBo $waybills_print_data
     */
    public function setWaybillsPrintData(WaybillsPrintDataBo $waybills_print_data): void
    {
        $this->waybills_print_data = $waybills_print_data;
    }

    public function getOrderIdStr(): string
    {
        return implode('_', array_pluck($this->order_infos, 'id'));
    }

    public function getOrderCount(): int
    {
        return count($this->order_infos);
    }

    public function getFirstOrder()
    {
        return array_first($this->order_infos);
    }

    public function getFirstOrderShopId()
    {
        return $this->getFirstOrder()['shop_id'];
    }

    public function copyByPrintPackBo(PrintPackBo $obj)
    {
        return $this->copyByObject($obj);
    }
    public function copyByPrintWaybillBo(PrintWaybillBo $obj)
    {
        return $this->copyByObject($obj);
    }

    /**
     * @return WaybillsPrintDataBo[]
     */
    public function getSubWaybillsPrintDataArr()
    {
        return $this->sub_waybills_print_data_arr;
    }

    /**
     * @param WaybillsPrintDataBo[] $sub_waybills_print_data_arr
     */
    public function setSubWaybillsPrintDataArr(array $sub_waybills_print_data_arr): void
    {
        $this->sub_waybills_print_data_arr = $sub_waybills_print_data_arr;
    }

}
