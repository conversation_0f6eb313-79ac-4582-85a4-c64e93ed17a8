<?php

namespace App\Http\Controllers;

use App\Models\TagTemplate;
use Illuminate\Http\Request;

class TagTemplateController extends Controller
{
    public function index(Request $request)
    {
        $shopId=intval($request->input('shopId',$request->auth->shop_id));
        $list = TagTemplate::query()->where('shop_id', $shopId)->get();
        return $this->success($list);
    }

    public function create(Request $request)
    {
        $shopId=intval($request->input('shopId',$request->auth->shop_id));
        $data = $this->validate($request, [
            'name' => 'required|string|min:1|max:255',
            'horizontal' => 'required|integer|min:0',
            'vertical' => 'required|integer|min:0',
            'width' => 'required|integer|min:0',
            'height' => 'required|integer|min:0',
            'is_default' => 'required|in:0,1',
            'default_printer' => 'string',
            'custom_config' => 'string',
        ]);
        $data['shop_id'] = $shopId;
        $tagTemplate = TagTemplate::create($data);
        if ($data['is_default']) {
            TagTemplate::query()->where('shop_id', $shopId)
                ->where('id', '<>', $tagTemplate->id)
                ->update(['is_default' => 0]);
        }
        return $this->success($tagTemplate);
    }

    public function update(Request $request)
    {
        $shopId=intval($request->input('shopId',$request->auth->shop_id));
        $data = $this->validate($request, [
            'id' => 'required|int',
            'name' => 'string|min:1|max:255',
            'horizontal' => 'integer|min:0',
            'vertical' => 'integer|min:0',
            'width' => 'integer|min:0',
            'height' => 'integer|min:0',
            'is_default' => 'in:0,1',
            'default_printer' => 'string',
            'custom_config' => 'string',
        ]);
        $tagTemplate = TagTemplate::query()->where('shop_id', $shopId)->findOrFail($data['id']);
        $tagTemplate->update($data);
        if ($data['is_default']) {
            TagTemplate::query()->where('shop_id', $shopId)
                ->where('id', '<>', $tagTemplate->id)
                ->update(['is_default' => 0]);
        }
        return $this->success($tagTemplate);
    }

    public function delete(Request $request)
    {
        $shopId=intval($request->input('shopId',$request->auth->shop_id));
        $data = $this->validate($request, [
            'id' => 'required|int',
        ]);
        $tagTemplate = TagTemplate::query()->where('shop_id', $shopId)->findOrFail($data['id']);
        $tagTemplate->delete();
        return $this->success();
    }


}
