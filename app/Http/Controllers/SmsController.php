<?php

namespace App\Http\Controllers;

use App\Services\SmsService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\User;
use App\Models\MobileVerifyCode;
use Log;
use Illuminate\Support\Facades\Hash;

class SmsController extends Controller
{
    public function obtainCode(Request $request)
    {
        $phone = $request->input('phone');

        \Log::info('obtainCode input() = ', [$request->input()]);
        $messages = [
            'phone.regex'     => '手机号不合法',
            'phone.numeric'   => '手机号必需为数字',
        ];

        $validator = Validator::make($request->all(), [
            'phone' => 'required|numeric',
            'phone' => 'regex:/^1[3456789][0-9]{9}$/',     //正则验证
        ], $messages);

        if ($phone && str_start($phone, '1') && $validator->fails() == false)
        {
            $code_phone = MobileVerifyCode::query()->where('mobile', $phone)->first();
            if ($code_phone && strtotime($code_phone->updated_at) - time() < 60)
            {
                $this->fail(['message' => '请求过于频繁！']);
            }

            $smsSender = new SmsService;
            $verifyCode = rand(100000, 999999);

            MobileVerifyCode::where('mobile', $phone)->delete();
            $code = MobileVerifyCode::query()->create([
                        'mobile' => $phone,
                        'code' => $verifyCode,
                        'code_expire' => time() + 300
                    ]);

            \Log::info('obtainCode phone = ' . $phone . ' code = ' . $verifyCode);
            $result = $smsSender->sendSms($phone, $verifyCode);
            Log::info("sms result = " . var_export($result, true));

            return $this->success(['message' => '发送成功！']);
        }
        else
        {
            return $this->fail(['status'=>'error']);
        }
    }

    public function validCode(Request $request)
    {
        $verificationCode = $request->input('code');
        $mobile = $request->input('phone');

        \Log::info('validCode', [$request->input()]);

        $code = MobileVerifyCode::query()->where('mobile', $mobile)->first();
        if ($code && $code->code == $verificationCode && $code->code_expire >= time())
        {
           return $this->success(true); 
        }

        return $this->success(false);
    }

    public function savePhone(Request $request)
    {
   
        $phone = $request->input('phone');
        $password=$request->input('password');
        $verificationCode = $request->input('code');
        \Log::info('savePhone', [$request->input()]);
        $user = User::find($request->auth->user_id);
        if ($user) {
            $code_phone = MobileVerifyCode::query()->where('mobile', $phone)->first();
            \Log::info("input code = " . $verificationCode ." user->verify_code = " . $code_phone->code);
            if ($code_phone->code_expire >= time() && $code_phone->code == $request->input('code')) {
                $user->password = Hash::make($password);
                $user->phone = $phone;
                $user->save();

                return $this->success();
            }else{
                return $this->fail('验证码错误');
            }
        }
        return $this->fail('该用户尚未注册，请检查后再修改！');
    }

    public function noAuthBindPhone(Request $request)
    {
        $verificationCode = $request->input('code');
        $phone = $request->input('phone');
        $password = $request->input('password');
        $password_confirmation = $request->input('password_confirmation');

        \Log::info('noAuthBindPhone', [$request->input()]);

        $messages = [
            'phone.regex'        => '手机号不合法',
            'phone.numeric'      => '手机号必需为数字',
            'code.numeric'       => '验证码是6位数字',
            'password.required'  => '6到20位字符',
            'password.confirmed' => '两次密码不一致'
        ];

        $validator = Validator::make($request->all(), [
            'phone'     => 'required|numeric',
            'code'      => 'required|min:6|max:6|numeric',
            'phone'     => 'regex:/^1[3456789][0-9]{9}$/',     //正则验证
            'password'  => 'required|min:6|max:20|confirmed',
        ], $messages);


        $code = MobileVerifyCode::query()->where('mobile', $phone)->first();
        if ($code && $code->code == $verificationCode && $code->code_expire >= time())
        {
            \Log::info($code . '验证码正确');
        }else{
           return $this->fail('验证码不正确！');
        }

        \Log::info('code', [$code]);

        if (strlen($password) < 6 || strlen($password) > 20) {
            return $this->fail('密码是6到20位字符');
        }
            
        if ($password != $password_confirmation) {
            return $this->fail('两次密码不一致');
        }

        $user = User::query()->where('phone', $phone)->first();
        if ($user) {
             \Log::info($phone . '有注册过');
            return $this->fail('该手机'.$phone.'已经注册过了');
        } else {
            \Log::info($phone . '没有注册过');
        }

        $user = User::query()->create([
                    'phone' => $phone,
                    'nickname' => $phone,
                    'password' => Hash::make($password),
                    'invite_code' => getRandStr(16),
                ]);
        
        if ($user) {
            return $this->success('创建用户成功，请前往登录');
        } else {
            return $this->fail('万分抱歉！创建用户失败，请于上班时间联系在线客服');
        }
    }

    public function noAuthChangePwd(Request $request)
    {
        \Log::info('noAuthChangePwd', [$request->input()]);

        $verificationCode = $request->input('code');
        $phone = $request->input('phone');
        $password = $request->input('password');
        $password_confirmation = $request->input('password_confirmation');
        
        $messages = [
            'phone.regex'        => '手机号不合法',
            'phone.numeric'      => '手机号必需为数字',
            'code.numeric'       => '验证码是6位数字',
            'password.required'  => '6到20位字符',
            'password.confirmed' => '两次密码不一致'
        ];

        $validator = Validator::make($request->all(), [
            'phone'     => 'required|numeric',
            'code'      => 'required|min:6|max:6|numeric',
            'phone'     => 'regex:/^1[3456789][0-9]{9}$/',     //正则验证
            'password'  => 'required|min:6|max:20|confirmed',
        ], $messages);

        if (strlen($password) < 6 || strlen($password) > 20) {
            return $this->fail('密码是6到20位字符');
        }
            
        if ($password != $password_confirmation) {
            return $this->fail('两次密码不一致');
        }

        $user = User::query()->where('phone', $phone)->first();     
        if ($user) 
        {
            $code_phone = MobileVerifyCode::query()->where('mobile', $phone)->first();
            if ($code_phone->code_expire >= time() && $code_phone->code == $verificationCode)
            {
                $user->password = Hash::make($request->input('password'));
                $user->save();

                return $this->success('密码修改成功！');
            }else{
                return $this->fail('验证码不正确！');
            }
        } else {
            return $this->fail('该手机号尚未注册，请检查后再修改！');
        }
    }
}
