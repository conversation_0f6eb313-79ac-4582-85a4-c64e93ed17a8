<?php

namespace App\Http\Controllers\Shop;

use App\Exceptions\ClientException;
use App\Http\Controllers\Controller;
use App\Models\Shop;
use App\Services\Client\DyClient;
use App\Services\Shop\ShopService;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

/**
 * Dy店铺控制器
 */
class DyController extends Controller
{
    /**
     * 获取抖音token
     * @param Request $request
     * @return JsonResponse
     */
    public function getMcTokens(Request $request): JsonResponse
    {
        $shopIds = $request->input('shopIds');
        $shops = ShopService::shopsByIds($shopIds);
        $carbon = Carbon::now();
        $result = [];
        /**
         * @var Shop $shop
         */
        foreach ($shops as $shop) {
            //比较过期时间和当前时间

            try {
                if(!$shop->isDy()){
                    continue;
                }
                if ($shop->expire_at < $carbon) {
                    Log::info("抖音token过期", [$shop->id,$shop->expireAt, $carbon]);
                    continue;
                }
                $dyClient = DyClient::newInstance($shop->access_token);
                $response = $dyClient->execute("order/getMCToken", [], 'GET');
                $data=$response['data'];
//                \Log::info("获取抖音token", [$data]);
                if (!empty($data)) {
                    $result[] = ["shopId"=>$shop->id,"token" => $data['token'], "expireAt" => $data['expire_time']];
//                    \Log::info("获取抖音token", [$result[$shop->id]]);
                }
            } catch (\Throwable $exception) {

                Log::error("获取抖音token失败",[$exception]);

            }

        }
        return $this->success($result);

    }
}
