<?php

namespace App\Http\Controllers\Shop;

use App\Http\Controllers\Controller;
use App\Models\Shop;
use App\Models\ShopBind;
use Illuminate\Http\Request;

class AgentPrintController extends Controller
{
    /**
     * 获取厂家的商家
     * @return \Illuminate\Http\JsonResponse
     */
    public function searchShopsOfFactory(Request $request)
    {
        $factoryIds=$request->input("factoryIds");
        $shopName= $request->input("shopName");
        $result=[];

        if(empty($factoryIds)){
            $factoryIds=[$request->auth->shop_id];
        }
        $oShopIds = ShopBind::getShopIdsOfFactory($factoryIds);
        if (count($oShopIds) > 0) {
            $result =Shop::searchByKeyword($shopName, $oShopIds,null,20)->makeHidden(Shop::CRITICAL_FIELDS);
        }
        return $this->success($result);

    }

    /**
     * 获取厂家的商家
     * @return \Illuminate\Http\JsonResponse
     */
    public function hasShops(Request $request){
        $factoryIds=$request->input("factoryIds");
        if(empty($factoryIds)){
            $factoryIds=[$request->auth->shop_id];
        }
        $result=[];
        $factoryHasShop = ShopBind::factoryHasShop($factoryIds);
        foreach ($factoryIds as $factoryId){
            $item=["factoryId"=>$factoryId,"hasShops"=>false];
            if($factoryHasShop->has($factoryId)){
                $item["hasShops"]=true;
            }
            $result[]=$item;
        }
        return $this->success($result);
    }

}
