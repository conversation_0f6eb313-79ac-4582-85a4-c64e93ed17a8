<?php

namespace App\Http\Controllers;

use App\Models\FeedBack;
use App\Models\Shop;
use App\Services\BusinessException;
use Illuminate\Http\Request;

class FeedBackController extends Controller
{

    public function postFeedBack(Request $request)
    {
        $data = $this->validate($request, [
             'type'        => 'required|int',
             'content'     => 'required|string',
             'phone'       => 'required|string',
             'qq'          => 'required|string',
         ]);

         $data['user_id'] = $request->auth->user_id;
         $data['shop_id'] = $request->auth->shop_id;

         $ret = FeedBack::query()->create($data);
         if($ret){
             return $this->success($ret);
         }
    }

    public function delete(Request $request , $id)
    {
        $res = FeedBack::where('id',$id)->delete();

        if (!$res) {
            $this->fail();
        }
        return $this->success($res);
    }
}
