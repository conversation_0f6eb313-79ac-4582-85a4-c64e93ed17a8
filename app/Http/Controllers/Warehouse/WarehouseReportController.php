<?php

namespace App\Http\Controllers\Warehouse;

use App\Exceptions\ErrorCodeException;
use App\Http\Controllers\Controller;
use App\Http\Controllers\Warehouse\Request\WarehouseReportRequest;
use App\Http\StatusCode\StatusCode;
use App\Models\ExportTask;
use App\Services\Warehouse\WarehouseReportService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;
use Rap2hpoutre\FastExcel\FastExcel;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

/**
 * 云仓报表Controller
 */
class WarehouseReportController  extends Controller
{
    /**
     *
     * @var WarehouseReportService $warehouseReportService
     */
    protected $warehouseReportService;

    /**
     * @param WarehouseReportService $warehouseReportService
     */
    public function __construct(WarehouseReportService $warehouseReportService)
    {
        $this->warehouseReportService = $warehouseReportService;
    }


    /**
     * 云仓包裹汇总统计
     * @param Request $request
     * @return void
     * @throws ErrorCodeException
     * @throws ValidationException
     */
    public function summaryReport(Request $request):JsonResponse{
        $data = $this->validate($request, [
            "waybillShopIds" => 'required|array',
            "beginAt" => 'date',
            "endAt" => 'date',
            "batchNo" => 'string',
            "orderShopId" => 'required|int',
            "productName" => 'string',
        ]);

        $warehouseReportRequest=new   WarehouseReportRequest();
        $warehouseReportRequest->orderShopId = $data['orderShopId'];
        $warehouseReportRequest->currentShopId = request()->auth->shop_id;
        $warehouseReportRequest->waybillShopIds = $data['waybillShopIds'];
        $warehouseReportRequest->begin = $data['beginAt']??'';
        $warehouseReportRequest->end = $data['endAt']??'';
        $warehouseReportRequest->productName = $data['productName']??null;
        $warehouseReportRequest->batchNo = $data['batchNo']??null;

        if((empty($warehouseReportRequest->begin)||empty($warehouseReportRequest->end))&&empty($warehouseReportRequest->batchNo)){
            throw new ErrorCodeException([StatusCode::PARAM_MISS,'开始时间和结束时间或者批次号必须填写一个']);
        }


        $summaryReport = $this->warehouseReportService->summaryReport($warehouseReportRequest);
//        \Log::info('summaryReport',[ "request"=>json_encode($warehouseReportRequest),"summary"=>  json_encode($summaryReport)]);
        return $this->success($summaryReport);

    }

    /**
     * 导出云仓包裹汇总统计
     * @param Request $request
     * @return BinaryFileResponse
     * @throws ErrorCodeException
     * @throws ValidationException
     */
    public  function export(Request $request){
        $data = $this->validate($request, [
            "waybillShopIds" => 'required|array',
            "beginAt" => 'date',
            "endAt" => 'date',
            "batchNo" => 'string',
            "orderShopId" => 'required|int',
            "productName" => 'string',
        ]);

        $warehouseReportRequest=new   WarehouseReportRequest();
        $warehouseReportRequest->orderShopId = $data['orderShopId'];
        $warehouseReportRequest->currentShopId = request()->auth->shop_id;
        $warehouseReportRequest->waybillShopIds = $data['waybillShopIds'];
        $warehouseReportRequest->begin = $data['beginAt']??'';
        $warehouseReportRequest->end = $data['endAt']??'';
        $warehouseReportRequest->productName = $data['productName']??null;
        $warehouseReportRequest->offset=0;
        $warehouseReportRequest->limit=1000;
        $warehouseReportRequest->batchNo = $data['batchNo']??null;

        if((empty($warehouseReportRequest->begin)||empty($warehouseReportRequest->end))&&empty($warehouseReportRequest->batchNo)){
            throw new ErrorCodeException([StatusCode::PARAM_MISS,'开始时间和结束时间或者批次号必须填写一个']);
        }


        $shopId = $request->auth->shop_id;
        $userId = $request->auth->user_id;
        $type = ExportTask::WAREHOUSE_DETAIL_REPORT;
        $exportTask = ExportTask::create([
            'user_id' => $userId,
            'shop_id' => $shopId,
            'type' => $type,
            'condition' =>json_encode($warehouseReportRequest) ,
            'status' => ExportTask::STATUS_WAITING,
            'url' => env('APP_DOMAIN') . '/api/waybill_history/export_file?id=',
            'name' => ''
        ]);
        $warehouseReportRequest->exportTaskId = $exportTask->id;
        $warehouseSummaryReport = $this->warehouseReportService->export($warehouseReportRequest);
        $exportTask->refresh();
        return response()->download($exportTask->file_path, $exportTask->name, ['Content-Type' => 'application/vnd.ms-excel']);


    }



}
