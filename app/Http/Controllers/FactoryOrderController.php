<?php

namespace App\Http\Controllers;


use App\Constants\ErrorConst;
use App\Constants\PlatformConst;
use App\Constants\PrintConst;
use App\Exceptions\ApiException;
use App\Exceptions\PrintException;
use App\Jobs\Orders\SyncFactoryOrderByScopeJob;
use App\Jobs\Orders\SyncOrderByScopeJob;
use App\Models\FactoryOrder;
use App\Models\Order;
use App\Models\QueryArea;
use App\Models\Shop;
use App\Models\ShopExtra;
use App\Services\BusinessException;
use App\Services\FactoryOrder\FactoryOrderService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;


class FactoryOrderController extends Controller
{
    /**
     * @var FactoryOrderService
     */
    protected $service;

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     */
    public function index(Request $request)
    {
        $data = $this->validate($request, [
            "search"               => 'string',
            "begin_at"             => 'date',
            "end_at"               => 'date',
            "tab_flag"             => "int",
            "addressGroupId"       => 'int',   //筛选地址
            "displayMerge"         => "boolean",   //合并展示
            "onlyShowMergeOrder"   => "boolean",   //仅显示合并订单
            "onlyShowPrintedOrder" => "boolean", //仅显示打印订单
            "onlyShowRefundOrder"  => "boolean",  //查询退款单
            "flag"                 => 'sometimes|nullable',
            "goodsId"          => 'sometimes|nullable',
            "skuStyle"         => 'sometimes|nullable',
            "sku_value"        => 'string',
            "selectItem"       => 'int',
            "ownerIdList"      => 'array',
            "quickFilterValue" => 'string',
            "skuIdList"        => 'array',
            "goodSkuNumType"   => 'int',
            "shopIdList"       => 'array',
            "print_status"     => 'int',
            "distr_status"     => 'int',
        ]);

        $ownerIdList = $request->input('ownerIdList', []);
        $displayMerge = $request->input('displayMerge'); //合并展示
        $quickFilterValue = $request->input('quickFilterValue', ''); //快捷筛选
        $sort         = $request->input('sort', 'pay_at desc');
        $offset       = (int)$request->input('offset', 0);
        $limit        = (int)$request->input('limit', 10);
        $tab_flag = array_get($data, 'tab_flag', 0);        //tab 菜单项


        $shops       = Shop::getListByIdentifiers($ownerIdList);
        $shopIds     = collect($shops)->pluck('id')->toArray();
        $shop_id = $request->auth->shop_id;

        $query = FactoryOrder::query()->whereIn('shop_id', $shopIds);

        $this->getService()->buildIndexQuery($query, $data);
        $this->getService()->buildByTabFlag($query, $tab_flag);

        // 精准查询的 where
        $ordersNum = null; //一键搜索里有用到，订单数量
        $queryGroup  = null;
        $groupColumn = 'receiver_id';

        $query = $this->getService()->handleOrderBy($query,$sort);
        list($onlyShowMergeOrder, $notShowMergeOrder) = $this->getService()->buildByQuickFilter($query, $quickFilterValue);
        if ($limit == 0 || $limit > 1000) {
            $limit = 100;
        }

        if ($displayMerge) {
            list($query, $rows_found) = $this->getService()->handleOrderGroup($query, $data, $onlyShowMergeOrder, $notShowMergeOrder, $groupColumn, $sort, $limit, $offset);
        } else {
            list($query, $rows_found) = $this->getService()->handleOrderSingle($query, $data, $groupColumn, $sort, $limit, $offset);
        }
        $query->with('packages');
        $ret = $query->get();

        if ($displayMerge) {
            $ret = $this->getService()->handleMergeOrder($ret, $shop_id, $groupColumn);
        }else{
            $ret = $this->getService()->handleOrderPackages($ret);
        }

        $pagination = [
            'rows_found' => $rows_found,
            'offset'     => $offset,
            'limit'      => $limit
        ];

        return $this->success(['pagination' => $pagination, $ret]);

    }

    /**
     * @return FactoryOrderService
     */
    public function getService()
    {
        if (empty($this->service)) {
            $this->service = new FactoryOrderService();
        }
        return $this->service;
    }


    /**
     * 同步订单通过时间范围
     * @param Request $request
     * @return JsonResponse
     * @throws BusinessException
     * @throws ApiException
     * <AUTHOR>
     */
    public function syncOrderByScope(Request $request): JsonResponse
    {
        $this->validate($request, [
            "begin_time" => 'date',
            "end_time"   => 'date',
            "ownerIdList" => 'array',
        ]);
        $beginTime = $request->input('begin_time');
        $endTime = $request->input('end_time');
        $ownerIdList = $request->input('ownerIdList', []);
        $shops = Shop::getListByIdentifiers($ownerIdList)->pluck('id')->toArray();
        if (empty($shops)) {
            $shops = [$request->auth->shop_id];
        }

        if (empty($beginTime)) {
            $beginTime = date('Y-m-d', strtotime('-2 day'));
        }
        if (empty($endTime)) {
            $endTime = date('Y-m-d 23:59:59');
        }
        if (strtotime($beginTime) > strtotime($endTime)) {
            throw new BusinessException('开始时间不能大于结束时间');
        }
        if ((strtotime($endTime) - strtotime($beginTime)) / 86400 > 30) {
            throw new BusinessException('时间范围不能大于30天');
        }

//        $shopId = $request->auth->shop_id;
        $redis = \redis('cache');

        foreach ($shops as $index => $shopId) {
//            $shopId = $shop->id;
            $key = 'syncFactoryOrderByScopeLock:'. $shopId;
            $lock = $redis->set($key, 1, 'nx', 'ex' , 60);
            if (!$lock){
                throw new ApiException(ErrorConst::ORDER_REPEAT_SYNC);
            }
            $action = 'syncFactoryOrderByScope';
            // 当前同步数量
            $key = "$action:sync_current:$shopId";
            $redis->setex($key, 86400, 0);
            // 需要同步总数量
            $key = "$action:sync_total:$shopId";
            $redis->setex($key, 86400, 0);
            $key = "$action:sync_finish:$shopId";
            $redis->setex($key, 86400, 0);
            $this->dispatch(new SyncFactoryOrderByScopeJob($shopId, $beginTime, $endTime, $action));
        }


        return $this->success();
    }

    /**
     * 获取同步订单的进度
     * @param Request $request
     * @return JsonResponse
     * <AUTHOR>
     */
    public function getSyncOrderSchedule(Request $request): JsonResponse
    {
        $this->validate($request, [
            "ownerIdList" => 'array',
        ]);
        $ownerIdList = $request->input('ownerIdList', []);
        $shops = Shop::getListByIdentifiers($ownerIdList)->pluck('id')->toArray();
        if (empty($shops)) {
            $shops = [$request->auth->shop_id];
        }

//        $user_id = $request->auth->user_id;
        $redis = \redis('cache');
        $action = 'syncFactoryOrderByScope';
        $order_sync_current = 0;
        $order_sync_total = 0;
        $order_sync_finish_count = 0;
        $order_sync_finish = 0;
        foreach ($shops as $index => $shopId) {
            $key = "$action:sync_current:$shopId";
            $order_sync_current += $redis->get($key);
            $key = "$action:sync_total:$shopId";
            $order_sync_total += $redis->get($key);
            $key = "$action:sync_finish:$shopId";
            $order_sync_finish_count += $redis->get($key);
        }
        if ($order_sync_finish_count == count($shops)) {
            $order_sync_finish = 1;
        }

        return $this->success([
//            'order_count' => $orderCount,
            'order_sync_current' => $order_sync_current,
            'order_sync_total' => $order_sync_total,
            'order_sync_finish' => $order_sync_finish,
        ]);

    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     */
    public function printCheck(Request $request)
    {
        $data = $this->validate($request, [
            'type'   => 'required|int',
            'template_id' => 'required|int',
            'packages' => 'required|array'
        ]);

        $userId = $request->auth->user_id;
        $shopId = $request->auth->shop_id;
        try{
            //校验电子面单是否过期以及余额
            $this->getService()->checkAuthStatusAndWaybillBalance($data);
            //校验订单状态
            $this->getService()->checkPrintStatusAndOrderStatus($userId, $shopId, $data);
        }catch (PrintException $e) {
            return $this->success($e->getData(), $e->getMsg(), $e->getStatusCode());
        }
        return $this->success();
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     */
    public function print(Request $request)
    {
        $data = $this->validate($request, [
            'batch_no'    => 'required|string',
            'template_id' => 'required|int',
            'req_index'   => 'required|int',
            'print_params' => 'required|array'
        ]);

        // 打印批次上锁 防止重复打印
        $redis = redis('cache');
        $redisKeyPrefix = 'order_print_lock:';
        $requestRedisKey = $redisKeyPrefix . $data['batch_no'] . $data['req_index'];
        $isDoing = $redis->get($requestRedisKey);
        if (is_null($isDoing)) {
            $redis->setex($requestRedisKey, 60*60*24, 1);
        } else {
            return $this->fail('该批次正在操作中，请稍后再试', 400);
        }

        $userId = $request->auth->user_id;
        $shopId = $request->auth->shop_id;
        $printData = $this->getService()->getPrintData($userId, $shopId, $data);

        $redis->del($requestRedisKey);
        return $this->success($printData);

    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function notifyPrintResult(Request $request)
    {
        $data = $request->input();
        $this->getService()->notifyPrintResult($data);

        return $this->success();
    }

    /**
     * 订单回传
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     * <AUTHOR>
     */
    public function delivery(Request $request): JsonResponse
    {
        $data = $this->validate($request, [
            "delivers" => "required|array",
            "delivers.*.order_id" => "required|int",
            "delivers.*.waybill_code" => "required|string",
            "delivers.*.wp_code" => "required|string",
        ]);
        $arr = $this->getService()->waybillReturn($data['delivers']);
        return $this->success($arr);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function waybillRecovery(Request $request)
    {
        $data = $this->validate($request, [
            "waybill_code_list" => "required|array"
        ]);

        $ret = $this->getService()->waybillRecovery($data);
        return $this->success($ret);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws ApiException
     * @throws ValidationException
     */
    public function lock(Request $request)
    {
        $data = $this->validate($request, [
            "ids" => "required|array",
            "locked" => "required",
        ]);

        $ret = $this->getService()->lock($data);

        if (!$ret) {
            return $this->fail('操作失败');
        }
        return $this->success();
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     */
    public function queryGoodsName(Request $request)
    {
        $data = $this->validate($request, [
            "begin_at" => 'date',
            "end_at" => 'date',
            "timeField" => 'string',
            "ownerIdList" => 'array',
        ]);

        $ret = $this->getService()->getGoodsNameList($data);
        return $this->success($ret);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function getBindShopList(Request $request)
    {
        $shopId = $request->auth->shop_id;
        $ret = $this->getService()->getBindShopList($shopId);
        return $this->success($ret);
    }
}
