<?php

namespace App\Http\Controllers\Api;

use App\Constants\ErrorConst;
use App\Exceptions\ApiException;
use App\Models\Shop;
use App\Services\Order\Impl\JdOrderImpl;
use App\Services\Order\OrderServiceManager;
use Illuminate\Http\Request;

/**
 * JD相关的特殊的API
 */
class JdOpenApiV3Controller extends BaseOpenApiController
{
    /** 退款申请
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiException
     * @throws \Illuminate\Validation\ValidationException
     */
    public function refundApplyList(Request $request)
    {
        $data = $this->validate($request, [
            'checkTimeStart' => 'required|string',
            'checkTimeEnd' => 'required|string',
            'pageSize' => 'required|int|between:1,50',
            'pageIndex' => 'required|int|between:1,100',
        ]);

        $userId = $request->shop->user_id;
        $shopId = $request->shop->id;
        $orderService = OrderServiceManager::create();
        $orderService->setUserId($userId);
        $orderService->setShop(Shop::find($shopId));
        StatisticsCost('拉取平台退款审核单列表');
        // 拉取平台订单
        $tradesOrder = $orderService->batchGetRefundApplyList($data);
        StatisticsCost('拉取平台退款审核单列表完成：' . count($tradesOrder));
        if (empty($tradesOrder)) {
            throw new ApiException(ErrorConst::PLATFORM_GET_REFUND_APPLY_LIST_EMPTY);
        }

        return $this->successForOpenApi($tradesOrder);
    }

    /**
     * 查看售后和退款信息
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws \Illuminate\Validation\ValidationException
     */
    public function viewServiceAndRefund(Request $request){
        $data = $this->validate($request, [
            'orderId' => 'numeric',
            'applyTimeBegin' => 'date',
            'applyTimeEnd' => 'date',
            'approveTimeBegin' => 'date',
            'approveTimeEnd' => 'date',
            'pageNumber' => 'numeric',
            'pageSize' => 'numeric',
            'extJsonStr' => 'string',
            'buId' => 'string',
        ]);
        $orderService =new JdOrderImpl();
        $shopId = $request->shop->id;
        $orderService->setShop(Shop::find($shopId));
        $result = $orderService->viewServiceAndRefund($data);
        return $this->successForOpenApi($result);

    }


    /**
     * 获取电子面单
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws \Illuminate\Validation\ValidationException
     */
    public function printingPrintDataPullData(Request  $request){
        $data = $this->validate($request, [
            'param1' => 'required',
        ]);
        $userId = $request->shop->user_id;
        $shopId = $request->shop->id;
        $orderService =new JdOrderImpl();
        $orderService->setUserId($userId);
        $orderService->setShop(Shop::find($shopId));
        $result = $orderService->printingPrintDataPullData($data);
        return $this->successForOpenApi($result);
    }


    /**
     *  获取京东运单号接口
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws \Illuminate\Validation\ValidationException
     */
    public function getEtmsWaybillcode(Request  $request){
        $data = $this->validate($request, [
            'preNum' => 'required|string',
            'customerCode' => 'required|string',
            'orderType' => 'required|int',
        ]);
        $userId = $request->shop->user_id;
        $shopId = $request->shop->id;
        $orderService =new JdOrderImpl();
        $orderService->setUserId($userId);
        $orderService->setShop(Shop::find($shopId));
        $result = $orderService->getEtmsWaybillcode($data);
        return $this->successForOpenApi($result);
    }

    /**
     * 青龙接单接口
     * @param Request $request
     * @return void
     */
    public function sendEtmsWaybill(Request $request){
        $data = $this->validate($request, [
            'deliveryId' => 'string',
            'salePlat' => 'string',
            'orderId' => 'string',
            'customerCode' => 'string',
            'thrOrderId' => 'string',
            'selfPrintWayBill' => 'int',
            'pickMethod' => 'string',
            'packageRequired' => 'string',
            'senderName' => 'string',
            'senderAddress' => 'string',
            'senderTel' => 'string',
            'senderMobile' => 'string',
            'senderPostcode' => 'string',
            'receiveName' => 'string',
            'receiveAddress' => 'string',
            'province' => 'string',
            'city' => 'string',
            'county' => 'string',
            'town' => 'string',
            'provinceId' => 'numeric',
            'cityId' => 'int',
            'countyId' => 'int',
            'townId' => 'int',
            'siteType' => 'int',
            'siteId' => 'int',
            'siteName' => 'string',
            'receiveTel' => 'string',
            'receiveMobile' => 'string',
            'postcode' => 'string',
            'packageCount' => 'int',
            'weight' => 'numeric',
            'vloumLong' => 'numeric',
            'vloumWidth' => 'numeric',
            'vloumHeight' => 'numeric',
            'vloumn' => 'numeric',
            'description' => 'string',
            'collectionValue' => 'numeric',
            'collectionMoney' => 'numeric',
            'guaranteeValue' => 'numeric',
            'guaranteeValueAmount' => 'numeric',
            'signReturn' => 'int',
            'aging' => 'int',
            'transType' => 'int',
            'remark' => 'string',
            'goodsType' => 'int',
            'orderType' => 'int',
            'shopCode' => 'string',
            'orderSendTime' => 'string',
            'warehouseCode' => 'string',
            'areaProvId' => 'int',
            'areaCityId' => 'int',
            'shipmentStartTime' => 'date',
            'shipmentEndTime' => 'date',
            'idNumber' => 'string',
            'addedService' => 'string',
            'extendField1' => 'string',
            'extendField2' => 'string',
            'extendField3' => 'string',
            'extendField4' => 'string',
            'extendField5' => 'string',
            'senderCompany' => 'string',
            'receiveCompany' => 'string',
            'freightPre' => 'numeric',
            'goods' => 'string',
            'goodsCount' => 'int',
            'promiseTimeType' => 'int',
            'freight' => 'numeric',
            'unpackingInspection' => 'string',
            'fileUrl' => 'string',
            'customerBoxCode' => 'array',
            'customerBoxNumber' => 'array',
            'pickUpStartTime' => 'date',
            'pickUpEndTime' => 'date',
        ]);
        $userId = $request->shop->user_id;
        $shopId = $request->shop->id;
        $orderService =new JdOrderImpl();
        $orderService->setUserId($userId);
        $orderService->setShop(Shop::find($shopId));
        $result = $orderService->sendEtmsWaybill($data);
        return $this->successForOpenApi($result);
    }

    public function updateBuyOrderSopWaybill(Request $request){
        $data = $this->validate($request, [
            'orderId' => 'string',
            'logisticsId' => 'string',
            'tradeNo' => 'string',
            'waybill' => 'string',
        ]);
        $userId = $request->shop->user_id;
        $shopId = $request->shop->id;
        $orderService =new JdOrderImpl();
        $orderService->setUserId($userId);
        $orderService->setShop(Shop::find($shopId));
        $result = $orderService->updateBuyOrderSopWaybill($data);
        return $this->successForOpenApi($result);
    }
}
