<?php

namespace App\Http\Controllers\Template;

use App\Constants\ErrorConst;
use App\Exceptions\ApiException;
use App\Exceptions\ErrorCodeException;
use App\Http\Controllers\Controller;
use App\Http\Controllers\Template\Request\CompanyUpdateRequest;
use App\Http\StatusCode\StatusCode;
use App\Models\Company;
use App\Models\PrintTemplate;
use App\Models\Shop;
use App\Models\ShopBind;
use App\Models\Template;
use App\Models\Waybill;
use App\Models\WaybillHistory;
use App\Models\WaybillShareAction;
use App\Services\BusinessException;
use App\Services\PrintDataService;
use App\Services\Template\TemplateService;
use App\Services\Waybill\AbstractWaybillService;
use App\Services\Waybill\CompanyService;
use App\Services\Waybill\PlatformTemplate;
use App\Services\Waybill\WaybillServiceManager;
use App\Utils\Environment;
use App\Utils\ExpressCompanyUtil;
use App\Utils\ObjectUtil;
use App\Utils\WaybillUtil;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;

class TemplateController extends Controller
{
    /**
     * @var CompanyService $companyService
     */
    private $companyService;

    /**
     * @var TemplateService  $templateService
     */
    private $templateService;

    /**
     * @param CompanyService $companyService
     * @param TemplateService $templateService
     */
    public function __construct(CompanyService $companyService,TemplateService $templateService)
    {
        $this->companyService = $companyService;
        $this->templateService = $templateService;
    }


    /**
     * 电子面单以及商家自定义模板创建
     * @param Request $request
     * @return JsonResponse
     * @throws BusinessException|ValidationException
     * @throws ApiException
     */
    public function add(Request $request): JsonResponse
    {
        $data = $this->validate($request, [
            "height" => "required|numeric",
            "width" => "required|numeric",
            "content" => "required|string",
            "style" => "required|int",
            "type" => "required|int",
            "name" => "required|string",
            'picture' => "required|string",
            'picture_height' => "required|numeric",
            'picture_width' => "required|numeric",
            'waybill_type' => "required|string",
            'parent_template_id' => "required|int",
            'merge_template_url' => "string",
            'wp_name' => "required|string",
            'wp_code' => "required|string",
            'owner_id' => "required|string",
            'owner_name' => "required|string",
            'province' => "required|string",
            'city' => "required|string",
            'district' => "sometimes",
            'street_name' => "sometimes",
            'detail' => "string",
            'size' => "required|string",
            'branch_code' => "sometimes",
            'auth_source' => "required|int",
            'shopping_address_id' => "int",
            'time_delivery' => "sometimes",
            'horizontal' => "int",
            'vertical' => "int",
            'show_logo' => "boolean",
            'insure' => "int",
            "description" => "string",
            "default_print" => "string",
            "service_list" => "string",
            'platform_template_id' => "string",
            'platform_account_id' => "string",
            'platform_shop_id' => "string",
            'belong_shop_name'=>'string',
            'belong_shop_id'=>'int',
            'parentPart' => "int", // 子母件
            'print_contents'=>"string",
            'extended_info' => 'nullable|string',
        ]);
        //自定义区域默认
        //如果传入了自定义区域字段，就用传入的，否则用默认的
        if(!array_key_exists('content',$data)){
            $data['content'] = Template::NORMAL_CONFIG_100_180;
        }
        //如果是76*130的模板，就用默认的，而且没有自定义区域字段，就用默认的
        if ($data['size'] == Template::TEMPLATE_SIZE_76_130&&!array_key_exists('content',$data)) {
            $data['content'] = Template::NORMAL_CONFIG_76_133;
        }

        return $this->success(Template::generate($data, $request->auth->user_id, $request->auth->shop_id));
    }

    /**
     * 编辑电子面单模板
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     * @throws BusinessException
     * @throws ValidationException
     */
    public function edit(Request $request,int $id): JsonResponse
    {
        $data = $this->validate($request, [
            "height" => "required|numeric",
            "width" => "required|numeric",
            "content" => "required|string",
            "mergeContent" => "required|string",
            "style" => "required|int",
            "name" => "required|string",
            'size' => "string",
            'picture' => "required|string",
            'picture_height' => "required|numeric",
            'picture_width' => "required|numeric",
            'waybill_type' => "required|string",
            'parent_template_id' => "required|int",
            'merge_template_url' => "required|string",
            'wp_name' => "required|string",
            'wp_code' => "required|string",
            'owner_id' => "required|string",
            'owner_name' => "required|string",
            'auth_source' => "required|int",
            'shipping_address_id' => "int",
            'time_delivery' => "sometimes",
            'horizontal' => "int",
            'vertical' => "int",
            'show_logo' => "boolean",
            'insure' => "int",
            "description" => "string",
            "default_print" => "string",
            "print_contents"=>"nullable|string",
            'belong_shop_name'=>'string',
            'belong_shop_id'=>'int',
//            'shopping_address_id' => "int",
            'goods_new_page' => "boolean",
            "service_list"        => "nullable|string",
        ]);
        //先更新网点
        Template::edit(
            $id,
            $data
        );

        return $this->success();
    }

    /**
     * 编辑打印内容
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     */
    public function updatePrintContent(Request $request): JsonResponse
    {
        $data = $this->validate($request, [
            "id"=>"required|int",
            "printContent" => "string",
        ]);
        $id=get_array_value($data,'id');
        Template::updatePrintContents($id,get_array_value($data,'printContent',null));
        return $this->success();
    }



    /**
     * 删除电子面单
     * @param Request $request
     * @param         $id
     * @return JsonResponse
     * @throws BusinessException
     */
    public function delete(Request $request, $id): JsonResponse
    {
        $exist = Template::query()->findOrFail($id);
        if ($exist->user_id !== $request->auth->user_id || $exist->shop_id !== $request->auth->shop_id) {
            throw new BusinessException('该模板为关联店铺创建，该账号无权限删除');
        }

        if (!$exist->delete()) {
            throw new BusinessException('模板删除失败！');
        }

        return $this->success();
    }

    /**
     * 设置默认
     * @param Request $request
     * @param         $id
     * @return JsonResponse
     * @throws BusinessException
     */
    public function default(Request $request, $id)
    {
        $waybillTemplate = Template::query()->where([
            //'user_id' => $request->auth->user_id,
            //'shop_id' => $request->auth->shop_id,
            'id' => $id
        ])->firstOrFail();
        Template::query()->where([
            //'user_id' => $request->auth->user_id,
            'shop_id' => $request->auth->shop_id,
            'default' => Template::WAYBILL_DEFAULT_YES
        ])->update([
            'default' => Template::WAYBILL_DEFAULT_NO
        ]);
        $waybillTemplate->default = Template::WAYBILL_DEFAULT_YES;
        if (!$waybillTemplate->save()) {
            throw new BusinessException('设置默认失败！');
        }

        return $this->success();
    }

    /**
     * 用于提示信息展示
     * @param Request $request
     * @return JsonResponse
     */
    public function judge(Request $request)
    {
        $res = ['key' => false, 'value' => false];
        $hasAccount = Waybill::where('user_id', $request->auth->user_id)->count();
        if ($hasAccount > 0) {
            $res['key'] = true;
        }
        $shop_id = intval($request->input('shopId', $request->auth->shop_id));
        $hasTemplate = Template::where('shop_id', $shop_id)->count();
        if ($hasTemplate > 0) {
            $res['value'] = true;
        }

        return $this->success($res);
    }

    /**
     * 获取快递模板 通过接口
     * @param Request $request
     * @return JsonResponse
     * @throws ApiException
     * <AUTHOR>
     */
    public function getTemplatesByApi(Request $request): JsonResponse
    {
        $currentShopId = $request->auth->shop_id;
        $currentUserId = $request->auth->user_id;

        //1-商家店铺的模板
        //2-》商家绑定的主店铺的模板
        //3-》商家绑定的从店铺的模板
        $scopes=$request->input('scopes',[1,2]);
        //获取应用内定义的模板
        $templates = $this->templateService->getScopeDefinedTemplates($currentShopId,$currentUserId,$scopes);
        return $this->success($templates);
    }


    /**
     * 获取普通快递模板信息
     * @param Request $request
     * @return JsonResponse
     */
    public function commonTemplates(Request $request): JsonResponse
    {
      return $this->success(ExpressCompanyUtil::getCommonExpressTemplates());
    }

    /**
     * 各个平台支持的快递公司
     *
     */
    public function getPlatformCompany(): JsonResponse
    {
        return $this->success(ExpressCompanyUtil::supportExpressCompany());
    }


    /**
     * 获取商家自定义区域字段
     * @param Request $request
     * @return JsonResponse
     */
    public function customAreaContents(Request $request)
    {
        return $this->success(config('custom_area_contents'));
    }

    /**
     * 设置默认打印机
     * @param Request $request
     * @param         $id
     * @return JsonResponse
     * @throws BusinessException
     * @throws ValidationException
     */
    public function defaultPrint(Request $request, $id): JsonResponse
    {
        $this->validate($request, [
            "default_print" => "required|string",
        ]);
        $update = Template::query()->where([
            //'user_id' => $request->auth->user_id,
            //'shop_id' => $request->auth->shop_id,
            'id' => $id,
        ])->update(['default_print' => $request->input('default_print')]);
        if (!$update) {
            throw new BusinessException('更新失败！');
        }

        return $this->success();
    }

    /**
     * 打印测试
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     */
    public function print(Request $request): JsonResponse
    {
        $this->validate($request, [
            'content' => 'required|array',
            //'senderInfo' => 'required|array',
            'customTemplate' => 'required'
        ]);

        $waybillCode = "789874377392539700"; //测试面单号
        //构造测试打印发件人
        $sender = [
            'province' => '浙江省',
            'city' => '杭州市',
            'district' => '余杭区',
            'detail' => '文一西路100号',
            'sender_name' => '张三',
            'mobile' => '152*****000',
            'phone' => '',
        ];
        $orderIdStr = strval(mt_rand());
        $customConfig = $request->input('content');
        $template = $request->input('customTemplate');
        $printData = Template::templateTestData($sender, $orderIdStr, $template, $customConfig, $waybillCode);
        $printDataList[0] = $printData;

        return $this->success(['orders' => $printDataList]);
    }

    public function getJdTemplate(Request $request)
    {
        $templateId = $request->input('template_id', '');
        if (empty($templateId)) {
            return response()->make('', 200)->withHeaders([]);
        }
        $size = $request->input('size', '');

        if (Environment::isUni()) {
            $template = PrintTemplate::query()->where('id', $templateId)->first();
            $jdTemplate = [
                "top" => "0cm",
                "left" => "0.0cm",
                "width" => "76cm",
                "height" => "50cm",
            ];
            $allItem = [];
            foreach (json_decode($template['custom_config'], true) as $item) {
                preg_match('/=data.([a-zA-Z]+)/', $item['value'], $ch);
                $topOffset = $size == Template::TEMPLATE_SIZE_76_130 ? ($item['top'] + 280) : ($item['top'] + 350);
                if ($item['id'] != 20) {
                    $type = 'text';
                    $width = numberFormat($item['width'] * $template['width'] * 3.78);
                    $height = numberFormat($item['height'] * $template['height'] * 3.78);
                    $justifyContent = 'flex-start';
                    $fontWeight = 'normal';
                    $fontSize = 12;
                }else{
                    $type = 'water';
                    $width = $item['width'];
                    $height = $item['height'];
                    $justifyContent = 'center';
                    $fontWeight = 'bold';
                    $fontSize = 26;
                }
                $left = numberFormat($item['left'] * $template['width'] * 3.78);
                $top = numberFormat($item['top'] * $template['height'] * 3.78 + $topOffset);

                $tempTemplate = [
                    'type' => $type,
                    'top' => $top,
                    'left' => $left,
                    'width' => $width,
                    'height' => $height,
                    'content' => !empty($ch)?('@{' . $ch[1] . '}'): $item['value'],
                    'justifyContent' => $justifyContent,
                    'fontName' => '微软雅黑',
                    'fontWeight' => $fontWeight,
                    'fontSize' => $fontSize,
                    'lineHeight' => 1
                ];

                if ($item['id'] == 20) {
                    $tempTemplate['alpha'] = 0.65;
                }
                $allItem[] = $tempTemplate;
            }
            $jdTemplate['items'] = $allItem;
            $jdTemplate = base64_encode(json_encode($jdTemplate, JSON_UNESCAPED_UNICODE));
        }else{
            $template = Template::query()->where('id', $templateId)->first();
            $jdTemplate = $template->jd_template;
        }
        if (empty($template)) {
            return response()->make('', 200)->withHeaders([]);
        }

        return response()->make($jdTemplate, 200)->withHeaders([]);
    }

    public function getKsTemplate(Request $request)
    {
        $templateId = $request->input('template_id', '');
        $splitable = $request->input('splitable', 0);
        $size = $request->input('size', '');
        if (empty($templateId)) {
            return response()->make('', 200)->withHeaders([]);
        }

        if (Environment::isUni()) {
            $template = PrintTemplate::query()->where('id', $templateId)->first();
            $xmlStr = $this->genXmlTemplate($template, $size);
            $xml = simplexml_load_string($xmlStr);
            $layout = $xml;
            $layout['splitable'] = $splitable ? 'true' : 'false';

            foreach ($xml->text as $index => $text) {
                $layoutWidth = (float)$layout['width'];
                $layoutHeight = (float)$layout['height'];

                $left = (float)$text['left'];
                $top = (float)$text['top'];
                $width = (float)$text['width'];
                $height = (float)$text['height'];

                if (!empty($text['isNextContents'])) {
                    continue;
                }
                $text['left'] = number_format($left * $layoutWidth, 2,'.','');
                $text['top'] = number_format($top * $layoutHeight,2,'.','');
                $sizeFixedValue = (int)$text['sizeFixedValue'];
                if ($sizeFixedValue) {
                    $text['width'] = number_format($width / 3.78,2,'.','');
                    $text['height'] = number_format($height / 3.78,2,'.','');
                }else{
                    $text['width'] = number_format($width * $layoutWidth,2,'.','');
                    $text['height'] = number_format($height * $layoutHeight,2,'.','');
                }
            }
            $xmlData = $xml->asXML();
        }else{
            $template = Template::query()->where('id', $templateId)->first();
            $xmlData = $template->jd_template;
        }

        if (empty($template)) {
            return response()->make('', 200)->withHeaders([]);
        }


//        $template->jd_template = str_replace(['splitable="false"', 'splitable="true"'], $splitableStr, $updatedXml);
        return response()->make($xmlData, 200)->withHeaders(['content-type' => 'application/xml']);
    }

    public function getXhsTemplate(Request $request)
    {
        $templateId = $request->input('template_id', '');
        $splitable = $request->input('splitable', 0);
        $width = $request->input('width', 0);
        $height = $request->input('height', 0);
        if (empty($templateId)) {
            return response()->make('', 200)->withHeaders([]);
        }
//        if ($splitable){
//            $splitableStr = 'splitable="true"';
//        }else{
//            $splitableStr = 'splitable="false"';
//        }

        $template = Template::query()->where('id', $templateId)->first();
        if (empty($template)) {
            return response()->make('', 200)->withHeaders([]);
        }
//        $template->jd_template = str_replace(['splitable="false"', 'splitable="true"'], $splitableStr, $template->jd_template);
        $custom_config = json_decode($template->custom_config, true);
        if (!empty($width) && !empty($height)) {
            $custom_config['size'] = "{$width}x{$height}";
        }

        return response()->make($custom_config, 200)->withHeaders(['content-type' => 'application/json']);
    }



    /**
     * 获取平台模板
     * @param Request $request
     * @return JsonResponse
     */

    public function getCloudPrintStdTemplates(Request  $request):JsonResponse{
        $shop = Shop::query()->where('id', $request->auth->shop_id)->firstOrFail();
        $waybillService = WaybillServiceManager::init(WaybillUtil::getCurrentAuthSource(), $shop->access_token);
        $waybillService->setShop($shop);
        return  $this->success($waybillService->getCloudPrintStdTemplates());
    }


    /**
     * 获取商家在平台定义的模板
     * @param Request $request
     * @return JsonResponse
     */

    public function getPlatformDefinedTemplates(Request $request): JsonResponse
    {
        $wpCode = $request->input('wp_code', '');
        $templates = [];
        $shopIds = ShopBind::getAllRelationShopIds($request->auth->shop_id);
        foreach ($shopIds as $shopId) {
            $shop = Shop::where(['id' => $shopId, 'auth_status' => Shop::AUTH_STATUS_SUCCESS])->first();
            if (!$shop) {
                Log::info("店铺不存在或者未授权", [$shopId]);
                continue;
            }

            $waybillService = WaybillServiceManager::init(WaybillUtil::getCurrentAuthSource(), $shop->access_token);
            $waybillService->setShop($shop);
            $templates = array_merge($templates, $waybillService->getCustomizeWaybillTemplates($wpCode));
        }

        return $this->success($templates);
    }

    /**
     * 修改模板的网点信息
     * @param Request $request
     * @return void
     * @throws ApiException
     * @throws BusinessException
     * @throws ValidationException
     * @throws ErrorCodeException
     */
    public function updateCompany(Request  $request):JsonResponse{
        $data = $this->validate($request, [
            'wpName' => "required|string",
            'wpCode' => "required|string",
            'ownerId' => "required|string",
            'ownerName' => "required|string",
            'province' => "required|string",
            'city' => "required|string",
            'district' => "sometimes",
            'street' => "sometimes",
            'detail' => "string",
            'branchCode' => "sometimes",
            'settlementCode'=>"sometimes",
            'authSource' => "required|int",
            'templateIds'=>"required|array",
            'companyId'=>'required|int',
        ]);
        /**
         * @var CompanyUpdateRequest $companyUpdateRequest
         */
        $companyUpdateRequest=ObjectUtil::mapToObject($data,CompanyUpdateRequest::class);
        $companyUpdateRequest->shopId=$request->auth->shop_id;
        $companyUpdateRequest->userId=$request->auth->user_id;
        $company = $this->companyService->generate($companyUpdateRequest);
        if(!$company){
          throw_error_code_exception(StatusCode::OPERATION_FAILED,null,"生成网点失败");
        }
        $originCompanyId=get_array_value($data,'companyId');
        $templateIds=get_array_value($data,'templateIds');
        if($originCompanyId!=$company->id){
            $updatedCount = $this->templateService->updateCompany($templateIds, $company->id);
            if($updatedCount<1){
                throw_error_code_exception(StatusCode::OPERATION_FAILED,null,"修改模板的网点信息失败");
            }
            Log::info("修改模板的网点信息",[$templateIds,$originCompanyId]);
        }else{
            Log::info("修改模板的网点信息,没有修改模板的网点信息",[$templateIds,$originCompanyId]);
        }
        return $this->success();

    }

    /**
     * 修改模板的增值服务
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     */
    public function updateServiceList(Request $request): JsonResponse
    {
        $data=$this->validate($request,[
            'id'=>'required|int',
            'serviceList'=>'required|string',
            'timeDelivery'=>'string|nullable'
        ]);
        $templateId=get_array_value($data,'id');
        $serviceList=get_array_value($data,'serviceList');
        $timeDelivery= get_array_value($data,"timeDelivery");
        $updateCount = $this->templateService->updateServiceList($templateId, $serviceList,$timeDelivery);
        if($updateCount>0) {
            \Log::info("修改模板的增值服务", [$templateId, $serviceList]);
            return $this->success();
        }
        else{
            return $this->fail("模板增值服务修改失败",StatusCode::OPERATION_FAILED[0]);
        }

    }

    private function genXmlTemplate($template, $size)
    {
        $templateArr = $template->toArray();
        $str = "<?xml version='1.0'?>";
        //$str .= "<layout id='CUSTOM_AREA' width='76' height='50' left='0' top='80'>";
        $layoutWidth = number_format($templateArr['width'] , 2);
        $layoutHeight = number_format($templateArr['height'], 2);
        $layoutTop = 81;
        if ($size != Template::TEMPLATE_SIZE_76_130){
            $layoutTop = 150;
        }
        $str .= sprintf('<layout id="CUSTOM_AREA" width="%s" height="%s" left="0" top="%s" splitable="false" >', $layoutWidth, $layoutHeight, $layoutTop);
        foreach (json_decode($templateArr['merge_template_url'], true) as $item) {
            $left = number_format(($item['left'] / 1), 2);
            $top = number_format(($item['top'] / 1), 2);
            $width = number_format($item['width'] / 1, 2);
            $height = number_format(($item['height'] / 1), 2);
            $fontFamily = $item['fontFamily'];
            $fontWeight = $item['fontWeight'];
            $fontSize = intval($item['fontSize'] * 0.75);
            if ($item['id'] == 18) {
                $key = 'custom';
            } else {
                $key = PrintDataService::getCustomKey($item['value']);
            }
            $zindex = 1;
            if ($key == 'watermark') {
                $zindex = 111;
            }
            $sizeFixedValue = $item['sizeFixedValue'] ?? 0;
            //$str .= "<text left='".$left."' top='".$top."' width='".$width."' height='".$height."' style='fontFamily:".$fontFamily.";fontWeight:".$fontWeight.";fontSize:".$fontSize.";'><![CDATA[<%=_data.$key%>]]></text>";
            $str .= '<text left="' . $left . '" top="' . $top . '" width="' . $width . '" height="' . $height .
                '" sizeFixedValue="'.$sizeFixedValue.'" style="fontFamily:SimSun;fontSize:' . $fontSize .
                ';zIndex:'.$zindex.';fontWeight:' . $fontWeight . ';"><![CDATA[ <%= _data.' . $key . ' %> ]]></text>';
        }
        switch ($size){
            default:
            case Template::TEMPLATE_SIZE_76_130:
                $nextContentHeight = intval(130-$layoutHeight);
                break;
            case Template::TEMPLATE_SIZE_100_180:
                $nextContentHeight = intval(180-$layoutHeight);
                break;
            case Template::TEMPLATE_SIZE_100_150:
                $nextContentHeight = intval(150-$layoutHeight);
                break;
        }
        $str .= '<text left="0" top="'. $layoutHeight .'" width="'.$layoutWidth.'" height="'.$nextContentHeight.'" isNextContents="1" style="fontFamily:SimSun;fontSize:14;zIndex:111;fontWeight:bold;"><![CDATA[ <%= _data.nextContents %> ]]></text>';
        $str .= "</layout>";
        return $str;
    }
}
