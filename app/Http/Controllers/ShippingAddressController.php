<?php

namespace App\Http\Controllers;

use App\Models\CustomizeOrder;
use App\Models\ShippingAddress;
use App\Models\Template;
use App\Services\BusinessException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ShippingAddressController extends Controller
{
    /**
     * @param Request $request
     * @return JsonResponse
     * <AUTHOR>
     * 22-1-25去掉了user_id查询条件
     * 收货地址列表
     */
    public function index(Request $request): JsonResponse
    {
        $shopId=intval($request->input('shopId',$request->auth->shop_id));
        $list = ShippingAddress::where('shop_id', $shopId)
            ->where('tip', ShippingAddress::IS_SENDER_DEFAULT_YES)
            ->get();
        collect($list)->each(
            function ($item) {
                $item->handle_str = $item->province . '-' . $item->city . '-' . $item->district . '-' . $item->address;

                return $item;
            }
        );

        return $this->success($list);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws BusinessException
     * <AUTHOR>
     * 22-1-25去掉了userid
     * 设置发货地址
     */
    public function add(Request $request)
    {
        $shopId=intval($request->input('shopId',$request->auth->shop_id));
        $data = $this->validate($request, [
            "sender_name" => "required|string",
            "mobile" => "required|string",
            "tel" => "string",
            "postal_code" => "string",
            "province" => "required|string",
            "city" => "required|string",
            "district" => "string",
            "address" => "required|string",
            "tip" => "int",
        ]);

        $data['user_id'] = $request->auth->user_id;
        $data['shop_id'] = $shopId;

        $tip = $request->get('tip') ? $request->get('tip') : ShippingAddress::IS_SENDER_DEFAULT_YES;
        $mobile = $request->get('mobile');
        $data['tip'] = $tip;
        $count = ShippingAddress::where('shop_id', $shopId)
            ->where('tip', $tip)
            ->count();

        if ($count == 0) {
            $data['is_default'] = ShippingAddress::IS_DEFAULT_YES;
        }

        $address = ShippingAddress::create($data);

        if (!$address) {
            throw new BusinessException('地址添加失败！');
        }

        return $this->success($address);
    }

    /**
     * 修改发货地址
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     * @throws BusinessException
     * <AUTHOR>
     * 22-1-25去掉了发货地址
     */
    public function edit(Request $request, $id)
    {
        $shopId=intval($request->input('shopId',$request->auth->shop_id));
        $data = $this->validate($request, [
            "sender_name" => "required|string",
            "mobile" => "required|string",
            "tel" => "string",
            "postal_code" => "string",
            "province" => "required|string",
            "city" => "required|string",
            "district" => "required|string",
            "address" => "required|string",
        ]);
        $address = ShippingAddress::where([
            'id' => $id,
            'shop_id' => $shopId,
        ])->update($data);
        if (!$address) {
            throw new BusinessException('地址更新失败！');
        }

        return $this->success($address);
    }

    /**
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     * @throws BusinessException
     * <AUTHOR>
     * 22-1-25删除userId
     * 删除发货地址
     */
    public function delete(Request $request, $id)
    {
        $shopId=intval($request->input('shopId',$request->auth->shop_id));
        $address = ShippingAddress::where([
            'id' => $id,
            'shop_id' => $shopId,
        ])->delete();
        if (!$address) {
            throw new BusinessException('地址删除失败！');
        }
        Template::query()->where('shipping_address_id', $id)->update(['shipping_address_id' => 0]);

        return $this->success($address);
    }

    /**
     * 设置默认发货地址
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     * @throws BusinessException
     * <AUTHOR>
     * 22-1-25 去掉了userId
     */
    public function default(Request $request, $id)
    {
        $shopId=intval($request->input('shopId',$request->auth->shop_id));
        ShippingAddress::query()->where([
            'is_default' => ShippingAddress::IS_DEFAULT_YES,
            'shop_id' => $shopId,
            'tip' => ShippingAddress::IS_SENDER_DEFAULT_YES
        ])->update([
            'is_default' => ShippingAddress::IS_DEFAULT_NO
        ]);
        $default = ShippingAddress::query()->where([
            'id' => $id,
            'shop_id' => $shopId,
            'tip' => ShippingAddress::IS_SENDER_DEFAULT_YES
        ])->update([
            'is_default' => ShippingAddress::IS_DEFAULT_YES
        ]);
        if (!$default) {
            throw new BusinessException('设置失败！');
        }

        return $this->success($default);
    }


    /**
     * <AUTHOR>
     * 22-1-25 删除userId
     * @param Request $request
     * @return JsonResponse
     */
    public function getDefault(Request $request)
    {
        $shopId=intval($request->input('shopId',$request->auth->shop_id));
        $default = ShippingAddress::where('shop_id', $shopId)
            ->where('tip', ShippingAddress::IS_SENDER_DEFAULT_YES)
            ->where('is_default', ShippingAddress::IS_DEFAULT_YES)
            ->first();

        return $this->success($default);
    }


    /**
     * 收货地址列表
     * @param Request $request
     * @return JsonResponse
     * <AUTHOR>
     * 22-1-25 去掉了userId
     */
    public function ReceiverList(Request $request)
    {
        $shopId=intval($request->input('shopId',$request->auth->shop_id));
        $list = ShippingAddress::where('shop_id', $shopId)
            ->where('tip', ShippingAddress::IS_SENDER_DEFAULT_NO)
            ->get();
        collect($list)->each(
            function ($item) {
                $item->handle_str = $item->province . '-' . $item->city . '-' . $item->district . '-' . $item->address;

                return $item;
            }
        );

        return $this->success($list);
    }


    /**
     * 设置默认收件地址
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     * @throws BusinessException
     * <AUTHOR>
     * 22-1-25 去掉了userId
     */
    public function ReceiverDefault(Request $request, $id)
    {
        $shopId=intval($request->input('shopId',$request->auth->shop_id));
        ShippingAddress::query()->where([
            'is_default' => ShippingAddress::IS_DEFAULT_YES,
            'shop_id' => $shopId,
            'tip' => ShippingAddress::IS_SENDER_DEFAULT_NO
        ])->update([
            'is_default' => ShippingAddress::IS_DEFAULT_NO
        ]);
        $default = ShippingAddress::query()->where([
            'id' => $id,
            'shop_id' => $shopId,
            'tip' => ShippingAddress::IS_SENDER_DEFAULT_NO
        ])->update([
            'is_default' => ShippingAddress::IS_DEFAULT_YES
        ]);
        if (!$default) {
            throw new BusinessException('设置失败！');
        }

        return $this->success($default);
    }


    //搜索查询收件人

    /**
     * <AUTHOR>
     * 22-1-25去掉userId
     * @param Request $request
     * @return JsonResponse
     */
    public function ReceiverSearch(Request $request)
    {
        $shopId=intval($request->input('shopId',$request->auth->shop_id));
        $keywords = $request->get('keyword');
        if (!empty($keywords)) {
            $data = ShippingAddress::where('shop_id', $shopId)
                ->where('tip', ShippingAddress::IS_SENDER_DEFAULT_NO)
                ->where('sender_name', 'like', '%' . $keywords . '%')
                ->orWhere('mobile', 'like', "$keywords%")->get();
            return $this->success($data);
        }
    }


}
