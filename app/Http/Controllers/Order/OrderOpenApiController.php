<?php

namespace App\Http\Controllers\Order;

use App\Http\Controllers\Controller;
use App\Models\Fix\Order;
use App\Models\Fix\Shop;
use App\Services\Order\OrderServiceManager;
use Illuminate\Http\Request;

class OrderOpenApiController extends Controller
{
    public function batchMark(Request $request)
    {
        $data = $this->validate($request, [
            'list' => 'required|array',
            'list.*.identifier' => 'required|string',
            'list.*.tid' => 'required|string',
            'list.*.sourceType' => 'required|string',
            'list.*.assignType' => 'required|string|in:'.implode(',', Order::ASSIGN_TYPE_ARRAY),
        ]);
        $list = $data['list'];
        $shops = Shop::query()->whereIn('identifier', array_column($list, 'identifier'))->get();
//        $redis = redis('cache');
        collect($list)->groupBy('identifier')->each(function ($items) use ($shops) {
            $shop = $shops->firstWhere('identifier', $items[0]['identifier']);
            if (empty($shop)){
                throw new \Exception('店铺不存在');
            }
            $orderService = OrderServiceManager::create($shop->getPlatform());
            $orderService->setShop($shop);
            if (!$orderService->checkAuthStatus()) {
                throw new \Exception(sprintf('店铺%s授权过期', $shop->shop_name));
            }
            $thisOrders = [];
            foreach ($items as $item) {
                $thisOrders[] = ['tid' => $item['tid'], 'id' => $item['tid']];
            }
            $tradesOrders = $orderService->batchGetOrderInfo($thisOrders);
            foreach ($tradesOrders as $index => $tradesOrder) {
                $tradesOrders[$index] = array_merge($tradesOrder, [
                    'assign_type' => $items[0]['assignType'],
                ]);
            }
            Order::batchSave($tradesOrders, $shop->user_id, $shop->id);
        });
        return $this->success();
    }
}
