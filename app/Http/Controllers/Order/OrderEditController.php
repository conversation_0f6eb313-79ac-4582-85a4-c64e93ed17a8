<?php

namespace App\Http\Controllers\Order;

use App\Constants\PlatformConst;
use App\Events\Orders\OrderUpdateEvent;
use App\Exceptions\ErrorCodeException;
use App\Exceptions\OrderException;
use App\Exceptions\ShopCheckException;
use App\Http\StatusCode\StatusCode;
use App\Jobs\Orders\SyncSaveOrders;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Shop;
use App\Models\ShopBind;
use App\Models\User;
use App\Services\Order\OrderRemarkService;
use App\Services\Order\OrderServiceManager;
use App\Utils\Environment;
use App\Utils\OrderUtil;
use App\Utils\StrUtil;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Illuminate\Validation\ValidationException;
use Maatwebsite\Excel\Facades\Excel;
use App\Utils\FileUploadUtil;
use Swoole\Coroutine\WaitGroup;
use function Swoole\Coroutine\run;

/**
 * 订单修改
 */
class OrderEditController extends Controller
{

    private $orderRemarkService;

    /**
     * @param OrderRemarkService $orderRemarkService
     */
    public function __construct(OrderRemarkService $orderRemarkService)
    {
        $this->orderRemarkService = $orderRemarkService;
    }

    /**
     * 上传文件修改订单
     * @param Request $request
     * @return void
     */
    public function uploadOrder(Request $request)
    {
        $this->validate($request, [
            'file' => 'required|file|mimes:xls,xlsx',
        ]);
        $file = $request->file('file');
        if ($file->isValid()) {
            $ret = $this->uploadSetOrderRemark($request->auth->user_id, $request->auth->shop_id,
                $file->getClientOriginalName(),
                $file->getRealPath()
            );
            if (!$ret) {
                return $this->fail('操作失败');
            }
            return $this->success('success');
        }
    }


    /**
     * 批量修改代打
     * @param Request $request
     * @return JsonResponse
     * @throws ErrorCodeException
     * @throws ShopCheckException
     * @throws ValidationException
     */

    public function batchSetRemark(Request $request): JsonResponse
    {
        $this->validate($request, [
            'orders' => 'string', // 二选一
            'orderIds' => 'string', // 二选一
            'flag' => 'string',
            'remark' => 'string',
            'locked' => 'boolean',
            'isAppend' => 'boolean',
        ]);

        $orders = $request->input('orders');
        $orderIds = $request->input('orderIds');
        $tids = StrUtil::splitString($orders);
        $remark = $request->input('remark');
        $locked = $request->input('locked', false);
        $isAppend = $request->input('isAppend', false);
        $flag = $request->input('flag', 'GRAY');
        $currentShopId = $request->auth->shop_id;
        $failList = $this->orderRemarkService->batchEditRemark($request->auth->user_id, $currentShopId, $tids,$orderIds, $flag, $remark,
            $locked, $request,$isAppend);
        $data = '操作成功';
        if (!empty($failList)) {
            $data = $failList;
        }
        return $this->success($data);
    }


    /**
     *
     * 更新指定的物流
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     */

    public function batchSetSmartLogistics(Request $request): JsonResponse
    {
        $data = $this->validate($request, [
            'tids' => 'required|array',
            'wpCode' => 'required|string',
        ]);
        Order::query()->whereIn('tid', $data['tids'])->update(['smart_logistics' => $data['wpCode']]);
        return $this->success();

    }


    /**
     * 解析导入的文件，批量修改订单
     * @param string $userId
     * @param int $shopId
     * @param string $fileName 导入文件的名称
     * @param string $path 导入文件的路径
     * @return bool
     * @throws \Exception
     */

    public function uploadSetOrderRemark(string $userId, int $shopId, string $fileName, string $path)
    {
        $data = FileUploadUtil::uploadFileReadExcel($fileName, $path);
        if (empty($data) || !is_array($data)) {
            Log::warn('数据解析错误', [$shopId, $path, $data]);
            return false;
        }
        $shop = Shop::find($shopId);
        $orderService = OrderServiceManager::create(PlatformConst::PLATFORM_TYPE_MAP_REVERT[$shop->type]);
        $orderService->setUserId($userId);
        $orderService->setShop($shop);
        $shopIds = ShopBind::getAllRelationShopIds($shopId);

        $tids = [];
        foreach ($data as $datum) {
            $tids [] = trim($datum[0]);
        }
        $tids = OrderUtil::batchAppendOrderSuffix($tids);// $this->appendOrderSuffix($tids);
        $tidOrderMap = Order::query()->whereIn('shop_id', $shopIds)->whereIn('tid', $tids)->get()->keyBy("tid");
        \Log::info("批量导入订单匹配到" . $tidOrderMap->count() . "条");
        foreach ($data as $datum) {
            $tid = trim($datum[0]);
            $order = $tidOrderMap->get($tid);
            $tid = OrderUtil::appendOrderSuffix($tid);
            $order = $tidOrderMap->get($tid);
            $remark = trim($datum[1]);
            $locked = trim($datum[2]) > 0 ? true : false;

            $orderData = [];
            $orderData['locked_at'] = null;
            if ($locked) {
                $orderData['locked_at'] = date('Y-m-d H:i:s');
            }
            if ($remark) {
                $orderData['seller_memo'] = json_encode([$remark]);
            }
            if ($order) {
                $shop = $order->shop;
                $userId = $order->user_id;
                $flag = $order->seller_flag;
                $orderService = OrderServiceManager::create(PlatformConst::PLATFORM_TYPE_MAP_REVERT[$shop->type]);
                $orderService->setUserId($userId);
                $orderService->setShop($shop);

                //请求平台修改留言备注
                $tid = $order->tid;
                $res = $orderService->sendEditSellerRemark($tid, $flag, $remark);
                Log::info("批量更新订单 tid=" . $tid . ",result" . $res);
                if (!$res) {
                    return false;
                }
                Order::query()
                    ->where('shop_id', $order->shop_id)
                    ->where('tid', $tid)
                    ->update($orderData);
            }

        }
        return true;
    }

    /**
     * 修改卖家备注和旗帜
     * @param Request $request
     * @param $id
     * @return JsonResponse
     * @throws ErrorCodeException
     * @throws \Throwable
     */
    public function sellerNoteList(Request $request, $id): JsonResponse
    {
        $sellerFlag = $request->input('seller_flag');
        $sellerMemo = $request->input('seller_memo');

        $columns = ['id', 'tid', 'seller_flag', 'seller_memo'];
        $beforeOrders = Order::query()->where('id', $id)->get($columns)->toArray();
        //修改留言备注
        $ret = $this->orderRemarkService->editRemark($id, $sellerFlag, $sellerMemo);

        if (!$ret) {
            return $this->fail('编辑失败', 400);
        }
        $afterOrder = Order::query()->where('id', $id)->get($columns)->toArray();
        $user = User::query()->where('id', $request->auth->user_id)->first();
        $shop = Shop::query()->where('id', $request->auth->shop_id)->first();
        event((new OrderUpdateEvent($user, $shop, time(), $beforeOrders, $afterOrder, 'flag'))->setClientInfoByRequest($request));

        return $this->success('编辑成功');
    }


    /**
     * @throws ValidationException
     */
    public function batchUpdateSkuValue(Request $request): JsonResponse
    {
        $this->validate($request, [
            'tids' => 'required|array',
            'type' => 'required|string',
            'is_append' => 'required|string',
            'sku_value' => 'string',
        ]);

        $tids = $request->input('tids');
        $type = $request->input('type');//1自定义 2使用留言
        $isAppend = $request->input('is_append');//1完全替换 2后面追加
        $skuValue = $request->input('sku_value');

        $failed = 0;
        foreach ($tids as $tid) {
            $order = Order::query()->where('tid', $tid)->first();
            $buyerMessage = $order->buyer_message;

            $skuValue = $type == '1' ? $skuValue : $buyerMessage;
            //考虑到首页排序问题，同步修改orders中的sku_value、sku_value_last
            if ($isAppend == '1') {
//                Order::query()->where('id', $tid)->update([
//                    'sku_value' => $skuValue,
//                    'sku_value_last' => $skuValue
//                ]);
                $order->sku_value = $skuValue;
                $order->sku_value_last = $skuValue;
                $order->save();
                $order->orderItem()->update(['custom_order_sku_value' => $skuValue]);
//                OrderItem::query()->whereIn('id', $item['oids'])->update(['custom_order_sku_value' => $skuValue]);
            } else {
                $order->sku_value=$order->sku_value.$skuValue;
                $order->sku_value_last=$order->sku_value_last.$skuValue;
                $order->save();
//                Order::query()->where('id', $tid)->update([
//                    'sku_value' => \DB::raw("concat(sku_value, '$skuValue')"),
//                    'sku_value_last' => \DB::raw("concat(sku_value_last, '$skuValue')")
//                ]);
                $order->orderItem()->update(['custom_order_sku_value' => \DB::raw("concat(sku_value, '$skuValue')")]);
//                OrderItem::query()->whereIn('id', $item['oids'])->update(['custom_order_sku_value' => \DB::raw("concat(sku_value, '$skuValue')")]);
            }
        }
        return $this->success(['failed' => $failed]);
    }
//
//    /**
//     * 修改平台备注
//     * @param Request $request
//     * @param $id
//     * @return \Illuminate\Http\JsonResponse
//     */
//    public function editPlatformMemo(Request $request, $id)
//    {
//        $exist = Order::findOrFail($id);
//        $exist->platform_memo = $request->input('platform_memo');
//        if (!$exist->save()) {
//            return $this->fail('编辑失败', 400);
//        }
//
//        return $this->success('编辑成功');
//    }

}
