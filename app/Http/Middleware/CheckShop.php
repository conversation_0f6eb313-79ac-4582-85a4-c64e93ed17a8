<?php
namespace App\Http\Middleware;

use App\Http\StatusCode\StatusCode;
use App\Models\ApiAuthShop;
use App\Models\Shop;
use App\Models\UserExtra;
use Closure;
use Illuminate\Http\Request;

class CheckShop{
    /**
     * @param Request $request
     * @param Closure $next
     * @return mixed|void
     * @throws \App\Exceptions\ErrorCodeException
     */
    public function handle(Request $request, Closure $next)
    {

        // 校验shopToken
        $shopToken = $request->input('shop_token');
        if (empty($shopToken)) {
            return throw_error_code_exception(StatusCode::PARAMS_ERROR);
        }

        // 根据shopToken获取店铺唯一标识
        $apiAuthShopInfo = ApiAuthShop::query()->where('shop_token', $shopToken)->first();
        if (empty($apiAuthShopInfo)) {
            return throw_error_code_exception(StatusCode::SHOP_TOKEN_ERROR);
        }

        // 店铺信息
//        $shopInfo = Shop::query()->where('identifier', $apiAuthShopInfo->shop_identifier)->first();
        $shopInfo = Shop::firstByIdentifier($apiAuthShopInfo->shop_identifier);
        if (empty($shopInfo)) {
            return throw_error_code_exception(StatusCode::SHOP_UN_EXIST);
        }
        //店铺授权是否正常
        if (!empty($shopInfo->access_token) && $shopInfo->auth_status != Shop::AUTH_STATUS_SUCCESS) {
            return throw_error_code_exception(StatusCode::SHOP_AUTH_ERROR);
        }

        // 是否过期
        $userExtraInfo = UserExtra::query()->where('identifier', $apiAuthShopInfo->shop_identifier)->orderBy('created_at', 'desc')->first();
        if (empty($userExtraInfo) || strtotime($userExtraInfo->expire_at) < time()) {
            return throw_error_code_exception(StatusCode::SHOP_NO_BUY);
        }

        $request->user_id = $shopInfo->user_id;
        $request->shop_id = $shopInfo->id;
        $request->shop = $shopInfo;

        return $next($request);
    }
}
