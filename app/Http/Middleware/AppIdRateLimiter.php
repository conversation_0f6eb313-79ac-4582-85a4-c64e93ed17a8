<?php

namespace App\Http\Middleware;
use App\Constants\ErrorConst;
use App\Exceptions\ApiException;
use App\Exceptions\ErrorCodeException;
use App\Http\StatusCode\StatusCode;
use App\Models\ApiAuth;
use Closure;
use Illuminate\Cache\RateLimiter;
use Illuminate\Http\Request;

/**
 * 限速
 */
class AppIdRateLimiter
{
    /**
     * The rate limiter instance.
     *
     * @var \Illuminate\Cache\RateLimiter
     */
    protected $limiter;

    /**
     * Create a new request throttler.
     *
     * @param  \Illuminate\Cache\RateLimiter $limiter
     */
    public function __construct(RateLimiter $limiter)
    {
        $this->limiter = $limiter;
    }

    /**
     * 根据appId进行限流
     * Handle an incoming request.
     *
     * @param Request $request
     * @param \Closure $next
     * @return mixed
     * @throws ErrorCodeException
     */
    public function handle($request, Closure $next)
    {
        $appId = $request->input('appId');
        if (empty($appId)) {
            return $next($request);
        }
        // 每$decayMinutes分钟限制多少次
        $decayMinutes = 1;
        $maxAttempts = \Cache::remember('appidRequestFreqLimit:' . $appId, 60 * 1, function () use ($appId) {
            $info = ApiAuth::query()->where('app_id', $appId)->first();
            if (empty($info)) {
                return null;
            }
            return $info->request_freq_limit;
        });
        if ($maxAttempts <= 0){ // 0 不限制
            $maxAttempts = 1000; // 默认值
        }
//        $key = $this->resolveRequestSignature($request,$appId);
        $key = $appId;

        if ($this->limiter->tooManyAttempts($key, $maxAttempts)) {
            return throw_error_code_exception(StatusCode::RATE_LIMITED,null,'每分钟请求数上限'.$maxAttempts.'次');
        }

        $this->limiter->hit($key, $decayMinutes);

        return  $next($request);

//        return $this->addHeaders(
//            $response, $maxAttempts,
//            $this->calculateRemainingAttempts($key, $maxAttempts)
//        );
    }

    /**
     * Resolve request signature.
     *
     * @param  \Illuminate\Http\Request $request
     * @return string
     */
    protected function resolveRequestSignature($request,$token)
    {
        return md5($token.'|'.$request->path());
    }

    /**
     * Create a 'too many attempts' response.
     *
     * @param  string $key
     * @param  int $maxAttempts
     * @return \Illuminate\Http\Response
     */
    protected function buildResponse($key, $maxAttempts)
    {
        $response = response()->json([
            'code' => 429,
            'message' => '您的请求过于频繁, 请稍后再试',
        ]);

        $retryAfter = $this->limiter->availableIn($key);

        return $this->addHeaders(
            $response,
            $maxAttempts,
            $this->calculateRemainingAttempts($key, $maxAttempts, $retryAfter),
            $retryAfter
        );
    }

    /**
     * Add the limit header information to the given response.
     *
     * @param  \Symfony\Component\HttpFoundation\Response $response
     * @param  int $maxAttempts
     * @param  int $remainingAttempts
     * @param  int|null $retryAfter
     * @return \Illuminate\Http\Response
     */
    protected function addHeaders(Response $response, $maxAttempts, $remainingAttempts, $retryAfter = null)
    {
        $headers = [
            'X-RateLimit-Limit' => $maxAttempts,
            'X-RateLimit-Remaining' => $remainingAttempts,
        ];

        if (!is_null($retryAfter)) {
            $headers['Retry-After'] = $retryAfter;
            $headers['Content-Type'] = 'application/json';
        }

        $response->headers->add($headers);

        return $response;
    }

    /**
     * Calculate the number of remaining attempts.
     *
     * @param  string $key
     * @param  int $maxAttempts
     * @param  int|null $retryAfter
     * @return int
     */
    protected function calculateRemainingAttempts($key, $maxAttempts, $retryAfter = null)
    {
        if (!is_null($retryAfter)) {
            return 0;
        }

        return $this->limiter->retriesLeft($key, $maxAttempts);
    }
}
