<?php

namespace App\Http\Middleware;

use App\Http\StatusCode\StatusCode;
use App\Models\ApiAuth;
use App\Models\ApiShopBind;
use Closure;

class CheckApiShopBind
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $appId = $request->get('appId', '');
        if (empty($request->shop)) {
            return $next($request);
        }
        if (in_array($appId, ['16863066765337','16884393554734'])) { // 不验证店铺code
            return $next($request);
        }
        //好礼自己的不验证店铺code
        if (in_array($appId, ['16860511429552','17275738482769','17000988293841','17272515727425','17272537129341','17272538884710','17272538128387','16521539526624','16993388042341','16384286301188','16521661244010','17290569038935',"17332267697846","17338130849772"])) {
            return $next($request);
        }
        $shopId = $request->shop->id;
        $apiShopBind = ApiShopBind::firstByAppIdShopId($appId, $shopId);
        if (empty($apiShopBind)) {
            //把这个店铺关联的所有appId查出来
            $appIdsByShopId = ApiShopBind::findAppIdsByShopId($shopId);
            //再把这些appId的授权appId查出来
            $allAuthedAppId = ApiAuth::getAllAuthedAppIds($appIdsByShopId);
            //看看当前appId是否在授权的appId里面
            if(!in_array($appId,$allAuthedAppId)){
                \Log::info("检查店铺绑定失败", ['appId' => $appId, 'allAuthedAppId' => $allAuthedAppId]);
                throw_error_code_exception(StatusCode::SHOP_NOT_BOUND);
            }
        }

        return $next($request);
    }
}
