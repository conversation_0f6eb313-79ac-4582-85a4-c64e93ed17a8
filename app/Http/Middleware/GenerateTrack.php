<?php

namespace App\Http\Middleware;

use Closure;
use Log;

/**
 * 生成track,用于日志处理
 */
class GenerateTrack
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  \Closure $next
     *
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $request->trackId = strtoupper(md5(uniqid(mt_rand(), true)));
        return $next($request);
    }
}
