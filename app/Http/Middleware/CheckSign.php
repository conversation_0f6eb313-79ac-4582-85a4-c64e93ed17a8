<?php
namespace App\Http\Middleware;

use App\Http\StatusCode\StatusCode;
use App\Models\ApiAuth;
use Closure;
use Illuminate\Http\Request;

class CheckSign
{

    /**
     * @param Request $request
     * @param Closure $next
     * @return mixed|void
     * @throws \App\Exceptions\ErrorCodeException
     */
    public function handle(Request $request, Closure $next)
    {
        $app_id = $request->input('app_id', '');
        if (empty($app_id)) {
            return throw_error_code_exception(StatusCode::APP_ID_NOT_EXIST);
        }
        $sign = $request->input('sign', '');
        if (empty($sign)) {
            return throw_error_code_exception(StatusCode::SIGN_NOT_EXIST);
        }
        $info = ApiAuth::query()->where('app_id', $app_id)->first();
        if (!$info) {
            return throw_error_code_exception(StatusCode::APP_ID_ERROR);
        }
        $app_key = $info->app_key;

        $paramArr = $request->input();
        $bool = decryptApiSign($paramArr, $app_key);
        if (!$bool){
            return throw_error_code_exception(StatusCode::SIGN_ERROR);
        }

        return $next($request);
    }
}
