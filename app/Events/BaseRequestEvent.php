<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2021/7/28
 * Time: 16:47
 */

namespace App\Events;


use Illuminate\Http\Request;

class BaseRequestEvent extends Event
{
    /**
     * @var array
     */
    public $clientInput = [];
    /**
     * @var string
     */
    public $clientUrl = '';
    /**
     * @var string
     */
    public $clientIp = '';
    /**
     * 淘宝御城河的 ati
     * @var string
     */
    public $ati = '';
    /**
     * @var string
     */
    public $referer = '';
    /**
     * @var string
     */
    public $dyEventId;

    public function setClientInfoByRequest(?Request $request): BaseRequestEvent
    {
        if(!$request){
            return $this;
        }
        $this->clientIp = $request->ip();
        $this->clientUrl = $request->url();
        $this->clientInput = $request->input();
        $this->ati = request()->cookie('_ati', '');
        $this->referer = $request->header('referer', '');
        $this->dyEventId = $request->header('doudian-event-id', '');
        return $this;
    }
}
