<?php

namespace App\Events\Orders;

use App\Events\BaseRequestEvent;
use App\Models\Shop;
use App\Models\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;

class OrderExportEvent extends BaseRequestEvent
{

     public $user;
     public $shop;
     public $time;

    /**
     * @var array
     */
     public $orderIds;

    /**
     * @var array
     */
     public $queryConditions;
    /**
     * @var Request
     */
     public $request;


    /**
     *
     * @param $operatingUser
     * @param $shop
     * @param $time int 时间
     * @param array $orderIds 订单 tid 数组
     * @param array $queryConditions 查询条件
     */
    public function __construct($operatingUser, $shop, int $time, array $orderIds, array $queryConditions = [])
    {
        $this->user = $operatingUser;
        $this->shop = $shop;
        $this->time = $time;
        $this->orderIds = $orderIds;
        $this->queryConditions = $queryConditions;
        $this->request = $request;

    }
}
