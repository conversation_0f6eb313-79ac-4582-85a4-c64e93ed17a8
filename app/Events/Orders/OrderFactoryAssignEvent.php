<?php

namespace App\Events\Orders;

use App\Events\BaseRequestEvent;
use App\Models\Shop;
use App\Models\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;

class OrderFactoryAssignEvent extends BaseRequestEvent
{

    public $user;
    public $shop;
    public $time;
    public $orderInfo;
    public $targetShopId;


    /**
     *
     * @param $user
     * @param $shop
     * @param $time int 时间
     */
    public function __construct($user, $shop, int $time, $orderInfo, $targetShopId)
    {
        $this->user = $user;
        $this->shop = $shop;
        $this->time = $time;
        $this->orderInfo = $orderInfo;
        $this->targetShopId = $targetShopId;
    }
}
