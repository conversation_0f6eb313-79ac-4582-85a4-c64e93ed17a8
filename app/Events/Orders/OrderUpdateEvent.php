<?php

namespace App\Events\Orders;

use App\Events\BaseRequestEvent;
use App\Models\Order;
use App\Models\Shop;
use App\Models\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;

class OrderUpdateEvent extends BaseRequestEvent
{

    public $user;
    public $shop;
    public $time;

    /**
     * @var array
     */
    public $orderIds;

    /**
     * @var int
     */
    public $orderTotal;

    /**
     * @var array
     */
    public $beforeOrders;
    /**
     * @var array
     */
    public $afterOrders;
    /**
     * @var string
     */
    public $type;

    /**
     *
     * @param $operatingUser
     * @param $shop
     * @param $time int 时间
     * @param array $beforeOrders [id,tid, ....]
     * @param array $afterOrders [id,tid, ....]
     */
    public function __construct($operatingUser, $shop, int $time, array $beforeOrders, array $afterOrders, string $type)
    {
        $this->user = $operatingUser;
        $this->shop = $shop;
        $this->time = $time;
        $this->orderIds = array_pluck($beforeOrders,'tid');
        $this->beforeOrders = $beforeOrders;
        $this->afterOrders = $afterOrders;
        $this->type = $type;
    }
}
