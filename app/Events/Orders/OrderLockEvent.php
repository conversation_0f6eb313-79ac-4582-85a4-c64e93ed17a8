<?php

namespace App\Events\Orders;


use App\Events\BaseRequestEvent;

/**
 * 订单锁定事件
 */
class OrderLockEvent extends BaseRequestEvent
{

    public $user;
    public $shop;
    public $time;
    /**
     * @var array [[id,tid,xxx]]
     */
    public $orderList;

    public function __construct($operatingUser, $shop, int $time, $orderList = [])
    {
        $this->user = $operatingUser;
        $this->shop = $shop;
        $this->time = $time;
        $this->orderList = $orderList;
    }
}
