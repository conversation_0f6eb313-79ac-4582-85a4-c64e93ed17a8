<?php
/**
 * Created by PhpStorm.
 * User: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * Date: 2022/5/30
 * Time: 14:53
 */
namespace App\Exceptions;

class PrintException extends \Exception
{
    protected $code;
    protected $data;
    protected $msg;

    /**
     * ErrorCodeException constructor.
     * @param array $statusCode
     * @param null $data
     * @param string $info
     */
    public function __construct($data, $code, $msg = '')
    {
        parent::__construct($msg, $code);
        $this->data = $data;
        $this->code = $code;
        $this->msg = $msg;

    }

    public function getStatusCode()
    {
        return $this->code;
    }

    public function getData()
    {
        return $this->data;
    }

    public function getMsg()
    {
        return $this->msg;
    }
}