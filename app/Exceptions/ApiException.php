<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2020/3/3
 * Time: 15:28
 */

namespace App\Exceptions;


use Exception;
use Throwable;

class ApiException extends Exception
{
    private $errorCode;

    /**
     * 构造函数
     * @see \App\Constants\ErrorConst
     * @param array $errorConst
     * @param int $code
     * @param Throwable|null $previous
     */
    public function __construct(array $errorConst, $code = 400, Throwable $previous = null)
    {
        $this->errorCode = $errorConst[0];
        $code == 400 and $code = $errorConst[0];
        parent::__construct($errorConst[1], $code, $previous);
    }

    /**
     * @return int
     */
    public function getErrorCode(): int
    {
        return $this->errorCode;
    }
}
