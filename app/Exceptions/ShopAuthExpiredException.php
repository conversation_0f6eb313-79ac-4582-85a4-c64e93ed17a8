<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2020/3/3
 * Time: 15:28
 */

namespace App\Exceptions;


use App\Constants\ErrorConst;
use Exception;
use Throwable;

class ShopAuthExpiredException extends Exception
{
    private $errorCode;

    /**
     * 构造函数
     * @param string $message
     * @param int $code
     * @param Throwable|null $previous
     * @see \App\Constants\ErrorConst
     */
    public function __construct($message = "店铺授权过期", $code = 0, Throwable $previous = null)
    {
        $this->errorCode = ErrorConst::PLATFORM_SHOP_AUTH_EXPIRED[0];
        parent::__construct($message, $code, $previous);
    }

    /**
     * @return int
     */
    public function getErrorCode(): int
    {
        return $this->errorCode;
    }
}
