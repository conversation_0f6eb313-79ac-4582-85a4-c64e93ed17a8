<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2020/3/3
 * Time: 15:28
 */

namespace App\Exceptions;


use Exception;
use Throwable;

class OrderException extends Exception
{
    /**
     * 构造函数
     *
     * @param string $message
     * @param integer $code 错误码
     * @param Throwable|null $previous
     */
    public function __construct($message = "", $code = 400, Throwable $previous = null)
    {
        parent::__construct($message, $code, $previous);
    }
}
