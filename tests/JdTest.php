<?php

use Illuminate\Support\Facades\Log;
use Jos<PERSON>rderOaidMerge\MergeItem;
use Jd<PERSON><PERSON> as JdClientSdk;
class JdTest extends TestCase
{
    public function testOaidMerge()
    {
        $req = new JosOrderOaidMergeRequest();
        $request = new \JosOrderOaidMerge\Request();

        $input=[];
        $item1 = new MergeItem();
        $tid1 = "296439870749";
        $item1->setOrderId($tid1);
        $oaid1 = "YjRiZjNjABT5Yz05DTH47QCKVJNOXZu1Yjk1Mmt8rGdcKrxi4MGlCjNuPZDLVfKRYwp/n9VEMbOKJBejuuVB0rQhWMShZgVAkqoIqYAc5Sx2apGP2I9hCVz93OKMqr3BuL+M2IIPObypnEOpGLMOZ1nCxnaM9uBZKrS/PA==";
        $item1->setOaid($oaid1);
        $input[] = ["order_id" => $tid1, "oaid" => $oaid1];
        $item2 = new MergeItem();
        $oaid2 = "300448599872";
        $item2->setOrderId($oaid2);
        $oaid2 = "MGE4YjI5ABT5Yz05DTH47QCKVJNOXZu1Yjk1MvW3iFBzPADmr4hHuiYdv9GDztm4iusImummvByUsMU3YZ0q6C6/tvRvnx0KcsUHGZqfTd8xKOrKEip12ugF5OoSCMqrnNHNcnGBnM9gV20y47DAXWVWlewH6fWCmQqLKQ==";
        $input[] = ["order_id" => $oaid2, "oaid" => $oaid2];
        $item2->setOaid($oaid2);
        $merge_list[] = $item1;
        $merge_list[] = $item2;
        $request->setMergeList($merge_list);
        $client = new JdClientSdk();
        $client->appKey = config('socialite.jd.client_id');
        $client->appSecret = config('socialite.jd.client_secret');
        $client->accessToken = "f6414c220c5a4edcb1cc6125c8369143trjm";


        $req->setRequest($request->getInstance());


        $result = $client->execute($req, "ee8a582d92ff4219987c3193f87d76524mmq");
        echo('订单合并检查'.json_encode($result));


    }
}
