<?php

use App\Models\Shop;
use Illuminate\Support\Facades\Log;

class TaoBaoTest extends TestCase
{
    public function  testGetWaybillByOaid(){
        $request=new CainiaoWaybillIiGetRequest();
        $sender=[
            'sender_name'=>'张三',
            'mobile'=>'13800138000',
            'province'=>'广东省',
            'city'=>'深圳市',
            'district'=>'南山区',
            'address'=>'科技园中一路',
            'zip_code'=>'518000',
        ];
        $clientId="12519315";
        $secret="2713ce10a60ef95bf05de979429de2c2";
        $accessToken="7001210091578545bc2234a93e91e7bdd8ff1f8a8d92b12801a84f7b476edf84ad7249b75758527";
        $c = new TopClient($clientId, $secret);
        $w=new WaybillCloudPrintApplyNewRequest();
        $w->cp_code='POSTB';


        //发货人信息
        $senderDto = $this->setSender($sender);
        $w->sender = $senderDto;
        //面单信息
        $objectId = "sfsaadfaf";
        $order =["id"=>1,
            "tid"=>"3245074635133494323",
            "order_item"=>[["goods_title"=>"name","goods_num"=>1]],
//            "receiver_city"=>"嘉兴市",
//            "receiver_district"=>"秀洲区",
//            "receiver_state"=>"浙江省",
//            "receiver_address"=>"新平路盛世豪庭盛世香园11幢1单元202査米真丝",
//            "receiver_name"=>"张三",
//            "receiver_phone"=>"13800138000",
            'order_cipher_info'=>['oaid'=>'1mhib0EBOKkL33t11gYbcBg6DGyTJn0cdSINFHrClPrMMPu5t1bIPQGQCQUERPvDkFz63FaO']
        ];

        $trade_order_info_dtos = $this->setTradeOrderInfo($order,"75758527", $objectId);
        $w->trade_order_info_dtos = $trade_order_info_dtos;
        //打印报文是否加密
        $w->need_encrypt = "true";
        $request->setParamWaybillCloudPrintApplyNewRequest(json_encode($w));
        $waybill = $c->execute($request, $accessToken);
        echo(json_encode($waybill,JSON_PRETTY_PRINT));

        assert(true);
    }
    private function setSender($senderAddress)
    {
        $address = new AddressDto;
        $address->city=$senderAddress['city'];
        $address->detail=$senderAddress['address'];
        $address->district=$senderAddress['district'];
        $address->province=$senderAddress['province'];
        $address->town="";
        $sender = new UserInfoDto;
        @$sender->address = $address;
        $sender->mobile=$senderAddress['mobile'];
        $sender->name=$senderAddress['sender_name'];
        $sender->phone="";

        return $sender;
    }

    private function setTradeOrderInfo($order, $userId, $objectId)
    {
        $trade_order_info_dtos = new TradeOrderInfoDto;
        $trade_order_info_dtos->logistics_services="";
        $trade_order_info_dtos->object_id=$objectId;
        //订单信息
        $order_info = new OrderInfoDto;
        //订单渠道
        $order_info->order_channels_type=  "TB";
        if (isset($order['tid'])) {
            $order_tid = $order['tid'];
        }elseif (!empty($order['order_no'])) {
            $order_tid = $order['order_no'];
        }else{
            $order_tid = $order['id'];
        }
        $order_info->trade_order_list=[
//            isset($order['tid']) ? $order['tid'] . rand(0000, 9999) : $order['id'] . rand(0000, 9999)
            $order_tid
        ];
        $trade_order_info_dtos->order_info = $order_info;
        //包裹信息
        $package_info = $this->setPackageInfo($order, $objectId);
        $trade_order_info_dtos->package_info = $package_info;
        //收件人信息
        $receiver = $this->setReceiver($order);
        $trade_order_info_dtos->recipient = $receiver;
        //模板url
        $trade_order_info_dtos->template_url="http://cloudprint.cainiao.com/template/standard/101";
        $trade_order_info_dtos->user_id=$userId;

        return $trade_order_info_dtos;
    }

    public function setReceiver($order)
    {
        $oaid = $order['order_cipher_info']['oaid'] ?? '';
        $recipient = new UserInfoDto;
        $recipient->tid = $order['tid']??''; // 自由打印没有 tid
        $recipient->oaid = $oaid;
//        $address = new AddressDto;
//        $address->city=$order['receiver_city']??'';
//        $address->detail=$order['receiver_address']??'';
//        $address->district=$order['receiver_district']??'';
//        $address->province=$order['receiver_state'] ?? $order['receiver_province']??'';
//        $address->town="";
//        @$recipient->address = $address;
//        $recipient->mobile=$order['receiver_phone'];
//        $recipient->name=$order['receiver_name'];
//        $recipient->phone=$order['receiver_tel'] ?? '';
        $recipient->oaid = $oaid;

        return $recipient;
    }
    private function setPackageInfo($order, $objectId)
    {
        $item = [];
        if (array_key_exists('order_item', $order)) {
            foreach ($order['order_item'] as $good) {
                $temp = [];
                $temp['count'] = $good['goods_num'];
                $temp['name']  = $good['goods_title'] ? $good['goods_title'] : '';
                $item       = $temp;
            }
        }
        if (array_key_exists('production_type', $order)) {
            $item = [
                'count' => (!empty($order['num']) && is_int($order['num'])) ? $order['num'] : 1,
                'name'  => !empty($order['goods_info']) ?
                    (json_decode($order['goods_info'], true)[0]['title'] ?? $order['production_type']) : $order['production_type'],
            ];
        }

        $package_info = new PackageInfoDto;
        $package_info->id = $objectId;//包裹id
        $package_info->items = [
            'count' => $item['count'],
            'name'  => $item['name']
        ];
        $package_info->volume="1";
        $package_info->weight="1";
        if (isset($order['order_no'])) {
            $goodsDescription = json_decode($order['goods_info'], true)[0]['title'] ?? $order['production_type'];
        } else {
            $goodsDescription = $order['goods_title'] ?? "";
        }
        //商品描述
        //$package_info->goods_description=$goodsDescription;
        $package_info->goods_description="";


        return $package_info;
    }
}
