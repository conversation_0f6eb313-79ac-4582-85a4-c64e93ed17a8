<?php

use App\Constants\PlatformConst;
use App\Models\Address;
use App\Models\Address as AddressModel;
use App\Models\Fix\Shop;
use App\Models\Order;
use App\Models\Waybill;
use App\Services\Auth\AuthServiceManager;
use App\Services\Client\PddClient;
use App\Services\Logistic\Request\LogisticExceptionRequest;
use App\Utils\AddressUtil;
use Carbon\Carbon;
use Firebase\JWT\JWT;
use FireSoil\TopClient\Facades\TopClient;
use GuzzleHttp\Client;
use GuzzleHttp\Pool;
use GuzzleHttp\Promise;
use GuzzleHttp\Psr7\Request;
use GuzzleHttp\Psr7\Response;
use GuzzleHttp\Exception\RequestException;
use Swoole\Coroutine;
use Swoole\Coroutine\WaitGroup;
use function Swoole\Coroutine\run;

class ExampleTest extends TestCase
{

    /**
     * A basic test example.
     *
     * @return void
     */
    public function testExample()
    {
        $str ='<?xml version=\'1.0\'?><layout id="CUSTOM_AREA" width="72.00" height="52.00" left="0" top="81" splitable="false"><text left="51.06" top="2.38" width="21.69" height="47.09"  style="fontFamily:SimSun;fontSize:9;zIndex:1;fontWeight:bold;"><![CDATA[ <%= _data.remark %> ]]></text><text left="0.00" top="0.00" width="50.26" height="50.26"  style="fontFamily:SimSun;fontSize:9;zIndex:1;fontWeight:bold;"><![CDATA[ <%= _data.contents %> ]]></text><text left="40.21" top="24.34" width="21.16" height="13.23"  style="fontFamily:SimSun;fontSize:30;zIndex:111;fontWeight:bold;"><![CDATA[ <%= _data.watermark %> ]]></text></layout>';
        $outputString = str_replace(['splitable="false"', 'splitable="true"'], 'splitable="123"', $str);

        dd($outputString);
        dd(array_diff([1,2,3], [2,3,6]));
        $updateColumn = ['name'];
        $updateData = [
            1234 => ['name' => '张三'],
        ];
        $whereColumn = 'goods_id';
        dd(\App\Models\Goods::batchUpdateData2('goods',$updateColumn,$updateData,$whereColumn));

        $districtCode = AddressModel::getDistrictCodeAndOther('北京市', '北京市', '朝阳区');

        dd($districtCode);
        $dyClient = new \App\Services\Client\DyClient('3415760853490892224','404ff1753a1654834ed5e0cc2129e38e');
        $param_arr = array(
            "start_time" => date('Y-m-d H:i:s'),
            "end_time" => date('Y-m-d H:i:s'),
            "order_by" => "create_time",
            "size" => "100"
        );
        $arr = $dyClient->execute('order/list',$param_arr);
        dd($arr);
//        $pddClient = new PddClient(1, 'testSecret');
//        $pddClient->setAccessToken('asd78172s8ds9a921j9qqwda12312w1w21211');
//        $pddClient->execute('pdd.order.number.list.get',[
//            'order_status' => '1',
//            'page' => '1',
//            'page_size' => '10',
//        ]);

//        $query = \App\Models\Order::query()->where('a', 1);
//        $query1 = clone $query;
//        $query2 = clone $query;
//        $query1->where('b',2);
//        $query2->where('c',3);
//        dd($query->getQuery()->wheres,$query1->getQuery()->wheres,$query2->getQuery()->wheres);
//        $len = intval((strtotime('2020-03-03 13:29:29') - strtotime('2020-02-27 13:29:29')) / 60 / 43200 + 1);
//
//        dd($len);
//        dd(topClient());
//        socialite()->driver('taobao2')->user();
//        $this->get('/');
//
//        $this->assertEquals(
//            $this->app->version(), $this->response->getContent()
//        );

    }

    public function testAlbbTimeFormat_ValidInput_ReturnsFormattedTime()
    {
        $input = '20250211181351000+0800';
        $expectedOutput = '2025-02-11 18:13:51';
        $actualOutput = \App\Utils\DateTimeUtil::albbTimeFormat($input);
        $this->assertEquals($expectedOutput, $actualOutput);
    }

    public function testAddress()
    {
//        $addrStr = '辽宁省铁岭市昌图县后窑镇';
//        $addressInfo =  AddressUtil::smartParse($addrStr);
//        dd($addressInfo);

        $addrStr = '广西壮族自治区桂林市灵川县江上御都';
        $smartParse = AddressUtil::smartParse($addrStr);
        dd($smartParse);
        $regex = "#(?<province>[^省]+省|.+自治区|[^澳门]+澳门|[^香港]+香港|[^市]+市)?(?<city>[^自治州]+自治州|[^特别行政区]+特别行政区|[^市]+市|.*?地区|.*?行政单位|.+盟|市辖区|[^县]+县)(?<county>[^县]+县|[^市]+市|[^镇]+镇|[^区]+区|[^乡]+乡|.+场|.+旗|.+海域|.+岛)?(?<address>.*)#u";
//        $regex = "#(?<province>[^省]+省|.+自治区|[^澳门]+澳门|[^香港]+香港)?(?<city>[^市]+市|[^自治州]+自治州|[^特别行政区]+特别行政区)?(?<county>[^县]+县|[^市]+市|[^镇]+镇|[^区]+区|[^乡]+乡|.+场|.+旗|.+海域|.+岛)(?<address>.*)#u";

        preg_match($regex, '辽宁省铁岭市昌图县后窑镇', $matches);

        dd($matches['province'], $matches['city'], $matches['county'], $matches['address']);
    }
    public function testJwt()
    {

        $payload = [
            'iss' => 'lumen-jwt',
            'sub' => 1,
            'iat' => time(),
            'shop_id' => 1,
            'identifier' => 1,
            'plaftorm_type' => 0,
            'exp' => time() + 999999
        ];
        $token = JWT::encode($payload, env('JWT_SECRET'));
        redis('cache')->setex('jwt_token:' . $payload['sub'], $payload['exp'], $token);
        dd($token,$payload);
    }

    public function testGuzzle()
    {
        list($msec, $sec) = explode(' ', microtime());
        $time = (float)sprintf('%.0f', (floatval($msec) + floatval($sec)) * 1000);

        $client = new Client();
        $total = 10;
        $data = [];
        for ($i = 0; $i < $total; $i++) {
            $data[$i] = $i;
        }


	    $data = [
		    '12'       => 12,
		    '4332'     => 4332,
		    '434243'   => 434243,
		    '900'      => 900,
		    '7852'     => 7852,
		    '58744414' => 58744414,
		    '587'      => 587,
		    '123'      => 123,
		    '456'       => 456,
		    '789'       => 789,
	    ];

        $keyArr = array_keys($data);

        dd($keyArr);

//	    dd($data);

        $requests = function ($data) {
            foreach ($data as $index => $datum) {
                $uri = 'https://img.alicdn.com/tfs/TB1urCYGbr1gK0jSZR0XXbP8XXa-1190-70.jpg';
                yield new Request('GET', $uri . '?' . $datum);
            }
        };

        $pool = new Pool($client, $requests($data), [
            'concurrency' => 10, //并发数
            'fulfilled' => function (Response $response, $index) use ($data, $keyArr) {
                // 请求成功
//	            echo $index  . PHP_EOL;;

                echo $index .':'. $keyArr[$index] . PHP_EOL;
//                echo $response->getBody()->getContents();
            },
            'rejected' => function (RequestException $reason, $index) {
                // 请求失败
                echo $index . ':' . $reason->getMessage() . PHP_EOL;
            },
        ]);
        // 初始化并创建promise
        $promise = $pool->promise();
        // 等待所有进程完成
        $promise->wait();

        list($msec, $sec) = explode(' ', microtime());
        $time2 = (float)sprintf('%.0f', (floatval($msec) + floatval($sec)) * 1000);

        for ($i = 0; $i < $total; $i++) {
            file_get_contents('https://img.alicdn.com/tfs/TB1urCYGbr1gK0jSZR0XXbP8XXa-1190-70.jpg');
        }
        list($msec, $sec) = explode(' ', microtime());
        $time3 = (float)sprintf('%.0f', (floatval($msec) + floatval($sec)) * 1000);
        echo 'async：';
        echo $time2 - $time;
        echo 'ms';
        echo '----';
        echo 'sync：';
        echo $time3 - $time2;
        echo 'ms';

        dd();
    }

    public function testSwoole()
    {

        run(function () {
            $wg = new WaitGroup();
            $ch = new Coroutine\Channel();
            $errCh = new Coroutine\Channel();

            $result = [];
            go(function() use (&$ch){
                while($obj = $ch->pop()){
//                    Coroutine::sleep(0.3);
                    echo $obj.PHP_EOL;
                }
            });
            for ($j = 1; $j < 10; $j++) {
                $wg->add();
                //启动一个协程
                go(function () use ($wg, &$result, &$ch, $j) {
                    try {
                        $client = new Client();
                        $response = $client->get('https://www.baidu.com');
                        $result[] = $response->getStatusCode();
                        Coroutine::sleep(3);
                        $ch->push($j);
                        $wg->done();
                    }catch (\Exception $exception){
                    }
                    return false;
                });
                echo "$j over".PHP_EOL;
                if ($j % 100 == 0){
                    echo "$j % 2".PHP_EOL;
                    //挂起当前协程，等待所有任务完成后恢复
                    $wg->wait();
                }

            }
            $errCh->push(1);
            $wg->wait();
            $ch->close();
            //这里 $result 包含了 2 个任务执行结果
        });




    }

    public function test()
    {
        $abstractWaybillService = \App\Services\Waybill\WaybillServiceManager::init(PlatformConst::WAYBILL_TB,'62029156ab774f3f887e291ZZ25b37afdf1f1ec29f80ee32418986444');
        $query = $abstractWaybillService->waybillSubscriptionQuery('POSTB');
        dd($query);
    }

    public function testGetWpCodeDistrictList()
    {
        $queryAreaService = new \App\Services\QueryAreaService();
//        $wpCodeDistrictList = $queryAreaService->getWpCodeDistrictList(7334);
        $time = time();
        $wpCodeDistrictList = $queryAreaService->getUnionWpCodeByDistrictCode(7334,1);
        echo time() - $time;
        dd(json_encode($wpCodeDistrictList,JSON_UNESCAPED_UNICODE));
    }

    public function testIsFakeAddress()
    {
        $isFakeAddressX = Order::isFakeAddress('湖北省', '武汉市', '汉阳区', '湖北省武汉市汉阳区四新街道湖北省武汉市汉阳区水湖南路（招商公园**璟**');
        $this->assertFalse($isFakeAddressX);
//        $str = '海南省|内蒙古自治区|青海省|西藏自治区|新疆维吾尔自治区';
//        $arr = explode('|', $str);
//        $list = \App\Models\Address::getRemoteDistrictName();
//        dd(json_encode($list,JSON_UNESCAPED_UNICODE));
        $isFakeAddress = Order::isFakeAddress('陕西省', '西安市', '长安区', '陕西省西安市长安区斗门街道陕西省西安市长安区库尔勒市兴鸿博物流园宾馆前台');
        $isFakeAddress2 = Order::isFakeAddress('陕西省', '西安市', '长安区', '陕西省西安市长安区兴鸿博物流园宾馆前台');
        $isFakeAddress3 = Order::isFakeAddress('陕西省', '西安市', '长安区', '库尔勒市兴鸿博物流园宾馆前台');
        $isFakeAddress4 = Order::isFakeAddress('陕西省', '西安市', '长安区', '长安区新疆兴鸿博物流园宾馆前台');
        $isFakeAddress5 = Order::isFakeAddress('陕西省', '西安市', '长安区', '韦曲街道潏河路西安黑马艺术文化培训学校(南长安街校区)');
        $isFakeAddress6 = Order::isFakeAddress('陕西省', '嘉峪关市', '嘉峪关市辖区', '新疆哈密三道岭国道金顺饭店');
        $isFakeAddress7 = Order::isFakeAddress('陕西省', '嘉峪关市', '嘉峪关市辖区', '北京路***号');
        $isFakeAddress8 = Order::isFakeAddress('陕西省', '嘉峪关市', '嘉峪关市辖区', '北京*路*号');
        $isFakeAddress9 = Order::isFakeAddress('陕西省', '嘉峪关市', '嘉峪关市辖区', '陕西***号');
        $this->assertTrue($isFakeAddress);
        $this->assertFalse($isFakeAddress2);
        $this->assertTrue($isFakeAddress3);
        $this->assertTrue($isFakeAddress4);
        $this->assertFalse($isFakeAddress5);
        $this->assertTrue($isFakeAddress6);
        $this->assertFalse($isFakeAddress7);
//        $this->assertTrue($isFakeAddress8);
        $this->assertFalse($isFakeAddress9);


    }

    public function  testDecode(){
       var_dump(base64_decode('3v70s005e68ga1lsj5uqko4v58'));
    }
    public function testJson(){
        $api=[
            "tid"=>"1707779209398304474",
            "memo"=>"test",
            "flag"=>"1"
        ];
        var_dump(json_encode($api));
    }

    public function testInArray()
    {
        $this->assertTrue(in_array(0,['Abc']));
        $this->assertFalse(in_array('0',['Abc']));
        $this->assertTrue(in_array('1',[0,1]));
        $this->assertTrue(in_array('0',[0,1]));
        $this->assertTrue(in_array('0',[1]));
        $this->assertTrue(in_array('0',['1']));
    }

    public function testHandleLogisticsData()
    {
        $shippedOrderIdArr = [26763426];

        Order::handleLogisticsData($shippedOrderIdArr);
    }

}
