<?php
/**
 * TOP API: taobao.rdc.aligenius.warehouse.resend.update request
 * 
 * <AUTHOR> create
 * @since 1.0, 2018.09.26
 */
class RdcAligeniusWarehouseResendUpdateRequest
{
	/** 
	 * 参数
	 **/
	private $param0;
	
	private $apiParas = array();
	
	public function setParam0($param0)
	{
		$this->param0 = $param0;
		$this->apiParas["param0"] = $param0;
	}

	public function getParam0()
	{
		return $this->param0;
	}

	public function getApiMethodName()
	{
		return "taobao.rdc.aligenius.warehouse.resend.update";
	}
	
	public function getApiParas()
	{
		return $this->apiParas;
	}
	
	public function check()
	{
		
	}
	
	public function putOtherTextParam($key, $value) {
		$this->apiParas[$key] = $value;
		$this->$key = $value;
	}
}
