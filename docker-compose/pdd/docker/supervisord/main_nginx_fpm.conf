[program:php-fpm]
command=/bin/bash -c '/usr/sbin/php-fpm -F -O'
autostart=true
autorestart=true
priority=5
stdout_logfile=/var/log/supervisor/php-fpm.log
stopsignal=QUIT


[program:nginx]
command=/bin/bash -c 'envsubst "\$SERVER_PORT \$CODE_PATH"  < /usr/local/nginx/conf/nginx.template > /usr/local/nginx/conf/nginx.conf && /usr/local/sbin/nginx -g "daemon off;"'
stdout_logfile=/var/log/supervisor/cron.log

