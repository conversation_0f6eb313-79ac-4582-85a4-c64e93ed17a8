FROM centos:7.5.1804

RUN yum install -y wget epel-release \
    && sed -e 's|^mirrorlist=|#mirrorlist=|g' \
           -e 's|^#baseurl=http://mirror.centos.org/centos|baseurl=https://mirrors.tuna.tsinghua.edu.cn/centos|g' \
           -i.bak \
           /etc/yum.repos.d/CentOS-*.repo \
    && yum clean all \
    && yum makecache \
    && yum install -y python-setuptools python-pip \
    && /usr/bin/pip install -i https://repo.huaweicloud.com/repository/pypi/simple supervisor \
    && groupadd apps \
    && useradd -g apps apps

# pdd 分界线

#COPY docker /tmp/docker/
ADD docker.tar.gz /tmp/

RUN yum install -y --enablerepo=updates \
        epel-release \
        unzip \
        which \
        curl \
        curl-devel \
        make  \
        wget \
        gcc  \
        gcc-c++  \
        gettext \
        net-tools \
        python-setuptools \
        openssl \
        openssl-devel \
        bzip2-devel \
        zlib \
        zlib-devel \
        zip \
        unzip \
        yum-utils \
        libpng \
        libjpeg \
        libpng-devel \
        libjpeg-devel \
        ghostscript \
        libtiff \
        libtiff-devel \
        freetype \
        freetype-devel \
        autoconf \
        centos-release-scl \
#        crontabs \
    # 这 devtoolset-7 一定要分开
    && yum install -y devtoolset-7 \
    && yum install -y http://rpms.remirepo.net/enterprise/remi-release-7.rpm \
    && find /etc/yum.repos.d/ -name "remi*.repo" | xargs sed -i "s/#baseurl/baseurl/g" \
    && find /etc/yum.repos.d/ -name "remi*.repo" | xargs sed -i "s/mirrorlist/#mirrorlist/g" \
    && find /etc/yum.repos.d/ -name "remi*.repo" | xargs sed -i "s@http://rpms.remirepo.net@https://mirrors.tuna.tsinghua.edu.cn/remi@g" \
    && yum makecache \
    && yum-config-manager --enable remi-php73 \
    && yum install -y php php-devel php-fpm php-cli php-bcmath php-gd php-json php-mbstring php-mcrypt \
            php-mysqlnd php-opcache php-process php-sodium php-pdo php-pecl-crypto php-pecl-mcrypt php-pecl-geoip php-pear.noarch \
            php-recode php-snmp php-soap php-xmll php-calendar php-core php-ctype php-curl php-date php-dom \
            php-exif php-fileinfo php-filter php-ftp php-gettext php-hash php-iconv php-intl php-ldap php-libxml \
            php-pecl-memcached php-pecl-redis php-mysqli php-mysqlnd php-openssl php-pcntl php-pcre php-pdo \
            php-pdo_mysql php-pdo_sqlite php-phar php-posix php-reflection php-session php-simpleXML php-spl \
            php-sqlite3 php-standard php-tokenizer php-xml php-zip php-zlib php-xmlreader php-xmlwriter php-yac php-pecl-gmagick \
    && mkdir -p /app/conf \
    # install swoole
    && echo "source /opt/rh/devtoolset-7/enable" >> /root/.bashrc  \
    && source ~/.bashrc \
    && pecl install -D 'enable-sockets="no" enable-openssl="yes" enable-http2="yes" enable-mysqlnd="no" enable-swoole-json="yes" enable-swoole-curl="yes"' /tmp/docker/software/swoole-4.6.7.tgz \
    && echo "extension=swoole.so" > /etc/php.d/30-swoole.ini \
    # install nginx
    && cd /tmp/docker/software \
    && tar zxvf nginx-1.14.0.tar.gz \
    && cd nginx-1.14.0 \
    && ./configure --prefix=/usr/local/nginx --user=apps --group=apps --with-http_stub_status_module --with-http_ssl_module --with-http_gzip_static_module \
    && make \
    && make install \
    && rm -rf nginx-1.14.0.tar.gz \
    # config
    && ln -s /usr/local/nginx/sbin/* /usr/local/sbin/ \
    && mkdir -p /app /usr/local/supervisor/conf.d /var/log/cron /var/log/supervisor \
    && chown -R apps:apps /app /usr/local/supervisor/conf.d /usr/local/nginx /var/log/php-fpm /var/log/cron /var/log/supervisor \
    && chmod 777 /var/run \
    && chmod 777 /var/log \
    && mkfifo /var/log/stdout \
    && chmod 777 /var/log/stdout \
    && cp /tmp/docker/supervisord/main.conf /usr/local/supervisor/conf.d \
    && cp /tmp/docker/php/* /app/conf \
    && cp /tmp/docker/nginx/nginx.conf /usr/local/nginx/conf/nginx.template \
    && ln -sf /app/conf/php.ini /etc/php.ini \
    && ln -sf /app/conf/php-fpm.conf /etc/php-fpm.conf \
    && ln -sf /app/conf/www.conf /etc/php-fpm.d/www.conf \
    # cron
    && cp /tmp/docker/crontabs/default /app/conf/cron.conf \
#    && cp /tmp/docker/software/supercronic-v0.1.12 /usr/bin/supercronic \
#    && chmod +x /usr/bin/supercronic
#    && chmod 0644 /etc/cron.d/*  \
#    && crontab /etc/cron.d/default

# pdd 分界线
#COPY docker /tmp/docker/
RUN cp /tmp/docker/supervisord/main.conf /usr/local/supervisor/conf.d \
    && cp /tmp/docker/php/* /app/conf \
    && cp /tmp/docker/nginx/nginx.conf /usr/local/nginx/conf/nginx.template \
    && ln -sf /app/conf/php.ini /etc/php.ini \
    && ln -sf /app/conf/php-fpm.conf /etc/php-fpm.conf \
    && ln -sf /app/conf/www.conf /etc/php-fpm.d/www.conf \
    && cp /tmp/docker/crontabs/default /app/conf/cron.conf \
    && cp /tmp/docker/supervisord/supervisord.conf /etc/ \
#    && cp /tmp/docker/entrypoint.sh /tmp/docker/ \
    && cp /tmp/docker/supervisord/main.conf /usr/local/supervisor/conf.d/ \
    && cp /tmp/docker/entrypoint.sh /app/ \
#COPY docker/supervisord/supervisord.conf /etc/
#COPY docker/entrypoint.sh /tmp/docker/
#COPY * /app/
#COPY docker/supervisord/main.conf /usr/local/supervisor/conf.d/
    && chmod +x /app/entrypoint.sh \
    && cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
    && echo Asia/Shanghai > /etc/timezone \
    && yum clean all \
    && rm -rf /var/cache/yum/*

ENV PHP_ENV_FILE pro_tb.env
ENV SERVER_PORT 8080
ENV CODE_PATH /app/code

#USER apps
WORKDIR /app/code
ENTRYPOINT ["/app/entrypoint.sh"]
CMD ["start"]
